!function(){"use strict";var e={n:function(t){var a=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(a,{a:a}),a},d:function(t,a){for(var l in a)e.o(a,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:a[l]})},o:function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}},t=window.React,a=window.wp.i18n,l=window.wp.blocks,r=window.wp.blockEditor,o=(0,t.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 242.5 239.46"},(0,t.createElement)("defs",null,(0,t.createElement)("clipPath",{id:"clip-path",transform:"translate(1.72)"},(0,t.createElement)("circle",{className:"cls-1",cx:"119.73",cy:"119.73",r:"116.15",fill:"none"}))),(0,t.createElement)("g",{id:"Layer_2","data-name":"Layer 2"},(0,t.createElement)("g",{id:"Layer_1","data-name":"Layer 1"},(0,t.createElement)("g",{className:"cls-2",clipPath:"url(#clip-path)"},(0,t.createElement)("circle",{className:"cls-3",cx:"121.45",cy:"119.73",r:"116.15",fill:"#33c6f4"}),(0,t.createElement)("path",{className:"cls-4",d:"M239.32,167.79c-53.41-24-108.37-91.46-113-94.55s-10.84.77-10.84.77c-3.87-6.19-10.06.77-10.06.77C76.77,123.55.14,170.11.14,170.11S36.94,237.79,122,237.79C208.48,237.79,239.32,167.79,239.32,167.79Z",transform:"translate(1.72)",fill:"#1b447e"}),(0,t.createElement)("path",{className:"cls-5",d:"M67.48,116.58s15.48-7,12.38,4.65-15.48,28.64-11.61,29.41S83,140.58,86.06,142.12s5.42.78,3.87,6.2-3.1,9.29,0,9.29,5.42-7,9.29-13.94,10.06-3.87,12.38-1.55,9.29,15.49,14.71,13.94,8.51-8.52,6.19-24,1.55-20.12,1.55-20.12,4.64-2.32,13.16,8.51,24,27.09,26.31,26.32-10.83-17.8-7.74-19.35,15.48,2.32,21.68,7.74c0,0,2.12,8.87,2.12.36L126.31,73.24,115.47,74l-10.06.77S80.64,111.94,67.48,116.58Z",transform:"translate(1.72)",fill:"#fff"}),(0,t.createElement)("path",{className:"cls-6",d:"M239.32,170.11c-53.41-24-108.37-93.78-113-96.87s-10.84.77-10.84.77c-3.87-6.19-10.06.77-10.06.77C76.77,123.55.14,170.11.14,170.11",transform:"translate(1.72)",fill:"none",stroke:"#221e1f",strokeMiterlimit:"10",strokeWidth:"8px"})),(0,t.createElement)("circle",{className:"cls-6",cx:"121.45",cy:"119.73",r:"116.15",fill:"none",stroke:"#1b447e",strokeMiterlimit:"10",strokeWidth:"8px"})))),n=window.wp.element,c=window.wp.components,s=window.wp.apiFetch,m=e.n(s),i=window.wp.url;const p=e=>{let t="[contact-form-7]";return e.hash?t=t.replace(/\]$/,` id="${e.hash}"]`):e.id&&(t=t.replace(/\]$/,` id="${e.id}"]`)),e.title&&(t=t.replace(/\]$/,` title="${e.title}"]`)),e.htmlId&&(t=t.replace(/\]$/,` html_id="${e.htmlId}"]`)),e.htmlName&&(t=t.replace(/\]$/,` html_name="${e.htmlName}"]`)),e.htmlTitle&&(t=t.replace(/\]$/,` html_title="${e.htmlTitle}"]`)),e.htmlClass&&(t=t.replace(/\]$/,` html_class="${e.htmlClass}"]`)),"raw_form"===e.output&&(t=t.replace(/\]$/,` output="${e.output}"]`)),t},d=e=>{const t=ajaxurl.replace(/\/admin-ajax\.php$/,"/admin.php");return(0,i.addQueryArgs)(t,{page:"wpcf7",post:e.id,action:"edit"})};var h,f={from:[],to:[{type:"block",blocks:["core/shortcode"],transform:e=>{const t=p(e);return(0,l.createBlock)("core/shortcode",{text:t})}}]};window.wpcf7=null!==(h=window.wpcf7)&&void 0!==h?h:{contactForms:[]},(0,l.registerBlockType)("contact-form-7/contact-form-selector",{icon:o,transforms:f,edit:function({attributes:e,setAttributes:l}){const o=e=>e.reduce(((e,t)=>e.set(t.id,t)),new Map),[s,p]=(0,n.useState)((()=>{var e;return o(null!==(e=window.wpcf7.contactForms)&&void 0!==e?e:[])}));return(0,t.createElement)(t.Fragment,null,(0,t.createElement)(r.InspectorControls,null,e.id&&(0,t.createElement)(c.PanelBody,{title:e.title},(0,t.createElement)(c.ExternalLink,{href:d(e)},(0,a.__)("Edit this contact form","contact-form-7"))),e.id&&(0,t.createElement)(c.PanelBody,{title:(0,a.__)("Form attributes","contact-form-7"),initialOpen:!1},(0,t.createElement)(c.TextControl,{label:(0,a.__)("ID","contact-form-7"),value:e.htmlId,onChange:e=>l({htmlId:e}),help:(0,a.__)("Used for the id attribute value of the form element.","contact-form-7")}),(0,t.createElement)(c.TextControl,{label:(0,a.__)("Name","contact-form-7"),value:e.htmlName,onChange:e=>l({htmlName:e}),help:(0,a.__)("Used for the name attribute value of the form element.","contact-form-7")}),(0,t.createElement)(c.TextControl,{label:(0,a.__)("Title","contact-form-7"),value:e.htmlTitle,onChange:e=>l({htmlTitle:e}),help:(0,a.__)("Used for the aria-label attribute value of the form element.","contact-form-7")}),(0,t.createElement)(c.TextControl,{label:(0,a.__)("Class","contact-form-7"),value:e.htmlClass,onChange:e=>l({htmlClass:e}),help:(0,a.__)("Used for the class attribute value of the form element.","contact-form-7")}))),(0,t.createElement)("div",{...(0,r.useBlockProps)({className:"components-placeholder",style:{marginTop:"28px",marginBottom:"28px"}})},(0,t.createElement)(c.ComboboxControl,{label:(0,a.__)("Select a contact form:","contact-form-7"),options:(e=>{const t=[];for(const[a,l]of e)t.push({value:a,label:l.title});return t})(s),value:e.id,onChange:e=>l({id:parseInt(e),hash:s.get(parseInt(e))?.hash,title:s.get(parseInt(e))?.title}),onFilterValueChange:e=>{(async e=>m()({path:(0,i.addQueryArgs)("/contact-form-7/v1/contact-forms",{posts_per_page:20,orderby:"modified",order:"DESC",...e})}).then((e=>e)))({search:e}).then((e=>{p(o(e))}))}})))},save:({attributes:e})=>{const a=p(e);return(0,t.createElement)("div",{...r.useBlockProps.save()},a)}})}();