(self.webpackJSONPwpmdb=self.webpackJSONPwpmdb||[]).push([[179],{51286:function(e,t,n){"use strict";n.d(t,{r:function(){return s}});var r,a=n(4665);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}var s=function(e){return a.createElement("svg",i({width:12,height:10,viewBox:"0 0 12 7",xmlns:"http://www.w3.org/2000/svg"},e),r||(r=a.createElement("g",{id:"Symbols",stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd"},a.createElement("g",{id:"accordion_collapsed_default_active",transform:"translate(-1188 -25)",fill:"#000",fillRule:"nonzero"},a.createElement("g",{id:"arrow_down",transform:"translate(1188 25)"},a.createElement("path",{d:"m3.16 3.893 4.9 4.945a.548.548 0 0 0 .779 0 .558.558 0 0 0 0-.785L4.327 3.5l4.511-4.552a.559.559 0 0 0 0-.786.548.548 0 0 0-.779 0l-4.9 4.945a.564.564 0 0 0 0 .786Z",id:"arrow",transform:"rotate(-90 6 3.5)"}))))))};n.p},73042:function(e,t,n){"use strict";n.d(t,{r:function(){return s}});var r,a=n(4665);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}var s=function(e){return a.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 16 16"},e),r||(r=a.createElement("path",{fill:"#999",d:"m9.188 8 3.566-3.565a.84.84 0 1 0-1.189-1.189L8 6.812 4.435 3.246a.84.84 0 1 0-1.189 1.189L6.812 8l-3.566 3.565a.84.84 0 0 0 1.189 1.189L8 9.188l3.565 3.566a.84.84 0 1 0 1.189-1.189L9.188 8Z"})))};n.p},9712:function(e,t,n){"use strict";n.d(t,{r:function(){return s}});var r,a=n(4665);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}var s=function(e){return a.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24"},e),r||(r=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{cx:12,cy:12,r:8,fill:"#999"}),a.createElement("path",{fill:"#EEE",fillRule:"nonzero",d:"M12.75 15h-1.5a.24.24 0 0 0-.175.075.24.24 0 0 0-.075.175v1.5a.24.24 0 0 0 .075.175.24.24 0 0 0 .175.075h1.5a.24.24 0 0 0 .175-.075.24.24 0 0 0 .075-.175v-1.5a.24.24 0 0 0-.075-.175.24.24 0 0 0-.175-.075zm1.984-5.383a2.758 2.758 0 0 0-.688-.854 3.493 3.493 0 0 0-.948-.555A2.832 2.832 0 0 0 12.052 8c-1.316 0-2.32.594-3.012 1.781a.271.271 0 0 0 .065.355l1.064.837c.057.036.11.054.163.054a.25.25 0 0 0 .201-.107c.312-.402.543-.659.695-.77.186-.13.42-.195.7-.195.265 0 .498.072.699.215.201.142.302.308.302.495 0 .21-.054.38-.163.51-.108.128-.29.253-.545.374a2.752 2.752 0 0 0-.928.727c-.282.328-.293.674-.293 1.044v.302c0 .085.023.168.068.25.046.084.1.125.166.125L12.74 14c.07 0 .13-.035.182-.104a.357.357 0 0 0 .078-.217c0-.112-.018-.262.1-.449a1.29 1.29 0 0 1 .446-.435c.168-.099.3-.178.396-.238a3.3 3.3 0 0 0 .373-.291c.154-.134.273-.267.357-.399.084-.132.16-.302.227-.512.068-.21.101-.436.101-.677 0-.37-.089-.724-.266-1.06z"}))))};n.p},69479:function(e,t,n){"use strict";n.d(t,{r:function(){return s}});var r,a=n(4665);function i(){return i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i.apply(this,arguments)}var s=function(e){return a.createElement("svg",i({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 16 16"},e),r||(r=a.createElement("path",{fill:"#999",d:"m3.562 7.245-.015.003h6.691l-2.104-2.03a.517.517 0 0 1 0-.749l.328-.315c.103-.1.24-.154.386-.154.146 0 .284.054.387.154l3.606 3.472c.103.1.16.232.159.373a.512.512 0 0 1-.16.374l-3.605 3.473a.554.554 0 0 1-.387.154.554.554 0 0 1-.386-.154l-.328-.315a.512.512 0 0 1-.159-.372c0-.141.056-.266.16-.365l2.127-2.042H3.555A.556.556 0 0 1 3 8.212v-.447c0-.29.26-.52.562-.52Z"})))};n.p},49275:function(e,t,n){"use strict";var r=n(4665),a=n(80401);t.Z=function(e){var t=e.link,n=e.content,i=e.utmContent,s=e.utmCampaign,o=e.anchorLink,l=e.hasArrow,c=void 0===l||l;return r.createElement(a.Z,{link:t,content:n,utmContent:i,utmCampaign:s,classes:"docs-link",hasArrow:c,anchorLink:o})}},80401:function(e,t,n){"use strict";var r=n(4665),a=n(29942);t.Z=function(e){var t=e.link,n=e.classes,i=e.content,s=e.utmContent,o=e.utmCampaign,l=e.hasArrow,c=e.screenReaderText,u=e.anchorLink,p={source:(0,a.Yu)()?"wp-migrate-pro":"wp-migrate-lite",medium:"plugin",campaign:o,content:s},d=function(){var e=[];for(var t in p)p.hasOwnProperty(t)&&e.push("utm_".concat(t,"=").concat(p[t]));return!!e.length&&e.join("&")};return r.createElement("a",{className:n,href:function(){var e=t;return d()&&(e+="?"+d()),u&&(e+="#"+u),e}(),target:"__blank",rel:"NOFOLLOW"},i," ",c&&r.createElement("span",{className:"screen-reader-text"},c),l&&r.createElement("span",{className:"open-arrow"},"\u2192"))}},49736:function(e,t,n){"use strict";n.d(t,{B:function(){return a}});var r=n(42233),a={tables:{all:(0,r.__)("All tables","wp-migrate-db"),selected:(0,r.__)("Selected tables","wp-migrate-db")},backups:{none:(0,r.__)("No backup","wp-migrate-db"),all:(0,r.__)("Backup all tables","wp-migrate-db"),migration:(0,r.__)("Backup tables selected for migration","wp-migrate-db"),selected:(0,r.__)("Backup selected tables","wp-migrate-db")},post_types:{all:(0,r.__)("All post types","wp-migrate-db"),selected:(0,r.__)("Selected post types","wp-migrate-db")}}},91828:function(e,t,n){"use strict";n.d(t,{B:function(){return a}});var r=n(42233),a={push:(0,r.__)("Push","wp-migrate-db"),pull:(0,r.__)("Pull","wp-migrate-db"),backup_local:(0,r.__)("Backup Database","wp-migrate-db"),import:(0,r.__)("Import Database","wp-migrate-db"),find_replace:(0,r.__)("Find & Replace","wp-migrate-db"),savefile:(0,r.__)("Export Database","wp-migrate-db")}},86645:function(e,t,n){"use strict";n.d(t,{O:function(){return l}});var r=n(4665),a=n(62295),i=n(42233),s=n(4516),o=n(29942),l=function(e){return(e?e.reduce((function(e,t){return e+t}),0):0)/(e&&e.length>0?e.length:1)};t.Z=function(){var e=(0,a.v9)((function(e){return e})),t=(0,s.r5)("intent",e),n=(0,s.r5)("current_stage",e),c=(0,s.r5)("currentPayloadSize",e),u=(0,s.r5)("currentMaxPayloadSize",e),p=(0,s.r5)("payloadSizeHistory",e),d=(0,s.r5)("fileTransferRequests",e);return["push","pull"].includes(t)&&["media_files","theme_files","plugin_files","muplugin_files","other_files"].includes(n)?r.createElement("dl",{className:"fs-stats-container flex flex-row"},r.createElement("dt",{className:"fs-stats"},r.createElement("span",null,(0,i.__)("Max Request Size","wp-migrate-db")),r.createElement("span",{className:"fs-stat-value"},(0,o.lL)(null!==u&&void 0!==u?u:0))),r.createElement("dt",{className:"fs-stats"},r.createElement("span",null,(0,i.__)("Actual Request Size","wp-migrate-db")),r.createElement("span",{className:"fs-stat-value"},(0,o.lL)(null!==c&&void 0!==c?c:0))),r.createElement("dt",{className:"fs-stats"},r.createElement("span",null,(0,i.__)("Average Request Size","wp-migrate-db")),r.createElement("span",{className:"fs-stat-value"},(0,o.lL)(l(p)))),r.createElement("dt",{className:"fs-stats"},r.createElement("span",null,(0,i.__)("Total Requests","wp-migrate-db")),r.createElement("span",{className:"fs-stat-value"},null!==d&&void 0!==d?d:0))):null}},42222:function(e,t,n){"use strict";n.d(t,{$s:function(){return u},TJ:function(){return i},UJ:function(){return a},er:function(){return l},i2:function(){return o},q5:function(){return c}});var r=n(42233),a=function(e){if(e>=1e6){var t=e/1e6;return(Math.round(100*t)/100).toFixed(2)}if(e>=1e3){var n=e/1e3;return(Math.round(100*n)/100).toFixed(2)}return e<1?(1e3*e).toFixed(2):parseFloat(e).toFixed(2)},i=function(e){return e>=1e6?"GB":e>=1e3?"MB":e<1?"B":"KB"},s=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=Math.floor(e/t)%60,a=r.toString();return n&&(a="0"+a),r>=10?a.slice(-2):a.toString()},o=function(e){return s(e,1e3,arguments.length>1&&void 0!==arguments[1]&&arguments[1])},l=function(e){return s(e,6e4,arguments.length>1&&void 0!==arguments[1]&&arguments[1])},c=function(e){var t=(e/1e3).toFixed(2),n=l(e/1e3),a=(0,r.gB)("%s %s%s",t,(0,r.__)("sec","wp-migrate-db"),t>1?"s":""),i=(0,r.gB)("%s %s%s",n,(0,r.__)("min","wp-migrate-db"),n>1?"s":"");return(0,r.gB)("%s%s",n>0?i:""," ".concat(a))},u=function(e){return Math.floor(e/36e5)}},81294:function(e,t,n){"use strict";function r(e,t,n,r){return"selected"===n.tables_option?n.tables_selected:"pull"===r?t.prefixed_tables:e.this_prefixed_tables}n.d(t,{O:function(){return r}})},29214:function(e,t,n){"use strict";n.d(t,{Z:function(){return x}});var r,a,i,s=n(4665),o=n(42233);function l(){return l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l.apply(this,arguments)}var c,u,p=function(e){return s.createElement("svg",l({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),r||(r=s.createElement("path",{d:"M15.838 13.852 9.27.79C8.904.071 8.05-.208 7.366.166a1.38 1.38 0 0 0-.6.616L.165 13.867c-.366.71-.098 1.598.593 1.972.197.11.423.161.649.161h13.183c.776 0 1.411-.66 1.411-1.466a1.49 1.49 0 0 0-.162-.682Z",fill:"#FFB92B"})),a||(a=s.createElement("rect",{x:7,y:5,width:2,height:6,rx:1,fill:"#fff"})),i||(i=s.createElement("circle",{cx:8,cy:13,r:1,fill:"#fff"})))};n.p;function d(){return d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},d.apply(this,arguments)}var m,f,_=function(e){return s.createElement("svg",d({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),c||(c=s.createElement("path",{d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Z",fill:"#46B450"})),u||(u=s.createElement("path",{d:"M7.95 11.247a1.01 1.01 0 0 1-1.433 0l-2.22-2.234a1.024 1.024 0 0 1 0-1.441 1.01 1.01 0 0 1 1.433 0l1.323 1.33c.1.1.262.1.362 0L10.997 5.3a1.01 1.01 0 0 1 1.434 0 1.023 1.023 0 0 1 0 1.441l-4.48 4.507Z",fill:"#fff"})))};n.p;function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},g.apply(this,arguments)}var b,h,v,E=function(e){return s.createElement("svg",g({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),m||(m=s.createElement("path",{d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Z",fill:"#0968FF"})),f||(f=s.createElement("path",{d:"M9.026 4.111c0-.67-.549-1.211-1.211-1.211a1.21 1.21 0 0 0-1.211 1.211c0 .662.54 1.211 1.21 1.211a1.22 1.22 0 0 0 1.212-1.21ZM6.519 13.1H9.48c.463 0 .841-.378.841-.84 0-.471-.379-.841-.84-.841h-.556a.088.088 0 0 1-.085-.086V7.815c0-.878-.712-1.582-1.582-1.582h-.74c-.47 0-.841.371-.841.841 0 .462.37.84.84.84h.556c.044 0 .085.04.085.086v3.333c0 .018-.008.04-.027.058a.086.086 0 0 1-.058.027H6.52c-.47 0-.841.371-.841.841 0 .462.37.841.84.841Z",fill:"#fff",stroke:"#fff",strokeWidth:.2})))};n.p;function w(){return w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},w.apply(this,arguments)}var y=function(e){return s.createElement("svg",w({width:16,height:16,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),b||(b=s.createElement("path",{d:"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Z",fill:"#DC3232"})),h||(h=s.createElement("rect",{x:7,y:3.5,width:2,height:6,rx:1,fill:"#fff"})),v||(v=s.createElement("circle",{cx:8,cy:11.5,r:1,fill:"#fff"})))},x=(n.p,n(99816),function(e){var t=e.type,n=e.children,r={warning:p,success:_,info:E,danger:y},a={warning:(0,o.__)("Warning","wp-migrate-db"),success:(0,o.__)("Success","wp-migrate-db"),info:(0,o.__)("Information","wp-migrate-db"),danger:(0,o.__)("Error","wp-migrate-db")},i=r[t],l="migrate-notice ".concat(t);return s.createElement("div",{className:l},s.createElement(i,{"aria-label":a[t]}),s.createElement("div",{className:"migrate-notice-content"},n))})},46415:function(e,t,n){"use strict";n.d(t,{Uj:function(){return g},OP:function(){return _},X2:function(){return b}});var r,a,i=n(79043),s=n(4665),o=n(42233),l=n(96480),c=n.n(l);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}var p=function(e){return s.createElement("svg",u({xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",width:24,height:24,viewBox:"0 0 24 24"},e),r||(r=s.createElement("defs",null,s.createElement("circle",{id:"license-unchecked-a",cx:8,cy:8,r:8}))),a||(a=s.createElement("g",{fill:"none",fillRule:"evenodd",transform:"translate(4 4)"},s.createElement("use",{fill:"#dc3232",xlinkHref:"#license-unchecked-a"}),s.createElement("g",{fill:"#FFF",transform:"translate(4 4)"},s.createElement("rect",{width:2,height:8,x:3,rx:1,transform:"rotate(-45 4 4)"}),s.createElement("rect",{width:2,height:8,x:3,rx:1,transform:"rotate(-135 4 4)"})))))},d=(n.p,n(15925)),m=n(19085);function f(e,t){return e?{left:e.width+t}:{}}var _=function(e){var t=e.position,n=e.condition,r=e.errorMsg,a=e.spinnerCond;return s.createElement(s.Fragment,null,a&&s.createElement("div",{className:"relative"},s.createElement(m.Q,{className:"settings-spinner"})),s.createElement(d.u,{in:"success"===n},s.createElement(b,(0,i.Z)({locationInfo:t,classNames:"toggle-success"},e))),s.createElement(d.u,{in:"errored"===n},s.createElement(g,(0,i.Z)({error:r,locationInfo:t,classNames:"toggle-error"},e))))},g=function(e){var t=e.offset||12,n=f(e.locationInfo,t);return s.createElement("div",{className:"settings-tooltip ".concat(e.classNames?e.classNames:""),style:n},s.createElement("div",{className:"tooltip-saved flex-container",key:"ajax-error-".concat(c()())},s.createElement(s.Fragment,null,s.createElement(p,null)," ",e.error)))},b=function(e){var t=e.offset||12,n=f(e.locationInfo,t);return s.createElement("div",{className:"settings-tooltip ".concat(e.classNames?e.classNames:""),style:n},s.createElement("div",{className:"tooltip-saved flex-container",key:"ajax-error-".concat(c()())},s.createElement(s.Fragment,null,s.createElement(m.en,null)," ",s.createElement("div",null,(0,o.__)("Saved","wp-migrate-db")))))}},15925:function(e,t,n){"use strict";n.d(t,{u:function(){return o}});var r=n(4665),a=n(91490),i=n(96480),s=n.n(i),o=function(e){return r.createElement("div",{className:"relative"},r.createElement(a.Z,{in:e.in,timeout:e.timeout||500,classNames:"settings-node",unmountOnExit:!0,id:s()()},e.children))}},27114:function(e,t,n){"use strict";n.d(t,{Y:function(){return a}});var r=n(42233);function a(e){var t="";return"string"===typeof e.error_message?t=e.error_message:"object"===typeof e.error_message&&"string"===typeof e.error_message.message?t=e.error_message.message:"string"===typeof e?t=e:"string"===typeof e.data?t=e.data:"string"===typeof e.message?t=e.message.message:"undefined"!==typeof e.wpmdb_error&&(t=e.body),""!==t?t:(0,r.__)("An unknown error occurred. Please check your PHP error log or contact support.","wp-migrate-db")}},78677:function(e,t,n){"use strict";n.d(t,{Z:function(){return B}});var r,a,i=n(17186),s=n(4665),o=n(75338),l=n(42233),c=n(30348),u=n(96480),p=n.n(u),d=n(29942),m=n(63708);function f(){return f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},f.apply(this,arguments)}var _,g,b=function(e){return s.createElement("svg",f({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),r||(r=s.createElement("circle",{cx:6,cy:6,r:6,fill:"gray"})),a||(a=s.createElement("path",{d:"M5.538 6.138c.304-.549.888-.872 1.228-1.358.359-.509.158-1.46-.86-1.46-.668 0-.995.505-1.134.923l-1.022-.43c.28-.84 1.042-1.563 2.151-1.563.928 0 1.563.422 1.887.951.276.454.438 1.303.012 1.935-.474.698-.928.911-1.172 1.361-.1.182-.139.3-.139.885H5.35c-.004-.308-.052-.81.19-1.244Zm1.157 2.823c0 .434-.356.789-.79.789a.792.792 0 0 1-.79-.79c0-.434.356-.789.79-.789.434 0 .79.355.79.79Z",fill:"#fff"})))};n.p;function h(){return h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},h.apply(this,arguments)}var v,E,w=function(e){return s.createElement("svg",h({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),_||(_=s.createElement("circle",{cx:6,cy:6,r:6,fill:"#46B450"})),g||(g=s.createElement("path",{d:"M9.75 6.536H6.536V9.75H5.464V6.536H2.25V5.464h3.214V2.25h1.072v3.214H9.75v1.072Z",fill:"#fff"})))};n.p;function y(){return y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},y.apply(this,arguments)}var x,k,Z=function(e){return s.createElement("svg",y({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),v||(v=s.createElement("circle",{cx:6,cy:6,r:6,fill:"#46B450"})),E||(E=s.createElement("path",{d:"M6.5 9.5V3.915l2.44 2.44a.504.504 0 0 0 .71 0 .498.498 0 0 0 0-.705L6.355 2.355a.498.498 0 0 0-.705 0l-3.3 3.29a.498.498 0 1 0 .705.705L5.5 3.915V9.5c0 .275.225.5.5.5s.5-.225.5-.5Z",fill:"#fff"})))};n.p;function T(){return T=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},T.apply(this,arguments)}var S,N,P=function(e){return s.createElement("svg",T({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),x||(x=s.createElement("circle",{cx:6,cy:6,r:6,fill:"#DC3232"})),k||(k=s.createElement("path",{d:"M5.5 2.5v5.585l-2.44-2.44a.504.504 0 0 0-.71 0 .498.498 0 0 0 0 .705l3.295 3.295c.**************.705 0L9.645 6.35a.498.498 0 1 0-.705-.705L6.5 8.085V2.5c0-.275-.225-.5-.5-.5s-.5.225-.5.5Z",fill:"#fff"})))};n.p;function O(){return O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},O.apply(this,arguments)}var A,C=function(e){return s.createElement("svg",O({width:12,height:12,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),S||(S=s.createElement("circle",{cx:6,cy:6,r:6,fill:"gray"})),N||(N=s.createElement("path",{d:"M2.5 6.5h5.585l-2.44 2.44a.504.504 0 0 0 0 .71c.**************.705 0l3.295-3.295a.498.498 0 0 0 0-.705l-3.29-3.3a.498.498 0 1 0-.705.705L8.085 5.5H2.5c-.275 0-.5.225-.5.5s.225.5.5.5Z",fill:"#fff"})))},R=(n.p,function(e){var t=e.content,n=e.type;return s.createElement(s.Fragment,null,s.createElement("span",{className:"version-tooltip"},(0,o.ZP)(t)),s.createElement("div",{className:"compare-icon ".concat(n)},function(){switch(n){case"add":return s.createElement(w,null);case"up":return s.createElement(Z,null);case"down":return s.createElement(P,null);case"equal":return s.createElement(C,null);default:return s.createElement(b,null)}}()))}),I=function(e){var t=e.item,n=e.type,r=t.sourceVersion,a=t.destinationVersion,i=(0,m.n)(r,a),o="themes"===n?"theme":"plugin";return s.createElement("span",{className:"version-compare"},s.createElement(R,{content:function(e){switch(e){case"add":return(0,l.gB)((0,l.__)("New %s will be added at version <strong>%s</strong>","wp-migrate-db"),o,r);case"up":return(0,l.gB)((0,l.__)("%s will be upgraded from <strong>%s</strong> to <strong>%s</strong>","wp-migrate-db"),o,a,r);case"equal":return(0,l.gB)((0,l.__)("%s will remain the same at version <strong>%s</strong>","wp-migrate-db"),o,r);case"down":return(0,l.gB)((0,l.__)("%s will be downgraded from <strong>%s</strong> to <strong>%s</strong>","wp-migrate-db"),o,a,r);case"none":return(0,l.gB)((0,l.__)("No version detected in %s header","wp-migrate-db"),o);default:return""}}(i),type:i}))},D=c.ZP.ul(A||(A=(0,i.Z)(["\n  height: 180px;\n  overflow-y: scroll;\n  resize: vertical;\n  margin: 15px 0 0 0;\n  border: 1px solid #d6d6d6;\n  border-radius: 4px;\n  padding: 0.5rem;\n  label {\n    margin: 0.25rem 0;\n    display: block;\n  }\n"]))),M=null,F=function(e,t,n,r,a){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},l=arguments.length>6?arguments[6]:void 0,c=arguments.length>7&&void 0!==arguments[7]?arguments[7]:"",u=arguments.length>8&&void 0!==arguments[8]?arguments[8]:"";function m(e){var a=e.target.value,i=-1===n.indexOf(a),s=(0,d.XV)(n,a);!0===e.nativeEvent.shiftKey&&null!==M&&(s=function(e,t,n,r){var a=n.indexOf(e),i=M<=a?M:a,s=M>a?M:a,o=n.slice(i,s+1);return t?(0,d.VU)(r,o):(0,d.fg)(r,o)}(a,i,t,n)),M=t.indexOf(a),r(s)}var f="";return e.map((function(e,t){Object.keys(i).length>0&&(f=void 0!==typeof i[e]?i[e]:"");var r="string"===typeof e?"checkbox-".concat(a,"-").concat(e):"checkbox-".concat(a,"-").concat(e.path),d="string"===typeof e?e:e.path,_="string"===typeof e?e:e.name;return s.createElement("li",{"data-value":d,key:p()()},s.createElement("input",{id:r,type:"checkbox",value:d,onChange:m,checked:n.includes(d),disabled:l}),s.createElement("label",{htmlFor:r,key:p()()},s.createElement("span",{className:"name"},_),s.createElement("span",{className:"extra-label"},(0,o.ZP)(f)),e.hasOwnProperty("sourceVersion")&&"except"!==u&&s.createElement(I,{item:e,type:c})))}))},L=function(e){var t=e.visible,n=e.iterator,r=e.extraLabels,a=e.selected,i=e.disabled,o=e.id,c=e.options?e.options:[],u=void 0===e.showOptions||e.showOptions,d=p()(),m=(0,s.useMemo)((function(){return n?n():F(c,function(e){return"string"===typeof e[0]?e:e.map((function(e){return e.path}))}(c),a,e.stateManager,d,r,i,e.type,e.themePluginOption)}),[n,a]);return s.createElement("div",{className:"select-group"},e.ToggleButtons||"",t&&s.createElement("div",{className:"select-wrap"},s.createElement(D,{id:o,className:e.className?e.className:""},m),u&&s.createElement("p",{className:"multiselect-options"},s.createElement("button",{onClick:function(){return e.updateSelected(c)}},(0,l.__)("Select All","wp-migrate-db")),s.createElement("button",{onClick:function(){e.updateSelected([]),M=null}},(0,l.__)("Deselect All","wp-migrate-db"))," ",s.createElement("button",{onClick:function(){return e.selectInverse()}},(0,l.__)("Invert Selection","wp-migrate-db"))),e.afterList||""))},B=s.memo(L)},40795:function(e,t,n){"use strict";n.d(t,{Z:function(){return C}});var r=n(79043),a=n(18489),i=n(88368),s=n(27166),o=n(33032),l=n(4665),c=n(12544),u=n.n(c),p=n(62295),d=n(22633),m=n(76178),f=n(51286),_=function(e){var t=e.enabled,n=e.panelName,r=e.toggle,a=e.disabled,i=(0,p.I0)();return l.createElement("input",{type:"checkbox",onChange:function(e){if(a)return e.preventDefault(),!1;i(t?(0,d.I4)(n):(0,d.LX)(n)),i(r)},checked:t,id:"enable-".concat(n),"aria-labelledby":"panel-title-".concat(n)})},g=function(e){var t=e.preTitle,n=e.childPanel,r=e.panelName,a=e.title,i=e.enabled,s=e.toggle,o=e.disabled,c="undefined"!==typeof e.hasInput&&e.hasInput,u=n?"h3":"h2";return l.createElement(l.Fragment,null,t,c&&l.createElement(_,{enabled:i,panelName:r,toggle:s,disabled:o}),l.createElement(u,{id:"panel-title-".concat(r),className:"panel-title"},a))};var b=function(e){var t=e.panelName,n=e.childPanel,r=e.toggleClassName;if(e.hideArrow)return null;var a=n?" white-arrow":"";return l.createElement("div",{className:"button-wrap"},l.createElement("button",{"aria-labelledby":"panel-title-".concat(t),"aria-describedby":"panel-summary-".concat(t)},l.createElement(f.r,{className:"".concat(r).concat(a," panel-arrow")})))},h=function(e){var t=e.childPanel,n=e.panelOpen,r=e.panelsOpen,a=e.preTitle,i=e.forceDivider,s=e.panelName,o=e.registeredPanels,c=e.hideArrow,u=e.title,p=n?"open":"closed",d=function(){return e.panelSummary?l.createElement("div",{id:"panel-summary-".concat(s),className:"panel-summary"},e.panelSummary):null},m=function(e){var t=e.intent,n=e.panelName,r=e.connected,a=e.file_uploaded,i=["push","pull"].includes(t);return"import"===t&&"import"===n?!!a:!(!i||!r||"connect"!==n)||""!==t&&"connect"!==n}({intent:e.intent,panelName:e.panelName,connected:e.connected,file_uploaded:e.file_uploaded});i&&(m=!0);var f=m?" bg-grey-light":"",_=[];return e.hasSummary&&e.panelSummary&&_.push("has-summary"),t?l.createElement("div",{className:"panel-header child-panel-header ".concat(_.join(" "))},l.createElement(b,{childPanel:t,panelName:s,panelsOpen:r,toggleClassName:p}),l.createElement("div",{className:"child-summary"},l.createElement(g,{preTitle:a,childPanel:t,panelName:s,title:u}),l.createElement(d,null))):l.createElement(l.Fragment,null,l.createElement(g,{preTitle:a,childPanel:t,panelName:s,title:u,disabled:e.disabled,enabled:e.enabled,toggle:e.toggle,hasInput:e.hasInput}),i&&l.createElement("div",{className:"accordion-divider".concat(f)}),l.createElement("div",{className:"panel-header ".concat(_.join(" "))},l.createElement(d,null),l.createElement(b,{childPanel:t,panelName:s,panelsOpen:r,toggleClassName:p,registeredPanels:o,hideArrow:c})))},v=n(50166);var E,w,y=function(e){var t=e.panelContentClass,n=e.panelOpen,r=e.bodyClass,s=e.id,o=e.children,c=function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).on,t=void 0===e||e,n=(0,l.useRef)(),r=(0,l.useState)(0),a=(0,i.Z)(r,2),s=a[0],o=a[1],c=(0,l.useRef)(s),u=(0,l.useState)((function(){return new ResizeObserver((function(e){n.current&&c.current!==n.current.offsetHeight&&(c.current=n.current.offsetHeight,o(n.current.offsetHeight))}))})),p=(0,i.Z)(u,1)[0];return(0,l.useLayoutEffect)((function(){return t&&n.current&&(o(n.current.offsetHeight),p.observe(n.current,{})),function(){return p.disconnect()}}),[t,n.current]),[n,s]}(),u=(0,i.Z)(c,2),d=u[0],m=u[1],f=(0,p.v9)((function(e){return e})).migrations,_=r?"".concat(t," ").concat(r):t,g="backup_local"===f.current_migration.intent,b=(0,l.useState)(g),h=(0,i.Z)(b,2),E=h[0],w=h[1],y=E?{config:(0,a.Z)((0,a.Z)({},v.vc.gentle),{},{clamp:!0}),from:{opacity:0,height:0},to:{opacity:n?1:0,height:n?m:0}}:{},x=(0,v.q_)(y);(0,l.useEffect)((function(){w(!0)}),[]);var k=n?"panel-open":"panel-closed";return l.createElement("div",{className:"".concat(k," panel-body-wrap")},l.createElement(v.q.div,{style:(0,a.Z)((0,a.Z)({},x),{},{overflow:"hidden"}),id:s},l.createElement("div",{ref:d,className:"".concat(_," panel-body")},o)))},x=n(4516),k=(n(43156),n(29942)),Z=n(42233),T=n(75338),S=n(49275);function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},N.apply(this,arguments)}var P=function(e){return l.createElement("svg",N({width:28,height:28,viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),E||(E=l.createElement("rect",{width:28,height:28,rx:6,fill:"#DC3232"})),w||(w=l.createElement("path",{d:"m20.258 8.866-4.95-2.858a2.636 2.636 0 0 0-2.625 0L7.742 8.866a2.625 2.625 0 0 0-1.309 2.275v5.7c0 .933.5 1.8 1.309 2.275l4.95 2.858a2.636 2.636 0 0 0 2.625 0l4.95-2.858a2.625 2.625 0 0 0 1.308-2.275v-5.7a2.66 2.66 0 0 0-1.317-2.275Zm-6.883 1.583A.63.63 0 0 1 14 9.824a.63.63 0 0 1 .625.625v4.375a.63.63 0 0 1-.625.625.63.63 0 0 1-.625-.625V10.45Zm1.392 7.4c-.042.1-.1.192-.175.275a.825.825 0 0 1-.909.175.862.862 0 0 1-.275-.175 1.217 1.217 0 0 1-.183-.275.82.82 0 0 1-.058-.316c0-.217.083-.434.241-.592a.86.86 0 0 1 .275-.175.832.832 0 0 1 .909.175.86.86 0 0 1 .175.275.83.83 0 0 1 .066.317.83.83 0 0 1-.066.316Z",fill:"#fff"})))},O=(n.p,function(e){var t=e.panelName,n=e.panelTitle,r=(0,p.v9)((function(e){return e.migrations})),a=(0,Z.gB)((0,Z.__)("<code>%s</code> is not writable at the destination.","wp-migrate-db"),(0,k.j_)(t,r));return console.log(t),l.createElement("div",{className:"error-disabled"},l.createElement("div",{className:"action-panel shadow-div error-panel"},l.createElement("div",{className:"panel-header-wrap panel-closed has-summary-no-child"},l.createElement(P,null),l.createElement("h4",{className:"panel-title"},n),l.createElement("div",{className:"accordion-divider bg-grey-light"}),l.createElement("div",{className:"panel-header has-summary"},l.createElement("div",{className:"panel-summary"},l.createElement("p",null,(0,T.ZP)(a)),l.createElement(S.Z,{link:"https://deliciousbrains.com/wp-migrate-db-pro/doc/folder-permission-errors/",content:(0,Z.__)("How to Fix Folder Permissions","wp-migrate-db"),utmContent:"not-writable-error-panel-".concat(t),utmCampaign:"wp-migrate-documentation",hasArrow:!0}))))))}),A=function(){var e=(0,o.Z)((0,s.Z)().mark((function e(t){var r,a;return(0,s.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Promise.resolve().then(n.bind(n,67821));case 2:return r=e.sent,a=r.selectFromImportData,e.abrupt("return",a("file_uploaded",t));case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();var C=(0,p.$j)((function(e){var t=(0,m.O)("panelsOpen",e),n=(0,m.O)("panelClicked",e),r=(0,m.O)("panelStatus",e),a=(0,m.O)("registeredPanels",e),i=(0,x.r5)("intent",e),s=(0,x.r5)("connected",e),o=!1;return(0,k.Yu)()&&(o=A(e)),{panels:e.panels,panelsOpen:t,panelClicked:n,panelStatus:r,intent:i,connected:s,file_uploaded:o,registeredPanels:a}}),{removeOpenPanel:d.I4,togglePanelsOpen:d.SO,setPanelClicked:d.er,setPanelStatus:d.OW,registerPanel:d.ii})((function(e){var t=e.disabled,n=e.childPanel,s=e.className,o=e.hasDefault,c=e.selected,p=e.hasInput;(0,l.useEffect)((function(){e.registerPanel(e.panelName)}),[e.intent]);var d=u()(e.panelsOpen,e.panelName),m=d?"open":"closed",f=(0,l.useState)(""),_=(0,i.Z)(f,2),g=_[0],b=(_[1],(0,l.useState)(!1)),v=(0,i.Z)(b,2),E=v[0],w=(v[1],(0,l.useState)(!1)),x=(0,i.Z)(w,2),Z=x[0],T=(x[1],e.panelName),S=e.bodyClass,N=e.hasOwnProperty("panelSummary"),P=(0,a.Z)((0,a.Z)({},e),{},{panelOpen:d,hasSummary:N,panelName:T,hasInput:p}),A="action-panel shadow-div";s&&(A+=" ".concat(s));var C=function(e,t,n){var r=[];return e&&r.push("child-panel"),e&&t&&r.push("shadows"),n&&r.push("disabled"),r}(n,d,t),R=function(e,t,n,r,a){var i=[],s="panel-header-wrap";return s+=e?" panel-open":" panel-closed",t&&(n||!n&&r.length)&&i.push("maybe-has-default"),a&&!t?i.push("has-summary-no-child"):t||i.push("no-child"),t&&i.push("child-panel"),t&&!a&&i.push("no-summary"),{panelHeaderClasses:i,panelHeaderBaseClass:s}}(d,n,o,c,N),I=R.panelHeaderClasses,D=R.panelHeaderBaseClass;return"false"===e.writable?l.createElement(O,{panelTitle:P.title,description:"",panelName:T}):l.createElement("div",{key:e.panelKey,className:"".concat(A," ").concat(C.join(" "))},l.createElement("div",{onClick:function(t){if(e.disabled)return!1;!function(e,t,n,r,a,i,s){if(e.hasOwnProperty("callback")&&"function"===typeof e.callback&&!1===e.callback(s))return;var o="open";if(a&&(o="closing"),e.setPanelStatus(o),e.setPanelClicked(r),e.disabled)return!1;e.togglePanelsOpen(r)}(e,0,0,T,d,0,t)},className:"".concat(D," ").concat(I.join(" ")),id:"wpmdb-".concat((0,k.hs)(T))},l.createElement(h,P)),l.createElement(y,(0,r.Z)({shouldClose:Z,panelAnimating:g,panelName:T,panelOpenState:E,panelContentClass:m,panelOpen:d,className:S||"",id:T},e)))}))},29942:function(e,t,n){"use strict";n.d(t,{VU:function(){return h},op:function(){return w},wM:function(){return x},lL:function(){return U},mF:function(){return B},mp:function(){return A},Tc:function(){return T},NR:function(){return z},Nh:function(){return M},fX:function(){return L},OK:function(){return G},Yu:function(){return F},qu:function(){return C},sk:function(){return I},SC:function(){return R},Ol:function(){return O},j_:function(){return j},fg:function(){return v},Ph:function(){return N},zj:function(){return P},hs:function(){return E},XV:function(){return b},ME:function(){return D},mQ:function(){return S},oj:function(){return k}});var r=n(27166),a=n(33032),i=n(31125),s=n(62295),o=n(42233),l=n(10734),c=n.n(l),u=n(67101),p=n.n(u),d=n(12544),m=n.n(d),f=n(81294),_=n(68424);var g=n(27114);function b(e,t){return-1!==c()(e,t)?p()(e,t):[].concat((0,i.Z)(e),[t])}function h(e,t){return(0,i.Z)(new Set([].concat((0,i.Z)(e),(0,i.Z)(t))))}function v(e,t){return e.filter((function(e){return!t.includes(e)}))}function E(e){e=(e=e.replace(/^\s+|\s+$/g,"")).toLowerCase();for(var t="\xe0\xe1\xe3\xe4\xe2\xe8\xe9\xeb\xea\xec\xed\xef\xee\xf2\xf3\xf6\xf4\xf9\xfa\xfc\xfb\xf1\xe7\xb7/_,:;",n=0,r=t.length;n<r;n++)e=e.replace(new RegExp(t.charAt(n),"g"),"aaaaaeeeeiiiioooouuuunc------".charAt(n));return e=e.replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-")}function w(e,t){return y.apply(this,arguments)}function y(){return y=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,i,s,o,l=arguments;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=l.length>2&&void 0!==l[2]&&l[2],i=l.length>3&&void 0!==l[3]?l[3]:null,s={method:"POST",credentials:"same-origin",headers:{"Content-Type":"application/json","X-WP-Nonce":window.wpmdb_data.nonces.rest_nonce},body:JSON.stringify(n),signal:window.wpmdb_abort_controller.signal},a&&(s.signal=a.signal),e.next=6,fetch(window.wpmdb_data.MDB_API_BASE+t,s);case 6:if((o=e.sent).ok){e.next=10;break}throw i&&i((function(e,t){e({type:"MDB_REST_NOT_ACTIVE"}),e((0,_.A_)())})),new Error(o.statusText);case 10:return e.abrupt("return",o.json());case 11:case"end":return e.stop()}}),e)}))),y.apply(this,arguments)}function x(e){var t=e.preRequest,n=void 0===t?function(){}:t,i=e.asyncFn,s=e.requestFailed,o=void 0===s?function(){}:s,l=e.requestSuccess,c=void 0===l?function(){}:l;return(0,a.Z)((0,r.Z)().mark((function e(){var t;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n(),e.prev=1,e.next=4,i;case 4:t=e.sent,e.next=11;break;case 7:return e.prev=7,e.t0=e.catch(1),o(e.t0),e.abrupt("return",!1);case 11:if(t.success){e.next=13;break}return e.abrupt("return",o(t));case 13:return c(t),e.abrupt("return",t);case 15:case"end":return e.stop()}}),e,null,[[1,7]])})))}function k(e){return Z.apply(this,arguments)}function Z(){return Z=(0,a.Z)((0,r.Z)().mark((function e(t){var n,a,i,s,o,l=arguments;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(i in n=l.length>1&&void 0!==l[1]&&l[1],a=new FormData,t)a.append(i,t[i]);return s={method:"POST",credentials:"same-origin",body:a,signal:window.wpmdb_abort_controller.signal},n&&(s.signal=n.signal),e.next=7,fetch(window.ajaxurl,s);case 7:if((o=e.sent).ok){e.next=14;break}return e.t0=Error,e.next=12,o.text();case 12:throw e.t1=e.sent,new e.t0(e.t1);case 14:return e.abrupt("return",o.json());case 15:case"end":return e.stop()}}),e)}))),Z.apply(this,arguments)}function T(e){w("/error-migration",{action:"cancel",error_message:(0,g.Y)(e)})}function S(e){return/^([a-z]([a-z]|\d|\+|-|\.)*):(\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?((\[(|(v[\da-f]{1,}\.(([a-z]|\d|-|\.|_|~)|[!\$&'\(\)\*\+,;=]|:)+))\])|((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=])*)(:\d*)?)(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*|(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)|((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)|((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)){0})(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(\#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(e)}var N=function(e){return e?e.replace(/(^\w+:|^)\/\//,"//"):e},P=function(e){return e?e.replace(/(^\w+:|^)\/\//,""):e},O=function(e){return e!==window.WPMDBStore.getState().migrations.local_site.this_prefix},A=function(e){var t=e.intent;if(""!==t)return{savefile:(0,o.__)("Export","wp-migrate-db"),backup_local:(0,o.__)("Backup","wp-migrate-db"),import:(0,o.__)("Import","wp-migrate-db"),find_replace:(0,o.__)("Find & Replace","wp-migrate-db"),push:(0,o.__)("Push","wp-migrate-db"),pull:(0,o.__)("Pull","wp-migrate-db")}[t]};function C(e,t,n){var r;return m()(["find_replace","savefile","pull","backup_local","import"],e)?r=t.write_permissions:"push"===e&&(r=n.write_permissions),{disabled:"false"===r,uploads_dir:"push"===e?n.uploads_dir:t.this_uploads_dir}}function R(e,t){var n=t.migrations,r=n.local_site,a=n.remote_site,i=n.current_migration,s=(0,f.O)(r,a,i,e);return(s=s.join(""))===s.toLowerCase()?null:("1"===r.lower_case_table_names||"push"!==e&&"savefile"!==e)&&("1"===a.lower_case_table_names||"pull"!==e)||null}function I(e,t){var n=t.migrations,r=t.multisite_tools,a=n.local_site,i=n.remote_site;if(m()(["push","pull"],e)){if(!r||!a.site_details||!i.site_details)return!1;if(a.site_details.is_multisite!==i.site_details.is_multisite)return"1"!==a.mst_available||"1"!==i.mst_available||!r.is_licensed}return!1}function D(){var e=(0,s.v9)((function(e){return e.settings})),t=e.masked_licence;if(!e.isPro)return!0;var n=(0,s.v9)((function(e){return e.dbi_api_data})).licence.licence_status;return null===t&&!window.wpmdb_settings.license_constant||![null,"active_licence","subscription_expired"].includes(n)}function M(e){return window.reactpluginBuildURLmdb+e}var F=function(){return!1};function L(e){var t=[];if(null===e||!e.hasOwnProperty("imported")||!1===e.imported)return t;var n=e.value,r=n.current_migration,a=n.media_files;return r.hasOwnProperty("post_types_option")&&"all"!==r.post_types_option&&["pull","import"].includes(r.intent)&&t.push("post_types"),a.hasOwnProperty("enabled")&&!0===a.enabled&&t.push("media_files"),t}var B=function(e,t){var n={pull:(0,o.__)("to pull remote data into this site","wp-migrate-db"),push:(0,o.__)("to push data to a remote site","wp-migrate-db"),import:(0,o.__)("to import data into this site","wp-migrate-db"),regex:(0,o.__)("to use Regular Expressions","wp-migrate-db"),dryRun:(0,o.__)("to view details on the exact changes that will be made","wp-migrate-db")},r=(0,o.__)("Activate your license","wp-migrate-db"),a="#settings/enter",i="";t||(r=(0,o.__)("Upgrade","wp-migrate-db"),a="https://deliciousbrains.com/wp-migrate-db-pro/upgrade/?utm_campaign=WP%2BMigrate%2BDB%2BPro%2BUpgrade&utm_source=MDB%2BFree&utm_medium=insideplugin",i='target="_blank"');var s=(0,o.__)('<a href="%s" %s rel="noopener noreferrer" class="upgrade">%s</a> %s',"wp-migrate-db");return(0,o.gB)(s,a,i,r,n[e])},U=function(e){return(e/1e3/1024).toFixed(2)},j=function(e,t){var n,r,a,i,s,o=t.current_migration,l=t.local_site,c=t.remote_site,u="pull"===o.intent,p=u?c:l,d=p.site_details,m=d.themes_path,f=d.plugins_path,_=d.muplugins_path,g=d.content_dir,b=u?p.path:p.this_path,h=function(e,t){return void 0===e?null:e.replace(t,"").replace(/^\/|\/$/g,"")};switch(e){case"themes":case"theme_files":return null!==(n=h(m,b))&&void 0!==n?n:"wp-content/themes";case"plugins":case"plugin_files":return null!==(r=h(f,b))&&void 0!==r?r:"wp-content/plugins";case"muplugins":case"muplugin_files":return null!==(a=h(_,b))&&void 0!==a?a:"wp-content/mu-plugins";case"media":case"media_files":return null!==(i=h(u?p.wp_upload_dir:p.this_wp_upload_dir,b))&&void 0!==i?i:"wp-content/uploads";case"others":case"other_files":return null!==(s=h(g,b))&&void 0!==s?s:"wp-content";default:return""}},z=function(e,t,n,r){var a=t.localSource?r:n;switch(e){case"theme_files":case"themes":return a.themes_permissions;case"plugin_files":case"plugins":return a.plugins_permissions;case"muplugin_files":case"muplugins":return a.muplugins_permissions;case"other_files":case"others":return a.others_permissions;default:return a.write_permissions}},G=function(){var e=window.location.hash.substring(1);if(e.includes("unsaved")||e.includes("migrate")){var t=e.split("/");return t.length>1&&t}return!1}},18832:function(e,t,n){"use strict";var r=n(10734),a=n.n(r);t.Z=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",i=[];t.forEach((function(e){return-1===a()(n,e)&&i.push(e),null})),e(i,r)}},66055:function(e,t,n){"use strict";n.d(t,{m:function(){return a}});var r=n(18489);function a(e,t){for(var n=arguments.length,a=new Array(n>2?n-2:0),i=2;i<n;i++)a[i-2]=arguments[i];return(0,r.Z)({type:e,payload:t},a)}},74094:function(e,t,n){"use strict";n.d(t,{J:function(){return i}});var r=n(29942),a=n(58696),i=function(){return function(e,t){return!!(0,r.Yu)()&&(0,a.C)("status",t()).connecting}}},83115:function(e,t,n){"use strict";n.d(t,{$1:function(){return m},G$:function(){return u},_s:function(){return d},dy:function(){return p},g1:function(){return c}});var r=n(14251),a=n(3460),i=n(52650),s=n(29950),o=n(66866),l=n(15265),c=function(){return function(e){if(e((0,r.LR)(!0)),e(u(!0)),e({type:"WPMDB_PRE_MIGRATION"}),e({type:"MIGRATION_STARTED"}),!e((0,i.b)()))return!1;e((0,a.Cy)())}},u=function(e){return{type:s.Um,payload:e}},p=function(e){return{type:o.DL,payload:e}},d=function(e){return{type:l.DQ,payload:e}},m=function(e){return{type:l._b,payload:e}}},22497:function(e,t,n){"use strict";n.d(t,{KG:function(){return a},KJ:function(){return i},Kw:function(){return o},O:function(){return l},ku:function(){return s}});var r=n(18066);function a(e,t){return function(n,a){n({type:r.TO,payload:{name:e,fn:t}})}}function i(e,t){return function(n,a){n({type:r.fK,payload:{name:e,fn:t}})}}function s(e){return function(t,n){t({type:r.l3,payload:{name:e}})}}function o(e){return function(t,n){n().mdb_filters.actions.forEach((function(t){t.name===e&&t.fn.call()}))}}function l(e){for(var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length,a=new Array(r>2?r-2:0),i=2;i<r;i++)a[i-2]=arguments[i];return function(r,i){var s=i().mdb_filters.filters,o=n;return s.forEach((function(n){var r;n.name===e&&(o=(r=n.fn).call.apply(r,[t,o].concat(a)))})),o}}},47895:function(e,t,n){"use strict";n.d(t,{C:function(){return u},Lk:function(){return p},Tf:function(){return c},q$:function(){return l}});var r=n(27166),a=n(33032),i=n(12544),s=n.n(i),o=n(66055);function l(e,t){return function(n,r){n((0,o.m)(t,e))}}function c(e,t,n){return function(){var i=(0,a.Z)((0,r.Z)().mark((function a(i,s){return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:i((0,o.m)(n,{type:e,status:t}));case 1:case"end":return r.stop()}}),a)})));return function(e,t){return i.apply(this,arguments)}}()}function u(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return function(t,n){var r=n().migrations;""===e&&(e=r.current_migration.intent);var a="true"===r.local_site.is_multisite;if(s()(["find_replace","savefile","backup_local"],e)&&a)return!0;if(s()(["push","pull"],e)){if(!r.remote_site.site_details)return!1;var i="true"===r.remote_site.site_details.is_multisite;return a||i}return!1}}function p(e,t,n,r){return"migration"===r?"pull"===e||"import"===e?n:t:"pull"===e||"import"===e?t:n}},14251:function(e,t,n){"use strict";n.d(t,{CV:function(){return f},E2:function(){return g},ED:function(){return _},I8:function(){return p},LR:function(){return x},OZ:function(){return Z},P_:function(){return v},S5:function(){return b},Tf:function(){return u},fJ:function(){return m},gy:function(){return E},j8:function(){return k},nX:function(){return T},oA:function(){return d},qg:function(){return h},t9:function(){return w},uI:function(){return S},vq:function(){return y}});var r=n(29942),a=n(66055),i=n(29950),s=n(66866),o=n(22973),l=n(4516),c=n(3460);function u(e){return(0,a.m)(i.d0,e)}function p(e){return(0,a.m)(i.n_,e)}function d(){return function(e){e((0,a.m)("RESET_APP",{}))}}function m(e){return{type:o.Qu,payload:e}}function f(e){return function(t,n){var a=n();t({type:i.av,payload:e});var s=a.migrations.current_migration;t(m((0,r.mp)({intent:e.intent,local_site:a.migrations.local_site,remote_site:a.migrations.remote_site,connected:s.connected}))),"savefile"===e.intent&&(0,r.SC)("savefile",n())&&t(_())}}function _(){return function(e){e((0,a.m)("SET_CONNECTION_STATUS",{key:"mixed_case_table_name_warning",statusVal:!0}))}}function g(e){return function(t,n){if(t({type:i.KU,payload:e}),"backup_selected"===(0,l.r5)("backup_option",n())){var r=t((0,c.K6)());t(h(r))}}}function b(e,t){return function(n){n({type:i.mu,payload:e}),"all"===e&&n(g(t))}}function h(e){return{type:i.O$,payload:e}}function v(e){return function(t,n){t({type:i.Yl,payload:e});var r=t((0,c.K6)());t(h(r))}}function E(e){return{type:i.ic,payload:e}}function w(e,t){return function(n){n({type:i.Nt,payload:e}),"all"===e&&n(E(t))}}function y(e){return{type:i.Ss,payload:e}}function x(e){return{type:s.mx,payload:e}}function k(e){return{type:i.b_,payload:e}}function Z(e,t){return function(n){n({type:i.mO,payload:e}),n({type:i.Ac,payload:t})}}function T(e){return function(t){t({type:i.u2,payload:e})}}function S(e){return function(t){t({type:i.EF,payload:e})}}},58900:function(e,t,n){"use strict";n.d(t,{B3:function(){return Z},BG:function(){return w},K_:function(){return k},s2:function(){return T}});var r=n(88368),a=n(89472),i=n(27166),s=n(33032),o=n(12544),l=n.n(o),c=n(42233),u=n(62295),p=n(4516),d=n(29942),m=n(66866),f=n(66055),_=n(42222),g=n(91828),b=n(38906),h=n(22497),v=n(86645),E=n(29816),w=function(e){return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,a,s,o,u,d,_,g;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t((0,f.m)(m.V$,"Finalizing Migration")),t((0,f.m)(m.MG,"FINALIZING")),r=(0,p.r5)("intent",n()),a=(0,p.r5)("stages",n()),s=(0,p.xg)("dump_info",n()),o=(0,p.r5)("databaseEnabled",n()),u=(0,p.r5)("fseDumpFilename",n()),0!==Object.entries(s).length||!("savefile"===r&&o||"backup_local"===r)){e.next=9;break}throw new Error((0,c.__)("File dump info empty","wp-migrate-db"));case 9:if("backup_local"===r||"savefile"===r){e.next=11;break}return e.abrupt("return",t(y(r)));case 11:if(d=window.wpmdb_data.this_download_url+(o?encodeURIComponent(s.dump_filename):u),_=(0,p.r5)("advanced_options_selected",n()),(g=o?a.length>1:"savefile"===r)?d+="&zip=1":l()(_,"gzip_file")&&(d+="&gzip=1"),g&&(d+="&fullSiteExport=1"),"savefile"!==r){e.next=19;break}return t(N(r)),e.abrupt("return",setTimeout((function(){window.location=d}),0));case 19:return e.abrupt("return",t(N(r,s)));case 20:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},y=function(e){return function(){var t=(0,s.Z)((0,i.Z)().mark((function t(n,r){return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n((0,s.Z)((0,i.Z)().mark((function e(){var t,a,s,o,l;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,p.Gn)(r());case 2:return t=e.sent,a=(0,p.r5)("databaseEnabled",r()),s={tables:a?t.join(","):""},(0,p.do)(r())&&(s.prefix=(0,p._P)("this_prefix",r())),s=n((0,h.O)("wpmdbFinalizeMigration",s)),l=(0,p.xg)("document_title",r()),document.title=(0,c.__)("Finalizing","wp-migrate-db")+" - "+l,e.prev=9,e.next=12,(0,d.op)("/finalize-migration",s);case 12:o=e.sent,n((0,h.Kw)("afterFinalizeMigration")),e.next=21;break;case 16:return e.prev=16,e.t0=e.catch(9),console.error(e.t0),n((0,b.m7)({error_type:m.gF,error_message:e.t0})),e.abrupt("return",!1);case 21:if(o.success){e.next=24;break}return n((0,b.m7)({error_type:m.gF,error_message:o.data})),e.abrupt("return",!1);case 24:return e.abrupt("return",o);case 25:case"end":return e.stop()}}),e,null,[[9,16]])})))).then(function(){var t=(0,s.Z)((0,i.Z)().mark((function t(r){return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r){t.next=2;break}return t.abrupt("return",!1);case 2:return t.next=4,x(n,e);case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(e){n((0,b.m7)({error_type:m.gF,error_message:e}))})));case 1:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()},x=function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,a,s;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t((0,f.m)(m.V$,(0,c.__)("Flushing caches...","wp-migrate-db"))),(r=new FormData).append("action","wpmdb_flush"),r.append("nonce",window.wpmdb_data.nonces.flush),e.next=6,fetch(window.ajaxurl,{method:"POST",body:r});case 6:if((a=e.sent).ok){e.next=9;break}throw new Error(a.statusText);case 9:return e.next=11,a.json();case 11:if((s=e.sent).success){e.next=14;break}throw new Error(s.data);case 14:return e.abrupt("return",t(N(n)));case 15:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),k=function(){return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,s,o,l,c;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,p.xg)("migration_tables",n());case 2:r=e.sent,s=!1,o=(0,a.Z)(r),e.prev=5,o.s();case 7:if((l=o.n()).done){e.next=14;break}if(c=l.value,!(s=/(.*)_options|(.*)_sitemeta/.test(c))){e.next=12;break}return e.abrupt("break",14);case 12:e.next=7;break;case 14:e.next=19;break;case 16:e.prev=16,e.t0=e.catch(5),o.e(e.t0);case 19:return e.prev=19,o.f(),e.finish(19);case 22:return e.abrupt("return",s);case 23:case"end":return e.stop()}}),e,null,[[5,16,19,22]])})));return function(t,n){return e.apply(this,arguments)}}()},Z=function(e){return function(t,n){var a=(0,p.r5)("intent",n()),i=(0,p.xg)("document_title",n()),s=n().profiles;document.title=i;l()(["import","pull","find_replace"],a)&&(t(k())&&"COMPLETE"===e&&(l()(["import","pull"],a)?window.location.href=function(){var e=window.location.hash,t=window.location.href.replace(e,""),n=(0,r.Z)(s.recent,1)[0];n=void 0!==n?n.id:"";var a=0;-1===e.indexOf("#unsaved/")&&-1===e.indexOf("#migrate/")||(-1!==e.indexOf("migrate")&&(a=1),n=e.match(/([0-9]+)(?=[^/]*$)/gm));return"".concat(t,"&redirect_profile=").concat(n,"&saved_profile=").concat(a)}():window.location.reload()))}},T=function(e){var t=(0,_.i2)(e),n=(0,_.er)(e),r=(0,_.$s)(e),a=(0,c.gB)((0,c.__)("%ss","wp-migrate-db"),t);return n>0&&(a=(0,c.gB)((0,c.__)("%sm %s","wp-migrate-db"),n,a)),r>0&&(a=(0,c.gB)((0,c.__)("%shr %s","wp-migrate-db"),r,a)),a},S=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(t,n){var a,i,s,o=(0,p.r5)("intent",n());(0,d.Yu)()&&(s=(0,d.zj)((0,p.FY)("url",n())));var l=(0,p.xg)("migration_size",n()),u=(0,p.xg)("timer",n()),m=(0,p.xg)("migration_size",n()),f=(0,_.UJ)(l),b=T(u.time),h="pull"===o?(0,c.__)("from","wp-migrate-db"):(0,c.__)("to","wp-migrate-db");switch(o){case"pull":case"push":var w=(0,p.r5)(["payloadSizeHistory","stages_complete"],n()),y=(0,r.Z)(w,2),x=y[0],k=y[1],Z=(0,c.gB)((0,c.__)('with an average request size of <span class="text-primary semibold">%sMB</span>',"wp-migrate-db"),(0,d.lL)((0,v.O)(x)));a="".concat(g.B[o],' <span class="regular">').concat(h,"</span> ").concat(s),i=(0,c.gB)((0,c.__)('<span class="text-primary semibold">%s%s</span> of data was migrated in <span class="text-primary semibold">%s</span> %s'),f,(0,_.TJ)(m),b,k&&k.some((function(e){return E.AG.includes(e)}))?Z:"");break;case"savefile":a=(0,c.__)("Export Complete","wp-migrate-db"),i=(0,c.gB)((0,c.__)('<span class="text-primary semibold">%s%s</span> of data was exported in <span class="text-primary semibold">%s</span>',"wp-migrate-db"),f,(0,_.TJ)(m),b);break;case"import":a=(0,c.__)("Import Complete","wp-migrate-db"),i=(0,c.gB)((0,c.__)('Completed in <span class="text-primary semibold">%s</span>'),b);break;case"find_replace":a=(0,c.__)("Find & Replace Complete","wp-migrate-db"),i=(0,c.gB)((0,c.__)('<span class="text-primary semibold">%s%s</span> of data was replaced in <span class="text-primary semibold">%s</span>',"wp-migrate-db"),f,(0,_.TJ)(m),b);break;case"backup_local":a=(0,c.__)("Backup Complete","wp-migrate-db"),i=e.dump_path?(0,c.__)("The backup file has been saved to your server.","wp-migrate-db"):""}return{title:a,message:i}}},N=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(r,a){var s,o,l;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r((0,b.z6)(!0)),(0,u.dC)((function(){r((0,f.m)(m.Jj,"COMPLETE")),r((0,b.Uo)());var e=r(S(t)),n=e.title,a=e.message;r((0,f.m)(m.V$,n)),r((0,f.m)(m.Du,a))})),s=(0,p.xg)("document_title",a()),!(0,d.Yu)()){e.next=9;break}return e.next=6,Promise.resolve().then(n.bind(n,44789));case 6:o=e.sent,l=o.TrackMigrationComplete,r(l());case 9:document.title=(0,c.__)("Complete","wp-migrate-db")+" - "+s;case 10:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}},3460:function(e,t,n){"use strict";n.d(t,{Am:function(){return B},Cy:function(){return Z},Gn:function(){return R},K6:function(){return U},Kh:function(){return I},Rw:function(){return D},Z6:function(){return T},al:function(){return F},h8:function(){return v},ly:function(){return y},p_:function(){return E},uk:function(){return h},z6:function(){return j}});var r=n(88368),a=n(27166),i=n(33032),s=n(62295),o=n(42233),l=n(66055),c=n(29950),u=n(66866),p=n(38906),d=n(4516),m=n(29942),f=n(27325),_=n(22497),g="INITIATE_MIGRATION",b="MIGRATE",h="UPLOAD",v="UPLOAD_IMPORT_SUCCESSFUL",E="IMPORT_FILE",w="ADDONS_STAGE",y="COMPLETE";function x(e,t){return{find_replace:(0,o.__)("Running find & replace...","wp-migrate-db"),import:(0,o.__)("Importing SQL file...","wp-migrate-db"),push:(0,o.gB)((0,o.__)("Pushing to %s...","wp-migrate-db"),t),pull:(0,o.gB)((0,o.__)("Pulling from %s...","wp-migrate-db"),t),backup:(0,o.__)("Running backup, please wait...","wp-migrate-db"),savefile:(0,o.__)("Exporting, please wait...","wp-migrate-db")}[e]}var k={};function Z(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,s.dC)((function(){t((0,l.m)(c.f7,"initiate_migration")),t((0,p.yo)()),t((0,l.m)(u.ol,document.title)),t((0,l.m)(c.jD,!0)),t((0,l.m)(u.V$,(0,o.__)("Establishing Connection...","wp-migrate-db")))})));case 1:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}function T(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return function(){var c=(0,i.Z)((0,a.Z)().mark((function i(c,m){var f,_,x,k;return(0,a.Z)().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:c((0,l.m)(u.MG,e)),a.t0=e,a.next=a.t0===g?4:a.t0===b?5:a.t0===h?6:a.t0===v||a.t0===E?7:a.t0===w?8:a.t0===y?13:19;break;case 4:return a.abrupt("return",c(S()));case 5:return a.abrupt("return",c(N(s,t,o)));case 6:return a.abrupt("return",c(P(s,t)));case 7:return a.abrupt("return",c(O(t)));case 8:return a.next=10,Promise.resolve().then(n.bind(n,34653));case 10:return f=a.sent,_=f.runAddonsStage,a.abrupt("return",c(_(t,o)));case 13:return(0,d.xg)("pause_before_finalize",m())&&c((0,l.m)(u.Jj,"PAUSED")),x=(0,r.Z)(t,1),k=x[0],a.next=18,c(B(p.BG,[k],!0));case 18:return a.abrupt("return");case 19:case"end":return a.stop()}}),i)})));return function(e,t){return c.apply(this,arguments)}}()}var S=function(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){var r,i;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t((0,p.kK)()),t(j(!1)),(0,s.dC)((function(){t((0,l.m)(c.f7,"initiate_migration")),t((0,l.m)(u.ol,document.title)),t((0,l.m)(c.jD,!0)),t((0,l.m)(u.V$,(0,o.__)("Establishing Connection...","wp-migrate-db")))})),r=(0,d.r5)("intent",n()),e.next=6,t(B(p.Up));case 6:if(i=e.sent){e.next=9;break}return e.abrupt("return",!1);case 9:if("import"!==r||"backup"===i){e.next=11;break}return e.abrupt("return",t(T(h)));case 11:return e.abrupt("return",t(T(b)));case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},N=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i,s){var o,u,m,g,b;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(!(0,d.r5)("databaseEnabled",s())){r.next=23;break}if(u=(0,f.u)("delay_between_requests",s()),m=(0,d.xg)("status",s()),n||"CANCELLED"===m||i(A(e)),g=[-1,"",0],o=p.Zk,t.length&&(o=t[0].fn,g=t[0].args),e.length&&g.push(e),!(u>0)){r.next=15;break}return r.next=12,(0,p.QK)((function(){return i(B(o,g))}),1e3*u);case 12:b=r.sent,r.next=18;break;case 15:return r.next=17,i(B(o,g));case 17:b=r.sent;case 18:if(b){r.next=20;break}return r.abrupt("return",!1);case 20:if(b.hasOwnProperty("dump_filename")||"success"===b){r.next=22;break}throw new Error(b);case 22:i((0,l.m)(c.sP,"tables"));case 23:return r.next=25,i((0,_.O)("mdbAddonActions",null));case 25:if(null!==r.sent){r.next=28;break}return r.abrupt("return",i(T(y)));case 28:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}()},P=function(e,t){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(r){var i,s,c,p;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r((0,l.m)(u.V$,(0,o.__)("Uploading File","wp-migrate-db"))),s=[0],e.next=4,n.e(879).then(n.bind(n,78879));case 4:c=e.sent,p=c.uploadFileActions,i=p,t.length&&(i=t[0].fn,s=t[0].args),r(B(i,s));case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},O=function(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n){var r,i;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=null,i=null,e.length&&(i=e[0].fn,r=e[0].args),t.abrupt("return",n(B(i,r)));case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()};function A(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n,r){var i,s,o,u,p;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=(0,d.r5)("intent",r()),"initiate_migration"===(0,d.r5)("current_stage",r())&&(s="",(0,m.Yu)()&&(s=(0,d.FY)("url",r())),n(L(x(i,s)))),o=(0,d.r5)("backup_option",r()),u="migrate","find_replace"===i&&(u="find_replace"),"none"!==o&&(u="backup"),e.length&&(u=e),n((0,l.m)(c.f7,u)),"backup"===u?(p=n(U()),n(M(p))):n(C(r(),(0,d.Gn)(r())));case 10:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}var C=function(e,t){return M(t)};function R(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t((0,l.m)(u.V$,(0,o.__)("Cancelling migration...","wp-migrate-db"))),"PAUSED"!==(0,d.xg)("status",n())){e.next=5;break}return t((0,l.m)(u.Jj,"CANCELLED")),e.abrupt("return",t(B((function(){return!1}))));case 5:t(j(!0)),t((0,l.m)(u.Jj,"CANCELLED"));case 7:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}function I(){return function(e,t){e((0,l.m)(u.V$,(0,o.__)("Pausing...","wp-migrate-db"))),e((0,l.m)(u.Jj,"PAUSED"))}}function D(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){var r;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t((0,l.m)(u.Jj,"")),t((0,p.Xg)()),r=(0,d.xg)("progress_stage",n()),e.next=5,t(T(r,[{fn:k.fn,args:k.args}],!1,!0));case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}function M(e){return(0,l.m)(u.Z,e)}function F(e){return function(t){t((0,l.m)(c.jD,e))}}var L=function(e){return function(t,n){return t((0,l.m)(u.V$,e))}};function B(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i,c){var f,_,g,b,h,v,E;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(f=(0,d.r5)("preview",c()),_=(0,d.xg)("status",c()),g=(0,d.xg)("progress_stage",c()),b=(0,d.xg)("pause_before_finalize",c()),h=(0,o.__)("Paused","wp-migrate-db"),v=(0,o.gB)("%s - %s",h,(0,o.__)("preview changes below","wp-migrate-db")),"CANCELLED"!==_){r.next=23;break}return r.prev=7,r.next=10,(0,m.op)("/cancel-migration",{action:"cancel"});case 10:E=r.sent,r.next=16;break;case 13:return r.prev=13,r.t0=r.catch(7),r.abrupt("return",i((0,p.m7)({error_type:u.gF,error_message:"".concat((0,o.__)("Migration cancellation failed")," \u2013 ").concat(r.t0)})));case 16:if(E.success){r.next=18;break}return r.abrupt("return",i((0,p.m7)({error_type:u.gF,error_message:"".concat((0,o.__)("Migration cancellation failed")," \u2013 ").concat(E.data)})));case 18:return i(j(!0)),(0,s.dC)((function(){i((0,p.Uo)()),i((0,l.m)(u.V$,(0,o.__)("Migration cancelled","wp-migrate-db"))),i((0,l.m)(u.Jj,"CANCEL_COMPLETE"))})),r.abrupt("return",!1);case 23:if(!("PAUSED"===_||b&&n)){r.next=29;break}return b&&n&&i((0,l.m)(u.mx,!1)),(0,s.dC)((function(){i((0,l.m)(u.V$,f&&"COMPLETE"===g?v:h)),i((0,p.N6)())})),k.fn=e,k.args=t,r.abrupt("return",!1);case 29:return r.next=31,i(e.apply(null,t));case 31:return r.abrupt("return",r.sent);case 32:case"end":return r.stop()}}),r,null,[[7,13]])})));return function(e,t){return r.apply(this,arguments)}}()}var U=function(){return function(e,t){var n=(0,d.r5)("intent",t()),r=(0,d.NR)("current_migration",t()),a=r.backup_option,i=r.tables_option,s=[],o="push"===n?(0,d.FY)("prefixed_tables",t()):(0,d._P)("this_prefixed_tables",t()),l="push"===n?(0,d.FY)("tables",t()):(0,d._P)("this_tables",t());switch(a){case"backup_only_with_prefix":s=o;break;case"backup_selected":var c;c="import"===n?(0,d.FY)("tables",t()):"selected"===i?(0,d.r5)("tables_selected",t()):"pull"===n?(0,d.FY)("prefixed_tables",t()):(0,d._P)("this_prefixed_tables",t()),s=l.filter((function(e){return c.includes(e)}));break;case"backup_manual_select":s="push"===n?(0,d.FY)("tables",t()):(0,d._P)("this_tables",t())}return s=e((0,_.O)("wpmdbBackupTables",s,a))}},j=function(e){return(0,l.m)(u.ti,e)}},42714:function(e,t,n){"use strict";n.d(t,{I6:function(){return m},KO:function(){return d},M7:function(){return u},Wv:function(){return h},fP:function(){return p},gF:function(){return b},sw:function(){return f},v_:function(){return g}});var r=n(27166),a=n(33032),i=n(12544),s=n.n(i),o=n(4516),l=n(66055),c=n(66866);function u(e,t){var n=0;for(var r in e)s()(t,r)&&(n+=parseInt(e[r]));return n}var p=function(e,t){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(a,i){var s,l;return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("import"!==e){t.next=8;break}return t.next=3,Promise.resolve().then(n.bind(n,67821));case 3:return s=t.sent,l=s.selectFromImportData,t.abrupt("return",l("table_sizes",i()));case 8:if("pull"===e){t.next=10;break}return t.abrupt("return",(0,o._P)("this_table_sizes",i()));case 10:return t.abrupt("return",(0,o.FY)("table_sizes",i()));case 11:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()},d=function(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n,a){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("push"===e){t.next=2;break}return t.abrupt("return",(0,o._P)("this_table_sizes",a()));case 2:return t.abrupt("return",(0,o.FY)("table_sizes",a()));case 3:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()};function m(e){return function(t,n){return t((0,l.m)(c.tO,e)),e}}function f(){return function(e,t){var n=(0,o.xg)("document_title",t());document.title=n}}function _(e){return function(t,n){var r=(0,o.r5)("stages",n()),a=(0,o.r5)("current_stage",n()),i=(0,o.xg)("total_stage_size",n()),s=r.length;a="migrate"===a?"tables":a;var u=r.findIndex((function(e){return e===a}))+1,p=" ".concat(u," of ").concat(s),d=(0,o.xg)("document_title",n()),m=Math.floor(e/i*100)||0;return document.title="".concat(m,"% Stage ").concat(p," - ").concat(d),t((0,l.m)(c.QH,e))}}function g(){return function(e,t){e((0,l.m)(c.hg))}}function b(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a,i){var s,l,c;return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:s=e>1e3?Math.ceil(e):e,l=parseInt(s),Number.isInteger(l)&&l>=1&&(s=l),c=parseInt((0,o.xg)("stage_size",i())),t?c=parseInt(e):c+=s,a(_(c));case 6:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}function h(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n,a){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:(0,o.xg)("total_stage_size",a()).length||n(m(e));case 2:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}},62457:function(e,t,n){"use strict";n.d(t,{U:function(){return d},m:function(){return p}});var r=n(27166),a=n(33032),i=n(62295),s=n(42233),o=n(66055),l=n(66866),c=n(38906),u=n(29942);function p(e){return function(t){(0,i.dC)((function(){t((0,o.m)(l.V$,(0,s.__)("Migration Failed","wp-migrate-db"))),t((0,o.m)(l.cE,e)),t(d())})),(0,u.Tc)(e)}}var d=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,c.N6)()),t((0,c.sw)());case 2:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}},38068:function(e,t,n){"use strict";n.d(t,{N6:function(){return c},Xg:function(){return l},hI:function(){return p},yo:function(){return u}});var r=n(27166),a=n(33032),i=n(4516),s=n(66055),o=n(66866),l=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,l,c;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:a=(0,i.xg)("timer",n()),l={on:!0,time:a.time,start:Date.now()-a.time},t((0,s.m)(o.G,l)),c=setInterval((function(){var e=(0,i.xg)("timer",n()),r=Date.now()-e.start;t((0,s.m)(o.cJ,r))}),1e3),t((0,s.m)(o.hS,c));case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},c=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,s.m)(o.c4,!1)),a=(0,i.xg)("timer",n()).timer_instance,clearInterval(a);case 3:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},u=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,s.m)(o.sr,0)),t((0,s.m)(o.cJ,0));case 2:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},p=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t(u()),t(l());case 2:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}},38906:function(e,t,n){"use strict";n.d(t,{p_:function(){return T.p_},BG:function(){return Z.BG},uk:function(){return T.uk},h8:function(){return T.h8},kK:function(){return N},QK:function(){return M},y:function(){return k},KO:function(){return S.KO},fP:function(){return S.fP},Ac:function(){return w},M7:function(){return S.M7},Up:function(){return b},Uo:function(){return P.U},Zk:function(){return R},hI:function(){return O.hI},sw:function(){return S.sw},v_:function(){return S.v_},yo:function(){return O.yo},Z6:function(){return T.Z6},Am:function(){return T.Am},z6:function(){return T.z6},m7:function(){return P.m},Wv:function(){return S.Wv},Xg:function(){return O.Xg},N6:function(){return O.N6},H_:function(){return x},gF:function(){return S.gF}});var r=n(27166),a=n(33032),i=n(12544),s=n.n(i),o=n(29942),l=n(18489),c=(0,n(56802).P1)([function(e){return e.migrations.current_migration},function(e){return e.migrations.connection_info},function(e){return e.migrations.search_replace}],(function(e,t,n){var r=e.intent,a=(0,l.Z)({},n);s()(["push","pull","import","savefile"],r)||(delete a.standard_search_replace,delete a.standard_options_enabled,delete a.standard_search_visible);var i={current_migration:e,search_replace:a};return s()(["push","pull"],r)&&(i.connection_info=t),i})),u=n(4516),p=n(61358),d=n(86191),m=n(66866),f=n(66055),_=n(29950),g=n(22497),b=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,i){var s,l,b,E,w,y,x,k;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t(function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,S.v_)()),t((0,f.m)(m.u9)),t((0,f.m)(_.GG,[])),t((0,O.hI)()),a=(0,d.d)("current_profile",n()),t((0,p.uJ)(a));case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),s=c(i()),l=(0,u.NR)("current_migration",i()),b=(0,u.NR)("connection_info",i()),E=(0,u.r5)("intent",i()),w=h(l,E),y=(0,u.r5)("stages",i()),x={intent:E,form_data:JSON.stringify(s),stage:w,stages:JSON.stringify(y),site_details:{local:(0,u.NR)("local_site",i()).site_details}},(x=v(E,x,b,i)).site_details=JSON.stringify(x.site_details),x=t((0,g.O)("intiateMigrationPostData",x)),e.prev=11,e.next=14,(0,o.op)("/initiate-migration",x);case 14:k=e.sent,t(function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t){var a,i;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(0,o.Yu)()){e.next=7;break}return e.next=3,Promise.resolve().then(n.bind(n,44789));case 3:return a=e.sent,i=a.TrackMigrationStart,e.next=7,t(i());case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()),e.next=22;break;case 18:return e.prev=18,e.t0=e.catch(11),t((0,P.m)({error_type:m.gF,error_message:e.t0})),e.abrupt("return",!1);case 22:if(k.success){e.next=26;break}return t((0,P.m)({error_type:m.gF,error_message:k.data})),t((0,P.U)()),e.abrupt("return",!1);case 26:return"savefile"===E&&t((0,f.m)("SET_FSE_DUMP_FILENAME",k.data.dump_filename)),e.abrupt("return",w);case 28:case"end":return e.stop()}}),e,null,[[11,18]])})));return function(t,n){return e.apply(this,arguments)}}()};function h(e,t){var n="migrate";return e.databaseEnabled&&"none"!==e.backup_option&&"backup_local"!==t?n="backup":"import"===t&&"none"===e.backup_option&&(n="upload"),n}function v(e,t,n,r){if(s()(["push","pull"],e)){t.url=n.connection_state.url,t.key=n.connection_state.key;var a=(0,u.NR)("remote_site",r());t.site_details.remote=a.site_details,t.temp_prefix=a.temp_prefix}else if("import"===e){var i=(0,u.FY)("import_gzipped",r());t.import_info={import_gzipped:JSON.stringify(i)}}else"backup_local"===e&&(t.intent="savefile");return t}var E=n(42233),w=function(e,t){switch(e){case"backup":return(0,E.__)("Backing up","wp-migrate-db");case"find_replace":return(0,E.__)("Searching table","wp-migrate-db");case"migrate":if("backup_local"===t)return(0,E.__)("Saving","wp-migrate-db")}return(0,E.__)("Transferring","wp-migrate-db")},y=function(e){return function(t){return t((0,f.m)(m.uj,e))}},x=function(e,t,n,r,a){return function(i,s){var l,c=n[e],p=t[c],d=(0,u.r5)("current_stage",s()),m=(0,u.xg)("item_progress",s());a=parseInt(a);var f=(0,u._P)("this_table_rows",s()),_=null;(0,o.Yu)()&&(_=(0,u.FY)("table_rows",s())),l=f,"pull"===r&&"backup"===d?l=f:"pull"===r||"push"===r&&"backup"===d?l=_:"push"===r&&(l=f);var g=function(e,t,n,r,a){var i=e[t]||0,s=n[t],o=r/parseInt(s);o>1&&(o=1),-1===r&&(o=1);var l,c=a*o;return-1===r?(l=Math.ceil(parseInt(a))-Math.ceil(i),{estTransferred:c,totalTransferred:Math.ceil(l)}):{estTransferred:c,totalTransferred:l=c-i}}(m,c,l,a,p),b=g.estTransferred,h=g.totalTransferred;if(-1===a)return i(y({item:c,progress:1})),m[c]?i((0,S.gF)(h)):i((0,S.gF)(p));i(y({item:c,progress:b})),i((0,S.gF)(h))}};function k(e,t,n){return function(e,t){var r={};"undefined"!==typeof n.dump_filename&&(r.dump_filename=n.dump_filename),"undefined"!==typeof n.dump_path&&(r.dump_path=n.dump_path),"undefined"!==typeof n.full_site_export&&(r.full_site_export=n.full_site_export),"undefined"!==typeof n.export_path&&(r.export_path=n.export_path),e((0,f.m)(m.DK,r))}}var Z=n(58900),T=n(3460),S=n(42714);function N(){return function(e,t){var n=(0,u.r5)("intent",t()),r=(0,u.r5)("databaseEnabled",t()),a=(0,u.r5)("backup_option",t()),i=[];switch(n){case"push":case"pull":r&&"none"!==a&&i.push("backup"),r&&i.push("tables");break;case"find_replace":"none"!==a&&i.push("backup"),i.push("tables");break;case"import":"none"!==a&&i.push("backup"),i.push("upload"),i.push("import"),i.push("find_replace");break;case"savefile":r&&i.push("tables");break;case"backup_local":i.push("tables")}i=e((0,g.O)("addMigrationStages",i)),e((0,f.m)(_.oK,i))}}var P=n(62457),O=n(38068),A=n(83115);function C(e,t){return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if("backup"===e){n.next=6;break}return n.next=3,a((0,S.fP)(t));case 3:n.t0=n.sent,n.next=9;break;case 6:return n.next=8,a((0,S.KO)(t));case 8:n.t0=n.sent;case 9:return n.abrupt("return",n.t0);case 10:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}var R=function e(t,n,i){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";return function(){var l=(0,a.Z)((0,r.Z)().mark((function a(l,p){var d,g,b,h,v,y,Z,N,O,R,M,F,L,B,U,j,z;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return d=i||0,g=(0,u.r5)("intent",p()),b=(0,u.r5)("current_stage",p()),h=(0,u.r5)("backup_tables_selected",p()),v=(0,u.r5)("backup_option",p()),r.next=7,l(C(b,g));case 7:return y=r.sent,r.next=10,(0,u.Au)(p());case 10:if(Z=r.sent,"backup"===b&&"backup_manual_select"===v&&(Z=h),l((0,S.Wv)((0,S.M7)(y,Z))),N=c(p()),!(d>=Z.length)){r.next=38;break}if("upload"!==b){r.next=19;break}d=0,r.next=38;break;case 19:if("backup"!==b){r.next=37;break}if(l((0,f.m)(_.sP,"backup")),l((0,S.v_)()),!(0,u.do)(p())){r.next=27;break}return l((0,f.m)(_.f7,"migrate")),r.abrupt("return",l(D("migrate")));case 27:if("find_replace"!==g){r.next=32;break}return l((0,f.m)(_.f7,"find_replace")),r.abrupt("return",l(D("find_replace")));case 32:if("import"!==g){r.next=34;break}return r.abrupt("return",l((0,T.Z6)(T.uk)));case 34:d=0,r.next=38;break;case 37:return r.abrupt("return",Promise.resolve("success"));case 38:return O=0,d===Z.length-1&&(O=1),R=(0,u.do)(p())?1:0,M=w(b,g),l((0,f.m)(m.V$,(0,E.gB)("<span>%s</span>&nbsp;<b>%s</b>",M,I(Z[d],g)))),F={table:Z[d],stage:(0,u.r5)("current_stage",p()),form_data:JSON.stringify(N),current_row:t,last_table:O,primary_keys:n,gzip:R,nonce:window.wpmdb_data.nonces.migrate_table,action:"wpmdb_migrate_table"},B=performance.now(),r.prev=45,r.next=48,(0,o.oj)(F);case 48:L=r.sent,r.next=56;break;case 51:return r.prev=51,r.t0=r.catch(45),console.error(r.t0),l((0,P.m)({error_type:m.gF,error_message:r.t0})),r.abrupt("return",!1);case 56:if(L.success){r.next=59;break}return l((0,P.m)({error_type:m.gF,error_message:L.data})),r.abrupt("return",!1);case 59:return(U=L.data.replace_data?JSON.parse(L.data.replace_data):null)&&l((0,A._s)({table:Z[d],data:U,time:performance.now()-B,executed:!0})),j=L.data,l(x(d,y,Z,g,j.current_row)),-1===parseInt(j.current_row)&&(d++,j.current_row="",j.primary_keys=""),1!==O||"savefile"!==g&&"backup_local"!==g||l(k(0,0,j)),z=[j.current_row,j.primary_keys,d],s.length&&z.push(s),r.next=69,l((0,T.Z6)("MIGRATE",[{fn:e,args:z}],s));case 69:return r.abrupt("return",r.sent);case 70:case"end":return r.stop()}}),a,null,[[45,51]])})));return function(e,t){return l.apply(this,arguments)}}()},I=function(e,t){return"import"===t?e.replace(/_mig_/,""):e},D=function(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n,a){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n((0,T.Z6)("MIGRATE",[{fn:R,args:[-1,"",0]}],e)));case 1:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()},M=function(e,t){return new Promise((function(n,r){window.setTimeout((function(){return n(e())}),t)}))}},68424:function(e,t,n){"use strict";n.d(t,{A_:function(){return m},Ax:function(){return d},Rp:function(){return f}});var r=n(27166),a=n(33032),i=n(42233),s=n(66055),o=n(666),l=n(29942),c=n(47895),u={message:(0,i.gB)((0,i.__)('<a href="%s" target="_blank" rel="noopener noreferrer">Having trouble connecting to the REST API</a>, please ensure that it has not been disabled or altered.',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/rest-api-errors"),id:"wpmdb_rest_inactive"};function p(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n){return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n((0,s.m)(o.Kf,{key:e})));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}function d(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t((0,s.m)(o.Zz,"licence_expired")),t((0,s.m)(o.Zz,"not_activated")),t((0,s.m)(o.Zz,"activation_deactivated")),t((0,s.m)(o.Zz,"wpmdb_invalid_license"));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}function m(){return function(e){var t,n;e((t="wpmd_rest_inactive",n=u,function(){var e=(0,a.Z)((0,r.Z)().mark((function e(a){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",a((0,s.m)(o.Dv,{key:t,value:n})));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())),e((0,s.m)(o.e2,"wpmdb_rest_inactive"))}}function f(e,t,n){return function(){var i=(0,a.Z)((0,r.Z)().mark((function a(i,s){return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:i((0,c.Tf)(e,!0,o.k0)),i((0,c.q$)(e,o.$V)),(0,l.op)("/process-notice-link",{notice:e,type:t,reminder:n}).then((function(e){e.success})).catch((function(e){console.error(e)})),i(p(e));case 4:case"end":return r.stop()}}),a)})));return function(e,t){return i.apply(this,arguments)}}()}},22633:function(e,t,n){"use strict";n.d(t,{$f:function(){return P},$z:function(){return _},G7:function(){return h},H5:function(){return b},I4:function(){return y},LX:function(){return w},OW:function(){return k},SO:function(){return v},WJ:function(){return N},Xl:function(){return E},er:function(){return x},ii:function(){return g},nW:function(){return S},qb:function(){return T},rC:function(){return Z}});var r=n(31125),a=n(10734),i=n.n(a),s=(n(49736),n(66055)),o=n(41459),l=n(14251),c=n(4669),u=n(76178),p=n(29950),d=n(22497),m=n(74094),f=n(34653),_=["tables","backups","post_types","advanced_options","standard_fields","custom_fields"];function g(e){return function(t){t({type:o.fR,payload:e})}}function b(e){return{type:o.h6,payload:e}}function h(e){return function(t){var n=(0,r.Z)(e);t({type:o.ig,payload:n})}}function v(e){return function(t,n){if("database"!==e)return t({type:o.kW,payload:e})}}function E(){return function(e,t){var n=(0,u.O)("panelsOpen",t()),a=(0,u.O)("registeredPanels",t()),i=(0,r.Z)(n);return i=n.some((function(e){return _.includes(e)&&a.includes(e)}))?i.filter((function(e){return!_.includes(e)})):[].concat((0,r.Z)(i),_).filter((function(e){return a.includes(e)})),e((0,s.m)(o.ig,i)),!1}}function w(e){return function(t,n){t({type:o.kW,payload:e})}}function y(e){return(0,s.m)(o.q2,e)}function x(e){return function(t,n){if("database"!==e)return t((0,s.m)(o._H,[e]));var r=(0,u.O)("panelsOpen",n()).filter((function(e){return _.includes(e)||"database"===e}));t((0,s.m)(o._H,r))}}function k(e){return(0,s.m)(o.pE,e)}function Z(e,t){return function(n,a){var i=null;"undefined"!==typeof t&&(i=(0,r.Z)(t)),n({type:o.Nv,payload:e,panelPayload:i})}}var T=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(a,s){var o=s().panels.panelsToDisplay,l=(0,r.Z)(o);-1===i()(l,e)&&l.push(e),a(Z(l)),t&&n&&a(b({parent:t,title:n}))}},S=function(e){return function(t,n){var a=n().panels.panelsToDisplay,s=(0,r.Z)(a);e.forEach((function(e){-1===i()(s,e)&&s.push(e)})),t(Z(s))}},N=function(e){return function(t,n){var a=n().panels.panelsToDisplay,s=(0,r.Z)(a),o=i()(s,e);-1!==o&&(s.splice(o,1),t(Z(s)))}};function P(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(n,r){var a=r(),i=n((0,m.J)());t||n((0,l.oA)());var o=e.panel,u=e.intent;if(t||n((0,s.m)(p.F3,function(e){switch(e){case"savefile":return["gzip_file","replace_guids","exclude_transients"];case"find_replace":case"push":case"pull":return["replace_guids","exclude_transients"];case"backup_local":return["exclude_spam","exclude_transients","gzip_file"]}return[]}(u))),t||!i&&u!==a.migrations.current_migration.intent){n((0,d.Kw)("addonActions"));var _=n((0,d.O)("addonPanels",[],u)),g=n((0,d.O)("addonPanelsOpen",[o,"custom_fields"],u));_.push(o);"savefile"===u&&["media_files","theme_plugin_files"].forEach((function(e){return _.push(e)})),n(Z(_,g));["savefile","backup_local","find_replace"].includes(u)&&n((0,l.I8)(!0)),t||(n((0,l.CV)({intent:u})),n((0,c.EX)(u))),["savefile","find_replace"].includes(u)&&n((0,f.addonsLoaded)())}}}},52650:function(e,t,n){"use strict";n.d(t,{a:function(){return _},b:function(){return f}});var r=n(31125),a=n(89472),i=n(88368),s=n(96480),o=n.n(s),l=n(4516),c=n(76178),u=n(41459),p=n(14251),d=n(22497),m=n(29942);function f(){return function(e,t){e((0,p.Tf)("")),e((0,p.j8)(o()()));var n=(0,l.Jm)("custom_search_replace",t()),s=(0,l._P)("this_url",t()),f=(0,l._P)("this_path",t()),_=(0,l.r5)(["post_types_option","post_types_selected","backup_option","backup_tables_selected","tables_option","tables_selected","databaseEnabled","intent"],t()),g=(0,i.Z)(_,8),b=g[0],h=g[1],v=g[2],E=g[3],w=g[4],y=g[5],x=g[6],k=g[7],Z=t(),T=Z.media_files,S=Z.theme_plugin_files,N=Z.migrations,P=(0,c.O)("panelsOpen",t()),O=(0,l.r5)("status",t())||[];if(""===O&&(O=[]),x){if(n.length){var A,C=(0,a.Z)(n);try{for(C.s();!(A=C.n()).done;){var R=A.value;if(""===R.replace_old&&""!==R.replace_new||[(0,m.Ph)(s),f].includes(R.replace_old)&&""===R.replace_new){O.push({name:"COMMON_SEARCH_REPLACE_EMPTY",panel:"custom_fields"});break}if(R.regex&&!R.isValidRegex){O.push({name:"COMMON_SEARCH_REPLACE_INVALID_REGEX",panel:"custom_fields"});break}}}catch(M){C.e(M)}finally{C.f()}}"selected"===b&&0===h.length&&O.push({name:"POST_TYPES_SELECTED_EMPTY",panel:"post_types"}),"backup_manual_select"===v&&0===E.length&&O.push({name:"BACKUP_TABLES_SELECTED_EMPTY",panel:"backups"}),"selected"===w&&0===y.length&&O.push({name:"TABLES_SELECTED_EMPTY",panel:"tables"})}var I=[];if(["push","pull","savefile"].includes(k)&&(T&&!0===T.enabled&&I.push("media"),S)){var D=function(e,t){var n=e.other_files,r=e.plugin_files,a=e.muplugin_files,i=e.theme_files,s=e.core_files,o=t.others,l=t.plugins,c=t.muplugins,u=t.themes,p=t.core,d=[];n&&!0===n.enabled&&Object.keys(o).length>0&&d.push("others");r&&!0===r.enabled&&Object.keys(l).length>0&&d.push("plugins");a&&!0===a.enabled&&Object.keys(c).length>0&&d.push("muplugins");i&&!0===i.enabled&&Object.keys(u).length>0&&d.push("themes");s&&!0===s.enabled&&Object.keys(p).length>0&&d.push("core");return d}(S,"pull"===k?N.remote_site.site_details:N.local_site.site_details);I.push.apply(I,(0,r.Z)(D))}return!0===x&&I.push("database"),0===I.length&&O.push({name:"EMPTY_MIGRATION_STAGES",panel:"submit"}),!((O=e((0,d.O)("wpmdbPreMigrationCheck",O))).length>0)||(e((0,p.Tf)(O)),O.forEach((function(t){P.includes(t.panel)||e({type:u.kW,payload:t.panel})})),!1)}}function _(e,t){if(!e||"object"!==typeof e||!e.length)return!1;var n=!1;return e.forEach((function(e){e.name===t&&(n=!0)})),n}},61358:function(e,t,n){"use strict";n.d(t,{qU:function(){return A},hV:function(){return L},UF:function(){return M},uJ:function(){return R},Vu:function(){return U},NF:function(){return I},qH:function(){return B},F9:function(){return j},zD:function(){return O},Tv:function(){return D}});var r,a=n(27166),i=n(33032),s=n(18489),o=n(4665),l=n(50029),c=n.n(l),u=n(70659),p=n.n(u),d=n(27114),m=n(29942),f=n(66055),_=n(22973),g=n(29950),b=n(17186),h=n(30348);function v(){return v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},v.apply(this,arguments)}n.p;var E,w=(0,h.ZP)((function(e){return o.createElement("svg",v({style:{animationFillMode:"forwards",animationIterationCount:1},xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",id:"el_0iWebJDPz"},e),r||(r=o.createElement("style",null,"@keyframes kf_el_mS-6SGLslI_an_V1DkmYpQ2{0%,50%{opacity:0}53.33%,to{opacity:1}}@keyframes kf_el_flrbQh1w8k_an_9eUz96drwv{0%,to{stroke-dasharray:59.7}}@keyframes kf_el_flrbQh1w8k_an_C4kYjD6bN{0%{stroke-dashoffset:59.7}50%,to{stroke-dashoffset:0}}@keyframes kf_el_mS-6SGLslI_an_bM1FLjjf73{0%,50%,to{stroke-dasharray:11.74}}@keyframes kf_el_mS-6SGLslI_an_fdLET0VVs{0%,50%{stroke-dashoffset:11.74}to{stroke-dashoffset:0}}#el_0iWebJDPz *{-webkit-animation-duration:1s;animation-duration:1s;-webkit-animation-iteration-count:1;animation-iteration-count:1;-webkit-animation-timing-function:cubic-bezier(0,0,1,1);animation-timing-function:cubic-bezier(0,0,1,1)}")),o.createElement("g",{fillRule:"evenodd",style:{stroke:"#fff",fill:"none",WebkitTransform:"translate(2px,2px)",transform:"translate(2px,2px)"}},o.createElement("path",{style:{strokeWidth:2,WebkitAnimationFillMode:"forwards,forwards,forwards",animationFillMode:"forwards,forwards,forwards",strokeDashoffset:11.74,WebkitAnimationName:"kf_el_mS-6SGLslI_an_fdLET0VVs,kf_el_mS-6SGLslI_an_bM1FLjjf73,kf_el_mS-6SGLslI_an_V1DkmYpQ2",animationName:"kf_el_mS-6SGLslI_an_fdLET0VVs,kf_el_mS-6SGLslI_an_bM1FLjjf73,kf_el_mS-6SGLslI_an_V1DkmYpQ2",WebkitAnimationTimingFunction:"cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1)",animationTimingFunction:"cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1)",strokeDasharray:11.74},d:"M6 10.5 9.2 13 14 7"}),o.createElement("circle",{cx:10,cy:10,r:9.5,style:{fillOpacity:0,WebkitAnimationFillMode:"forwards,forwards",animationFillMode:"forwards,forwards",strokeDashoffset:59.7,WebkitAnimationName:"kf_el_flrbQh1w8k_an_C4kYjD6bN,kf_el_flrbQh1w8k_an_9eUz96drwv",animationName:"kf_el_flrbQh1w8k_an_C4kYjD6bN,kf_el_flrbQh1w8k_an_9eUz96drwv",WebkitAnimationTimingFunction:"cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1)",animationTimingFunction:"cubic-bezier(0,0,1,1),cubic-bezier(0,0,1,1)",strokeDasharray:59.7}})))}))(E||(E=(0,b.Z)(["\n  #el_aEQFk8pHYY {\n    stroke: #fff;\n  }\n  width: 30px;\n"]))),y=n(47585);function x(e){var t=e.migrations,n=t.current_migration,r=t.connection_info,a=t.search_replace,i=n;r=(0,m.Yu)()?(0,s.Z)((0,s.Z)({},r),{},{status:(0,s.Z)({},y.A)}):void 0;var o={current_migration:n=(0,s.Z)((0,s.Z)({},i),{},{intent:i.intent,status:"",current_stage:"",stages:[],selected_existing_profile:null,running:!1,migration_enabled:!1}),connection_info:r,search_replace:a,media_files:e.media_files,theme_plugin_files:e.theme_plugin_files,multisite_tools:e.multisite_tools};return JSON.stringify(o)}function k(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(n,r){var a=x(r());return{name:e,value:a,guid:c()(),fromRecent:t}}}function Z(e){return function(t,n){return t({type:_._n,payload:e}),n()}}function T(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r,i){return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r((0,f.m)(_.Cp,"Save")),r((a={data:{location:e,message:(0,d.Y)(t)}},(0,f.m)(_.Gg,a))),n.abrupt("return",!1);case 3:case"end":return n.stop()}var a}),n)})));return function(e,t){return n.apply(this,arguments)}}()}function S(e){return(0,f.m)(_.qk,e)}function N(){return(0,f.m)(_.a)}function P(){return(0,f.m)(_.aC)}function O(e){return(0,f.m)(g.NS,{id:e,type:"saved"})}function A(e){return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n,r){var i,s,o;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return i=n(k(e.name)),n(N()),t.prev=2,t.next=5,(0,m.op)("/save-profile",i,!1,n);case 5:s=t.sent,t.next=11;break;case 8:return t.prev=8,t.t0=t.catch(2),t.abrupt("return",n(T("migration",t.t0)));case 11:if(s.success){t.next=13;break}return t.abrupt("return",n(T("migration",s)));case 13:return s.success&&n((0,f.m)(_.Cp,"success")),n(Z({name:e.name,guid:i.guid,id:s.data.id})),o={profile_saved:!0},n((0,f.m)(g.eZ,o)),n({type:_.bQ,payload:{id:s.data.id,type:"saved"}}),t.abrupt("return",s);case 19:case"end":return t.stop()}}),t,null,[[2,8]])})));return function(e,n){return t.apply(this,arguments)}}()}function C(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r,i){var s;return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r(N()),n.prev=1,n.next=4,(0,m.op)("/save-profile",t,!1,r);case 4:s=n.sent,n.next=11;break;case 7:return n.prev=7,n.t0=n.catch(1),console.error(n.t0),n.abrupt("return",r(T("profile",n.t0)));case 11:if(s.success){n.next=13;break}return n.abrupt("return",r(T("profile",s)));case 13:return r(Z({name:e.name,guid:t.guid,id:s.data.id})),r((0,f.m)(_.Cp,"Save")),n.abrupt("return",s);case 16:case"end":return n.stop()}}),n,null,[[1,7]])})));return function(e,t){return n.apply(this,arguments)}}()}function R(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n,r){var i,s,o,l,c;return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=r(),s=i.migrations.current_migration,o=s.profile_name,null===s.profile_type){t.next=4;break}return t.abrupt("return",null);case 4:return(l=n(k(o))).id=e,n(P()),t.prev=7,t.next=10,(0,m.op)("/unsaved-profile",l,!1,n);case 10:c=t.sent,t.next=16;break;case 13:return t.prev=13,t.t0=t.catch(7),t.abrupt("return",n(T("migration",t.t0)));case 16:if(c.success){t.next=18;break}return t.abrupt("return",n(T("migration",c)));case 18:if("not saved"!==c.data){t.next=20;break}return t.abrupt("return",c);case 20:return n({type:_.YQ,payload:c.data}),t.abrupt("return",c);case 22:case"end":return t.stop()}}),t,null,[[7,13]])})));return function(e,n){return t.apply(this,arguments)}}()}var I=function(e,t,n){return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i,s){var o;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return i(P()),r.next=3,(0,m.op)("/remove-recent-migration",{id:e});case 3:if((o=r.sent).success){r.next=6;break}return r.abrupt("return",i(T("profile",o)));case 6:i({type:_.N2,payload:{index:t,slice:n}});case 7:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}()};function D(e){return{type:_.xv,payload:e}}function M(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r,i){var o,l,c,u;return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return o=i(),l=o.profiles.recent,c=p()(l,{id:e}),u=(0,s.Z)((0,s.Z)({},c),{},{fromRecent:"true"}),n.next=6,r(C({name:c.name},u));case 6:n.sent.success&&(r(I(e,t,"recent")),r(D(o.profiles.saved.length)));case 8:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}function F(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r){return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:r({type:_.hm,payload:{index:e,text:t}});case 1:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function L(e,t,n){return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i){var s;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return i((0,f.m)(_.tT)),r.prev=1,r.next=4,(0,m.op)("/rename-profile",{guid:e,name:n});case 4:s=r.sent,r.next=10;break;case 7:return r.prev=7,r.t0=r.catch(1),r.abrupt("return",i(T("profile",r.t0)));case 10:if(s.success){r.next=12;break}return r.abrupt("return",i(T("profile",s)));case 12:i(F(t,n)),i(D(t));case 14:case"end":return r.stop()}}),r,null,[[1,7]])})));return function(e){return r.apply(this,arguments)}}()}var B=function(e,t,n){return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i){var s;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return i((0,f.m)(_.PM)),r.prev=1,r.next=4,(0,m.op)("/remove-profile",{guid:e,name:n});case 4:s=r.sent,r.next=10;break;case 7:return r.prev=7,r.t0=r.catch(1),r.abrupt("return",i(T("profile",r.t0)));case 10:if(s.success){r.next=12;break}return r.abrupt("return",i(T("profile",s)));case 12:return i({type:_.N2,payload:{index:t,slice:"saved"}}),r.abrupt("return",!0);case 14:case"end":return r.stop()}}),r,null,[[1,7]])})));return function(e){return r.apply(this,arguments)}}()};function U(e,t){return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(r,i){var s,o,l,c;return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return s=i(),o=x(s),r(S(!0)),l={contents:o,guid:e},n.prev=4,n.next=7,(0,m.op)("/overwrite-profile",l);case 7:c=n.sent,n.next=13;break;case 10:return n.prev=10,n.t0=n.catch(4),n.abrupt("return",r(T("migration",n.t0)));case 13:if(c.success){n.next=15;break}return n.abrupt("return",r(T("migration",c)));case 15:r(S(!1)),c.success&&r((0,f.m)(_.Cp,"success")),r((0,f.m)(g.eZ,{profile_saved:!0})),r({type:_.Qu,payload:t});case 19:case"end":return n.stop()}}),n,null,[[4,10]])})));return function(e,t){return n.apply(this,arguments)}}()}var j=function(e,t){return function(n){var r=e.btn_text;return"success"===r?(setTimeout((function(){var e;n((e="Save",function(){var t=(0,i.Z)((0,a.Z)().mark((function t(n,r){return(0,a.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n((0,f.m)(_.Cp,e));case 1:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()))}),2e3),o.createElement(w,{className:"success",onAnimationEnd:function(){t(!1)}})):r}}},4669:function(e,t,n){"use strict";n.d(t,{CN:function(){return o},EX:function(){return d},FL:function(){return u},GJ:function(){return c},Kf:function(){return i},Px:function(){return p},mX:function(){return a},rL:function(){return s},zf:function(){return l}});var r=n(66441);function a(e){return function(t){t({type:r.oK,payload:e})}}function i(e){return function(t){t({type:r.G3,payload:e})}}function s(e){return function(t,n){t({type:r.O9,payload:e})}}function o(e){return function(t,n){t({type:r.ci,payload:e})}}function l(e){return{type:r.IY,payload:e}}function c(e){return{type:r.Q0,payload:e}}function u(e){return{type:r.hx,payload:e}}function p(e){return{type:r.zi,index:e}}function d(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(n,a){var i=a(),s={intent:e,local_site:i.migrations.local_site,force_update:t};n({type:r.KM,payload:s})}}},87326:function(e,t,n){"use strict";n.d(t,{Lc:function(){return h},Xn:function(){return b},c9:function(){return m},m7:function(){return f},nE:function(){return _},rT:function(){return d}});var r=n(27166),a=n(33032),i=(n(62295),n(29942)),s=n(19826),o=n(66055),l=n(27114),c=n(666),u=n(47895);function p(e,t){return function(n,r){return n((0,o.m)(s.TE,{location:e,message:(0,l.Y)(t)})),!1}}function d(e){return function(t,n){t((0,u.q$)(e,s.eH))}}function m(e,t){return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a,i){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:a((0,u.Tf)(e,t,s.YQ));case 1:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}function f(e,t){return function(n){n((0,o.m)(s.km,{setting:e,value:t}))}}function _(e,t){return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:a(p(e,t)),a(m(e,"errored"));case 2:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}function g(e,t,s,l){return function(){var u=(0,a.Z)((0,r.Z)().mark((function a(u){var p;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return u(m(t,!0)),u(d(t)),r.prev=2,r.next=5,s();case 5:p=r.sent,r.next=11;break;case 8:return r.prev=8,r.t0=r.catch(2),r.abrupt("return",u(_(t,r.t0)));case 11:if(p.success){r.next=13;break}return r.abrupt("return",u(_(t,p)));case 13:if(u(m(t,"success")),u(f(t,e)),!l){r.next=24;break}r.t1=l,r.next="beta"===r.t1?19:"allow_tracking"===r.t1?22:24;break;case 19:if(!(0,i.Yu)()){r.next=21;break}return r.abrupt("return",Promise.resolve().then(n.bind(n,40882)).then((function(e){var t=e.betaOptionToggle;u(t())})));case 21:return r.abrupt("break",24);case 22:return u((0,o.m)(c.Kf,{key:"notice-enable-usage-tracking"})),r.abrupt("break",24);case 24:setTimeout((function(){u(m(t,!1))}),1500);case 25:case"end":return r.stop()}}),a,null,[[2,8]])})));return function(e){return u.apply(this,arguments)}}()}function b(e,t,n){return function(){var s=(0,a.Z)((0,r.Z)().mark((function s(o){return(0,r.Z)().wrap((function(s){for(;;)switch(s.prev=s.next){case 0:return s.abrupt("return",o(g(t,e,(0,a.Z)((0,r.Z)().mark((function n(){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",(0,i.op)("/save-setting",{setting:e,checked:t}));case 1:case"end":return n.stop()}}),n)}))),n)));case 1:case"end":return s.stop()}}),s)})));return function(e){return s.apply(this,arguments)}}()}function h(e){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n){var s;return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:n(g(s=1024*e*1024,"max_request",(0,a.Z)((0,r.Z)().mark((function e(){return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",(0,i.op)("/update-max-request",{max_request_size:s}));case 1:case"end":return e.stop()}}),e)})))));case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}},47585:function(e,t,n){"use strict";n.d(t,{A:function(){return r}});var r={auth_form:{username:"",password:""},show_auth_form:!1,connecting:!1,error:!1,error_msg:"",button_status:"disabled",ssl_notice:!1,pasted:!1,copy_to_remote:!1,prefix_mismatch:!1,mixed_case_table_name_warning:!1,show_mst_warning:!1,update_plugin_on_remote:!1,retry_over_http:!1}},29950:function(e,t,n){"use strict";n.d(t,{AV:function(){return j},Ac:function(){return Z},EF:function(){return S},F3:function(){return N},GG:function(){return F},IJ:function(){return z},KU:function(){return g},Ld:function(){return P},NL:function(){return W},NS:function(){return O},Nt:function(){return w},O$:function(){return h},S_:function(){return J},Ss:function(){return x},Um:function(){return A},W$:function(){return U},X4:function(){return H},Yl:function(){return v},Zz:function(){return K},av:function(){return y},b4:function(){return G},b_:function(){return B},d0:function(){return I},eZ:function(){return f},f7:function(){return R},ic:function(){return E},jD:function(){return M},mO:function(){return k},mu:function(){return b},n_:function(){return D},oK:function(){return C},sP:function(){return L},sr:function(){return V},tX:function(){return _},u2:function(){return T}});var r=n(31125),a=n(36222),i=n(18489),s=n(58319),o=n.n(s),l=n(33351),c=n.n(l),u=n(26429),p=n(29942),d=n(22973),m=n(66866),f="REPLACE_CURRENT_MIGRATION",_="SET_MIGRATION_CONNECTED",g="UPDATE_SELECTED_TABLES",b="UPDATE_TABLES_OPTION",h="UPDATE_BACKUPS_TABLES",v="UPDATE_BACKUPS_OPTION",E="UPDATE_POSTTYPES_SELECTED",w="UPDATE_POSTTYPES_OPTION",y="UPDATE_CURRENT_MIGRATION",x="UPDATE_ADVANCED_OPTIONS",k="UPDATE_SOURCE_PREFIX",Z="UPDATE_DESTINATION_PREFIX",T="UPDATE_TWO_MULTISITES",S="UPDATE_LOCAL_SOURCE",N="REPLACE_ADVANCED_OPTIONS",P="LOAD_PROFILE",O="SET_EXISTING_PROFILE",A="SET_MIGRATION_PREVIEW",C="SET_MIGRATION_STAGES",R="SET_CURRENT_STAGE",I="SET_STATUS",D="SET_MIGRATION_ENABLED",M="SET_MIGRATION_RUNNING",F="SET_STAGES_COMPLETE",L="SET_STAGE_COMPLETE",B="SET_MIGRATION_ID",U="SET_HIGH_PERFORMANCE_TRANSFERS_STATUS",j="TOGGLE_DATABASE_PANEL",z="SET_CURRENT_PAYLOAD_SIZE",G="INCREMENT_FILE_TRANSFER_COUNT",V="ADD_PAYLOAD_SIZE_HISTORY",H="SET_CURRENT_MAX_PAYLOAD_SIZE",W="ADD_HIGH_PERFORMANCE_TRANSFER_STAT",Y={connected:!1,intent:"",tables_option:"all",tables_selected:[],backup_option:"none",backup_tables_selected:[],post_types_option:"all",post_types_selected:[],advanced_options_selected:[],profile_name:"",selected_existing_profile:null,profile_type:null,status:{disabled:!1},stages:[],current_stage:"",stages_complete:[],running:!1,migration_enabled:!1,migration_id:null,source_prefix:"",destination_prefix:"",preview:!1,selectedComboOption:"preview",twoMultisites:!1,localSource:!0,databaseEnabled:!0,currentPayloadSize:0,currentMaxPayloadSize:null,fileTransferRequests:0,payloadSizeHistory:[],fileTransferStats:[],forceHighPerformanceTransfers:!0,fseDumpFilename:null},K=["replace_guids","exclude_spam","exclude_transients","keep_active_plugins","compatibility_older_mysql","gzip_file"];function q(e,t,n){return(0,i.Z)((0,i.Z)({},e),{},(0,a.Z)({},t,n))}var J=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Y,t=arguments.length>1?arguments[1]:void 0,n=(0,i.Z)({},e);return(0,u.ZP)(e,(function(a){var s;switch(t.type){case"RESET_APP":return Y;case"RESET_MIGRATION":return(0,i.Z)((0,i.Z)({},Y),{},{intent:e.intent});case f:return o()(a,t.payload);case y:return a.intent=t.payload.intent,a;case A:return a.preview=t.payload,a;case d.Qu:return a.profile_name=t.payload,a;case _:return a.connected=t.connected,a;case g:var l=(0,i.Z)((0,i.Z)({},e),{},{tables_selected:t.payload});return"migration"===e.backup_option&&c()(l,{backup_tables_selected:t.payload}),l;case k:return q(e,"source_prefix",t.payload);case Z:return q(e,"destination_prefix",t.payload);case T:return q(e,"twoMultisites",t.payload);case S:return q(e,"localSource",t.payload);case b:return q(e,"tables_option",t.payload);case h:return q(e,"backup_tables_selected",t.payload);case v:return q(e,"backup_option",t.payload);case E:return q(e,"post_types_selected",t.payload);case w:return q(e,"post_types_option",t.payload);case B:return q(e,"migration_id",t.payload);case x:return n=(0,p.XV)(e.advanced_options_selected,t.payload),(0,i.Z)((0,i.Z)({},e),{},{advanced_options_selected:n});case N:return(0,i.Z)((0,i.Z)({},e),{},{advanced_options_selected:t.payload});case P:var u=t.payload.profile.value.current_migration,K=u.intent;return(0,i.Z)((0,i.Z)({},u),{},{connected:!1,selected_existing_profile:t.payload.id,profile_type:t.payload.profile.value.profile_type,stages_complete:[],migration_enabled:!["push","pull"].includes(K),currentPayloadSize:0,fileTransferRequests:0,payloadSizeHistory:[]});case O:case d.bQ:return a.selected_existing_profile=t.payload.id,a.profile_type=t.payload.type,a;case"SET_MIGRATION_DISABLED":return(0,i.Z)((0,i.Z)({},a),{},{status:(0,i.Z)((0,i.Z)({},a.status),{},{disabled:t.payload})});case C:return(0,i.Z)((0,i.Z)({},a),{},{stages:t.payload});case R:return(0,i.Z)((0,i.Z)({},a),{},{current_stage:t.payload});case M:return(0,i.Z)((0,i.Z)({},a),{},{running:t.payload,preview:!1!==t.payload&&a.preview});case I:return(0,i.Z)((0,i.Z)({},a),{},{status:t.payload});case D:return(0,i.Z)((0,i.Z)({},a),{},{migration_enabled:t.payload});case"SET_CONNECTED":return(0,i.Z)((0,i.Z)({},a),{},{migration_enabled:!0});case L:return a.stages_complete.push(t.payload),a;case F:return(0,i.Z)((0,i.Z)({},a),{},{stages_complete:t.payload});case"MST_TOGGLE_ENABLED":return t.payload.enabled?a:q(e,"tables_option","all");case"SET_SELECTED_MIGRATION_COMBO_OPTION":return(0,i.Z)((0,i.Z)({},a),{},{selectedComboOption:t.payload});case j:return(0,i.Z)((0,i.Z)({},a),{},{databaseEnabled:!a.databaseEnabled});case m.u9:return(0,i.Z)((0,i.Z)({},a),{},{currentPayloadSize:0,fileTransferRequests:0,payloadSizeHistory:[],fileTransferStats:[]});case z:return(0,i.Z)((0,i.Z)({},a),{},{currentPayloadSize:t.payload});case H:return(0,i.Z)((0,i.Z)({},a),{},{currentMaxPayloadSize:t.payload});case V:return(0,i.Z)((0,i.Z)({},a),{},{payloadSizeHistory:[].concat((0,r.Z)(a.payloadSizeHistory?a.payloadSizeHistory:[]),[null!==(s=t.payload)&&void 0!==s?s:0])});case G:return a.fileTransferRequests?(0,i.Z)((0,i.Z)({},a),{},{fileTransferRequests:a.fileTransferRequests+1}):(0,i.Z)((0,i.Z)({},a),{},{fileTransferRequests:1});case U:var J,X,Q=!1;if("push"===a.intent)Q=null!==(J=t.payload.remote_site_mode)&&void 0!==J&&J;if("pull"===a.intent)Q=null!==(X=t.payload.local_site_mode)&&void 0!==X&&X;return(0,i.Z)((0,i.Z)({},a),{},{highPerformanceTransfersStatus:Q});case W:return(0,i.Z)((0,i.Z)({},a),{},{fileTransferStats:[].concat((0,r.Z)(a.fileTransferStats?a.fileTransferStats:[]),[t.payload])});case"SET_FORCE_HIGH_PERFORMANCE_TRANSFERS":return(0,i.Z)((0,i.Z)({},a),{},{forceHighPerformanceTransfers:t.payload});case"SET_FSE_DUMP_FILENAME":return(0,i.Z)((0,i.Z)({},a),{},{fseDumpFilename:t.payload});default:return e}}))}},15265:function(e,t,n){"use strict";n.d(t,{DQ:function(){return s},_b:function(){return o},xN:function(){return c}});var r=n(31125),a=n(18489),i=n(66866),s="ADD_DRY_RUN_RESULT",o="SET_CURRENTLY_PREVIEWED_DRY_RUN_ITEM",l={results:[],currentPreviewItem:null},c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,t=arguments.length>1?arguments[1]:void 0,n=(0,a.Z)({},e);switch(t.type){case s:var c=(0,r.Z)(n.results),u=c.findIndex((function(e){return e.table===t.payload.table}));if(-1!==u){var p=(0,a.Z)({},c[u]);p.data=p.data.concat(t.payload.data),p.count=p.data.length,p.time+=t.payload.time,p.executed=!0,c[u]=p}else c.push((0,a.Z)((0,a.Z)({},t.payload),{},{count:t.payload.data.length}));return(0,a.Z)((0,a.Z)({},n),{},{results:c});case o:return(0,a.Z)((0,a.Z)({},e),{},{currentPreviewItem:t.payload});case i.Jj:return"CANCEL_COMPLETE"===t.payload?(0,a.Z)({},l):(0,a.Z)({},e);default:return e}}},18066:function(e,t,n){"use strict";n.d(t,{TO:function(){return a},fK:function(){return i},l3:function(){return s}});var r=n(52089),a="ADD_ACTION",i="ADD_FILTER",s="REMOVE_FILTER",o=(0,r.Lq)({filters:[],actions:[]},{ADD_ACTION:function(e,t){return e.actions.push(t.payload),e},ADD_FILTER:function(e,t){return e.filters.push(t.payload),e},REMOVE_FILTER:function(e,t){var n=e.filters;return e.filters=n.filter((function(e){return e.name!==t.payload.name})),e}});t.ZP=o},66866:function(e,t,n){"use strict";n.d(t,{D$:function(){return O},DK:function(){return S},DL:function(){return P},Du:function(){return c},G:function(){return x},Jj:function(){return d},Lw:function(){return v},MG:function(){return h},QH:function(){return f},V$:function(){return l},Z:function(){return p},c4:function(){return E},cE:function(){return u},cJ:function(){return w},gF:function(){return s},hS:function(){return k},hg:function(){return g},mx:function(){return Z},ol:function(){return T},sr:function(){return y},tO:function(){return m},ti:function(){return N},u9:function(){return b},uj:function(){return _}});var r=n(18489),a=n(26429),i=n(29950),s="MIGRATION_ERROR_TYPE_FATAL",o={title:"",progress_message:"",progress:{},stages:[],migration_tables:[],status:"",progress_stage:"",total_stage_size:0,migration_size:0,stage_size:0,item_progress:{},timer:{on:!1,start:0,time:0,timer_instance:null},pause_before_finalize:!1,document_title:"",dump_info:{},allow_page_leave:!0,showDryRunResults:!1},l="SET_TITLE",c="SET_PROGRESS_MESSAGE",u="SET_MIGRATION_ERROR",p="SET_MIGRATION_TABLES",d="MIGRATION_STATUS",m="SET_STAGE_TOTAL_SIZE",f="SET_STAGE_SIZE",_="SET_ITEM_PROGRESS",g="RESET_STAGES",b="RESET_MIGRATION_SIZE",h="SET_PROGRESS_STAGE",v="RESET_PROGRESS_STAGE",E="SET_TIMER_ON",w="SET_TIMER_TIME",y="SET_TIMER_START",x="SET_TIMER",k="SET_TIMER_INSTANCE",Z="SET_PAUSE_BEFORE_FINALIZE",T="SET_DOCUMENT_TITLE",S="SET_DUMP_INFO",N="SET_ALLOW_PAGE_LEAVE",P="SET_SHOW_DRY_RUN_RESULTS",O=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return o;case i.jD:return!0===t.payload?(0,r.Z)((0,r.Z)({},n),{},{status:"",showDryRunResults:!1}):e;case l:return(0,r.Z)((0,r.Z)({},n),{},{title:t.payload});case c:return(0,r.Z)((0,r.Z)({},n),{},{progress_message:t.payload});case"SET_PROGRESS":return(0,r.Z)((0,r.Z)({},n),{},{progress:t.payload});case u:return(0,r.Z)((0,r.Z)({},n),{},{status:(0,r.Z)((0,r.Z)({},n.status),{},{error_type:t.payload.error_type,error_message:t.payload.error_message})});case p:return(0,r.Z)((0,r.Z)({},n),{},{migration_tables:t.payload});case h:return(0,r.Z)((0,r.Z)({},n),{},{progress_stage:t.payload});case"REMOVE_MIGRATION_TABLE":return n.migration_tables.splice(t.payload,1),n;case d:return(0,r.Z)((0,r.Z)({},n),{},{status:t.payload});case m:return(0,r.Z)((0,r.Z)({},n),{},{total_stage_size:t.payload});case f:return(0,r.Z)((0,r.Z)({},n),{},{stage_size:t.payload});case _:var a=(0,r.Z)({},n.item_progress),s=t.payload,O=s.item,A=s.progress;return a[O]=A,(0,r.Z)((0,r.Z)({},n),{},{item_progress:a});case v:return(0,r.Z)((0,r.Z)({},n),{},{item_progress:{},stage_size:0});case x:return(0,r.Z)((0,r.Z)({},n),{},{timer:t.payload});case k:return(0,r.Z)((0,r.Z)({},n),{},{timer:(0,r.Z)((0,r.Z)({},n.timer),{},{timer_instance:t.payload})});case E:return(0,r.Z)((0,r.Z)({},n),{},{timer:(0,r.Z)((0,r.Z)({},n.timer),{},{on:t.payload})});case w:return(0,r.Z)((0,r.Z)({},n),{},{timer:(0,r.Z)((0,r.Z)({},n.timer),{},{time:t.payload})});case y:return(0,r.Z)((0,r.Z)({},n),{},{timer:(0,r.Z)((0,r.Z)({},n.timer),{},{start:t.payload})});case b:return(0,r.Z)((0,r.Z)({},n),{},{migration_size:0});case g:return(0,r.Z)((0,r.Z)({},n),{},{total_stage_size:0,stage_size:0,item_progress:{}});case Z:return(0,r.Z)((0,r.Z)({},n),{},{pause_before_finalize:t.payload});case T:return(0,r.Z)((0,r.Z)({},n),{},{document_title:t.payload});case S:return(0,r.Z)((0,r.Z)({},n),{},{dump_info:t.payload});case i.sP:var C=t.payload,R=n.total_stage_size,I=n.migration_size;return["backup","upload"].includes(C)||(I+=R),(0,r.Z)((0,r.Z)({},n),{},{migration_size:I});case N:return(0,r.Z)((0,r.Z)({},n),{},{allow_page_leave:t.payload});case P:return(0,r.Z)((0,r.Z)({},n),{},{showDryRunResults:t.payload});default:return e}}))}},666:function(e,t,n){"use strict";n.d(t,{$V:function(){return u},Dv:function(){return o},Kf:function(){return l},Zz:function(){return p},e2:function(){return d},k0:function(){return c}});var r=n(36222),a=n(18489),i=n(26429),s={messages:(0,a.Z)({},window.wpmdb_notifications),status:{},errors:{},hidden:{}},o="ADD_NOTIFICATION",l="REMOVE_NOTIFICATION",c="SET_NOTIFICATION_STATUS",u="DELETE_NOTIFICATION_ERROR",p="HIDE_NOTIFICATION",d="SHOW_NOTIFICATION";t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s,t=arguments.length>1?arguments[1]:void 0;return(0,i.ZP)(e,(function(n){var i=t.payload;switch(t.type){case o:return(0,a.Z)((0,a.Z)({},n),{},{messages:(0,a.Z)((0,a.Z)({},n.messages),{},(0,r.Z)({},i.key,i.value))});case l:return delete n.messages[i.key],n;case c:return(0,a.Z)((0,a.Z)({},n),{},{status:(0,a.Z)((0,a.Z)({},n.status),{},(0,r.Z)({},t.payload.type,t.payload.status))});case"SET_NOTIFICATION_ERROR":return(0,a.Z)((0,a.Z)({},n),{},{errors:(0,a.Z)((0,a.Z)({},n.errors),{},(0,r.Z)({},t.payload.location,t.payload.message))});case u:return delete n.errors[t.payload],n;case p:return n.hidden[i]=i,n;case d:return delete n.hidden[i],n;default:return e}}))}},41459:function(e,t,n){"use strict";n.d(t,{Nv:function(){return u},_H:function(){return _},fR:function(){return b},h6:function(){return p},ig:function(){return d},kW:function(){return m},pE:function(){return g},q2:function(){return f}});var r=n(18489),a=n(26429),i=n(42233),s=n(2474),o=n.n(s),l=n(49736),c=n(29942),u="UPDATE_MIGRATION_PANELS",p="UPDATE_PANEL_TITLE",d="UPDATE_MIGRATION_PANELS_OPEN",m="TOGGLE_OPEN_PANEL",f="REMOVE_OPEN_PANEL",_="SET_PANEL_CLICKED",g="SET_PANEL_STATUS",b="REGISTER_PANEL",h={panelsToDisplay:[],panelsOpen:["action_buttons"],panelClicked:[],panelStatus:"",panelTitles:{action_buttons:(0,i.__)("Action","wp-migrate-db"),connect:(0,i.__)("Remote Site","wp-migrate-db"),database:(0,i.__)("Database","wp-migrate-db"),import:(0,i.__)("SQL File","wp-migrate-db"),save:(0,i.__)("Save Profile","wp-migrate-db")},panelSummaries:{},dbTitles:{tables:l.B.tables.all,backups:l.B.backups.none,post_types:l.B.post_types.all,custom_search_replace:""},registeredPanels:[]};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return;case"RESET_MIGRATION":var a=(0,r.Z)((0,r.Z)({},h),{},{panelsToDisplay:["action_buttons","connect"]});return t.payload&&(a.panelsOpen=t.payload),a;case u:return n.panelsToDisplay=t.payload,t.panelPayload&&(n.panelsOpen=t.panelPayload),n;case p:var s=t.payload,l=s.title,b=s.parent;return n.panelTitles[b]=l,n;case d:return n.panelsOpen=t.payload,n;case m:return n.panelsOpen=(0,c.XV)(e.panelsOpen,t.payload),n;case f:var v=o()(n.panelsOpen,(function(e,n){return t.payload!==e}));return(0,r.Z)((0,r.Z)({},n),{},{panelsOpen:v});case _:return n.panelClicked=t.payload,n;case g:return n.panelStatus=t.payload,n;case"UPDATE_DB_PANEL_TITLE":return n=function(e,t,n){e.dbTitles[n.payload.key]=n.payload.title;var r=Object.values(e.dbTitles).filter((function(e){return e})).join(", ");return t=(0,i.__)("Database","wp-migrate-db")+": ".concat(r),e.panelTitles.database=t,e}(n,l,t),n;case"MST_TOGGLE_ENABLED":return!t.payload&&n.panelsOpen.includes("tables")&&n.panelsOpen.splice(n.panelsOpen.indexOf("tables"),1),n;case"REGISTER_PANEL":return n.registeredPanels.includes(t.payload)||n.registeredPanels.push(t.payload),n;default:return e}}))}},22973:function(e,t,n){"use strict";n.d(t,{$Q:function(){return w},Cp:function(){return E},Gg:function(){return h},KG:function(){return x},N2:function(){return c},PM:function(){return b},Qu:function(){return p},YQ:function(){return l},_n:function(){return o},a:function(){return m},aC:function(){return f},bQ:function(){return v},hm:function(){return u},jJ:function(){return y},qk:function(){return g},tT:function(){return _},xv:function(){return d}});var r=n(18489),a=n(42233),i=n(52089),s=n(29942),o="ADD_PROFILE",l="ADD_RECENT_MIGRATION",c="REMOVE_PROFILE",u="RENAME_PROFILE",p="SAVE_PROFILE_NAME",d="TOGGLE_PROFILE_EDIT",m="PROFILE_SAVING",f="SAVING_RECENT",_="PROFILE_RENAMING",g="PROFILE_OVERWRITING",b="PROFILE_DELETING",h="PROFILE_SAVE_ERROR",v="SET_CURRENT_PROFILE",E="SET_BUTTON_TEXT",w="SET_PROFILE_STATUS",y="PROFILE_LOADING",x="PROFILE_LOAD_ERROR",k={saving:!1,saving_recent:!1,profile_renaming:!1,profile_deleting:!1,profile_overwriting:!1,profile_save_error:"",profile_loading:null,profile_load_error:!1},Z={saved:window.wpmdb_data.migration_profiles,recent:window.wpmdb_data.recent_migrations,toggled:[],status:k,current_profile:null,loaded_profile:null,imported:!1,ui:{btn_text:(0,a.__)("Save","wp-migrate-db")}},T=(0,i.Lq)(Z,{RESET_APP:function(e,t){return e.current_profile=null,e.loaded_profile=null,e.status=k,e},PROFILE_SAVE_ERROR:function(e,t){return e.status={saving:!1,saving_recent:!1,profile_renaming:!1,profile_deleting:!1,profile_overwriting:!1,profile_save_error:t.payload.data},e},TOGGLE_PROFILE_EDIT:function(e,t){return e.toggled=(0,s.XV)(e.toggled,t.payload),e},PROFILE_SAVING:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),{},{saving:!0,profile_save_error:""}),e.ui.btn_text=(0,a.__)("Saving...","wp-migrate-db"),e},SAVING_RECENT:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),{},{saving_recent:!0,profile_save_error:""}),e},ADD_PROFILE:function(e,t){return e.saved.push(t.payload),e.status.saving=!1,e},ADD_RECENT_MIGRATION:function(e,t){return e.recent=t.payload.profiles,e.status.saving_recent=!1,e},PROFILE_DELETING:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),{},{profile_deleting:!0,profile_save_error:""}),e},REMOVE_PROFILE:function(e,t){return e[t.payload.slice].splice(t.payload.index,1),e.status=(0,r.Z)((0,r.Z)({},e.status),{},{saving_recent:!1,profile_deleting:!1}),e},PROFILE_RENAMING:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),{},{profile_renaming:!0,profile_save_error:""}),e},RENAME_PROFILE:function(e,t){return e.saved[t.payload.index].name=t.payload.text,e.status.profile_renaming=!1,e},PROFILE_OVERWRITING:function(e,t){return e.status.profile_overwriting=t.payload,e.status.profile_save_error="",null!==e.loaded_profile&&(e.loaded_profile.imported=!1),e.ui.btn_text=(0,a.__)("Saving...","wp-migrate-db"),e},PROFILE_LOADING:function(e,t){return e.status.profile_loading=!0,e},LOAD_PROFILE:function(e,t){return e.status.profile_loading=!1,e.current_profile=t.payload.id,e.loaded_profile=t.payload.profile,e},SET_PROFILE_STATUS:function(e,t){return e.status.profile_loading=!1,e},SET_CURRENT_PROFILE:function(e,t){return e.current_profile=t.payload.id,e},SET_BUTTON_TEXT:function(e,t){return e.ui.btn_text=t.payload,e},PROFILE_LOAD_ERROR:function(e,t){return e.status.profile_load_error=t.payload,e}});t.ZP=T},66441:function(e,t,n){"use strict";n.d(t,{Dh:function(){return h},G3:function(){return u},IY:function(){return m},KM:function(){return b},O9:function(){return p},Q0:function(){return f},S_:function(){return v},ci:function(){return d},hx:function(){return _},jC:function(){return x},oK:function(){return c},zi:function(){return g}});var r=n(18489),a=n(96480),i=n.n(a),s=n(26429),o=n(29942),l=n(29950),c="UPDATE_STANDARD_SEARCH_REPLACE",u="UPDATE_STANDARD_SEARCH_REPLACE_DOMAIN",p="TOGGLE_STANDARD_SEARCH_REPLACE",d="TOGGLE_STANDARD_SEARCH_REPLACE_VISIBLE",m="UPDATE_CUSTOM_SEARCH_REPLACE",f="REORDER_CUSTOM_SEARCH_REPLACE",_="ADD_CUSTOM_SEARCH_REPLACE_ITEM",g="DELETE_CUSTOM_SEARCH_REPLACE_ITEM",b="SET_CUSTOM_SEARCH_REPLACE",h="SET_CUSTOM_SEARCH_DOMAIN_LOCKED",v="RESET_CUSTOM_SEARCH_REPLACE",E=function(){return{replace_old:arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",replace_new:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",focus:arguments.length>2&&void 0!==arguments[2]&&arguments[2],regex:arguments.length>3&&void 0!==arguments[3]&&arguments[3],isValidRegex:arguments.length>6&&void 0!==arguments[6]?arguments[6]:null,replace_old_placeholder:arguments.length>4&&void 0!==arguments[4]?arguments[4]:"",replace_new_placeholder:arguments.length>5&&void 0!==arguments[5]?arguments[5]:"",id:arguments.length>7&&void 0!==arguments[7]?arguments[7]:i()()}},w=[E()],y={standard_search_replace:{domain:{search:"",replace:""},path:{search:"",replace:""}},standard_options_enabled:["domain","path"],standard_search_visible:!0,custom_search_replace:w,custom_search_domain_locked:!1},x=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y,n=arguments.length>1?arguments[1]:void 0;return(0,s.ZP)(t,(function(t){switch(n.type){case c:return(0,r.Z)((0,r.Z)({},t),{},{standard_search_replace:n.payload});case u:return(0,r.Z)((0,r.Z)({},t),{},{standard_search_replace:{path:(0,r.Z)({},t.standard_search_replace.path),domain:n.payload}});case d:return(0,r.Z)((0,r.Z)({},t),{},{standard_search_visible:n.payload});case p:return e=(0,o.XV)(t.standard_options_enabled,n.payload),(0,r.Z)((0,r.Z)({},t),{},{standard_options_enabled:e});case m:var a=n.payload,i=a.key,s=a.option,y=a.value;return t.custom_search_replace[i]&&(t.custom_search_replace[i][s]=y),t;case f:return(0,r.Z)((0,r.Z)({},t),{},{custom_search_replace:n.payload});case _:return t.custom_search_replace.push(E("","",n.payload)),t;case g:return e=(0,o.XV)(t.custom_search_replace,t.custom_search_replace[n.index]),(0,r.Z)((0,r.Z)({},t),{},{custom_search_replace:e});case v:return(0,r.Z)((0,r.Z)({},t),{},{custom_search_replace:w});case b:var x=n.payload.local_site,k=x.this_url,Z=x.this_path,T=n.payload,S=T.intent,N=T.force_update;return"savefile"===S||N&&"import"!==S?t.custom_search_replace=[E("","")]:N&&"import"===S&&(t.custom_search_replace=[E("",(0,o.Ph)(k)),E("",Z),E("","")]),t;case h:return t.custom_search_domain_locked=n.payload,t;case l.Ld:var P=n.payload.profile.value.search_replace;return(0,r.Z)({},P);default:return t}}))}},19826:function(e,t,n){"use strict";n.d(t,{TE:function(){return u},YQ:function(){return c},eH:function(){return p},km:function(){return d}});var r=n(36222),a=n(18489),i=n(88368),s=n(26429),o=window.wpmdb_settings;Object.entries(o).forEach((function(e){var t=(0,i.Z)(e,2),n=t[0];t[1];"delay_between_requests"!==n&&(["1",""].includes(o[n])&&(o[n]="1"===o[n]))})),o.max_request=parseInt(o.max_request),o.isPro="false"!==window.wpmdb_data.is_pro,o.delay_between_requests=parseInt(o.delay_between_requests);o.masked_licence||(o.masked_licence=null);var l=(0,a.Z)((0,a.Z)({},o),{status:{resetting_api_key:!1},errors:{}}),c="SET_SETTINGS_STATUS",u="SETTINGS_ERROR",p="DELETE_SETTINGS_ERROR",d="UPDATE_SETTING";t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l,t=arguments.length>1?arguments[1]:void 0;return(0,s.ZP)(e,(function(n){switch(t.type){case"SET_ACTION":return n;case p:return delete n.errors[t.payload],n;case u:return(0,a.Z)((0,a.Z)({},n),{},{errors:(0,a.Z)((0,a.Z)({},n.errors),{},(0,r.Z)({},t.payload.location,t.payload.message))});case"LICENSE_REMOVED":return(0,a.Z)((0,a.Z)({},n),{},{licence:"",masked_licence:null});case c:return(0,a.Z)((0,a.Z)({},n),{},{status:(0,a.Z)((0,a.Z)({},n.status),{},(0,r.Z)({},t.payload.type,t.payload.status))});case"SET_API_KEY":return(0,a.Z)((0,a.Z)({},n),{},{key:t.payload});case d:return(0,a.Z)((0,a.Z)({},n),{},(0,r.Z)({},t.payload.setting,t.payload.value));case"UPDATE_SETTINGS":return(0,a.Z)((0,a.Z)({},n),t.payload);default:return e}}))}},58696:function(e,t,n){"use strict";n.d(t,{C:function(){return i}});var r=n(4516),a=function(e){return e.migrations.connection_info};function i(e,t){return(0,r.fX)(a,"connection_info",e,t)}},4516:function(e,t,n){"use strict";n.d(t,{Au:function(){return b},FY:function(){return h},Gn:function(){return T},Jm:function(){return w},NR:function(){return g},_P:function(){return v},do:function(){return Z},fX:function(){return y},r5:function(){return _},xg:function(){return E}});var r=n(27166),a=n(33032),i=n(56802),s=n(12544),o=n.n(s),l=n(29942),c=function(e){return e.migrations.current_migration},u=function(e){return e.migrations.remote_site},p=function(e){return e.migrations.local_site},d=function(e){return e.migrations},m=function(e){return e.migrations.search_replace},f=function(e){return e.migrations.migration_progress};function _(e,t){return y(c,"current_migration",e,t)}function g(e,t){return y(d,"migration",e,t)}function b(e){return E("migration_tables",e)}function h(e,t){return y(u,"remote_site",e,t)}function v(e,t){return y(p,"local_site",e,t)}function E(e,t,n){return y(f,"migration_progress",e,t,n)}function w(e,t){return y(m,"search_replace",e,t)}var y=function(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"array",s=(0,i.P1)([e],(function(e){return"object"===typeof n?x(e,n,a):k(e,n)}));return s(r)},x=function(e,t,n){var r=[],a={};return t.forEach((function(t){e.hasOwnProperty(t)&&("array"===n?r.push(e[t]):"object"===n&&(a[t]=e[t]))})),Object.keys(a).length>0?a:r},k=function(e,t){if(e.hasOwnProperty(t))return e[t]},Z=function(e){var t=_("intent",e);return o()(["push","pull"],t)},T=function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t){var a,i,s,o,c,u,p,d;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=_("intent",t),i=_("tables_option",t),s=_("tables_selected",t),o=v("this_prefixed_tables",t),"selected"!==i){e.next=8;break}o=s,e.next=19;break;case 8:if("import"!==a||!(0,l.Yu)()){e.next=18;break}return e.next=11,Promise.resolve().then(n.bind(n,67821));case 11:c=e.sent,u=c.selectFromImportData,p=u("tables",t),d=v("this_temp_prefix",t),o=p.map((function(e){return d+e})),e.next=19;break;case 18:"pull"===a&&(o=h("prefixed_tables",t));case 19:return e.abrupt("return",o);case 20:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()},76178:function(e,t,n){"use strict";n.d(t,{O:function(){return i}});var r=n(4516),a=function(e){return e.panels};function i(e,t){return(0,r.fX)(a,"panels",e,t)}},86191:function(e,t,n){"use strict";n.d(t,{d:function(){return i}});var r=n(4516),a=function(e){return e.profiles};function i(e,t){return(0,r.fX)(a,"profiles",e,t)}},27325:function(e,t,n){"use strict";n.d(t,{u:function(){return i}});var r=n(4516),a=function(e){return e.settings};function i(e,t){return(0,r.fX)(a,"settings",e,t)}},19085:function(e,t,n){"use strict";n.d(t,{en:function(){return E},HW:function(){return v},Ag:function(){return g},Q:function(){return h}});var r,a=n(17186),i=n(4665),s=n(30348);function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o.apply(this,arguments)}n.p;var l=n(9712),c=n(39794);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}n.p;var p,d,m,f,_,g=(0,s.ZP)(l.r)(p||(p=(0,a.Z)(["\n  position: relative;\n  &:hover {\n    cursor: pointer;\n  }\n"]))),b=(s.ZP.p(d||(d=(0,a.Z)(["\n  display: flex;\n  align-items: center;\n"]))),(0,s.ZP)((function(e){return i.createElement("svg",o({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",id:"el_HS5Ejor-n"},e),r||(r=i.createElement("path",{d:"M8 2v2a4 4 0 1 0 4 4h2a6 6 0 1 1-6-6Z",id:"el_6X7lquFKkl"})))}))(m||(m=(0,a.Z)(["\n  width: 1.2rem;\n  height: 1.2rem;\n  display: inline;\n"])))),h=function(e){return i.createElement("span",{className:"styled-spinner-wrap ".concat(e.className?e.className:"")},i.createElement(b,null))},v=(0,s.ZP)(h)(f||(f=(0,a.Z)(["\n  margin-left: 5px;\n  position: absolute;\n  top: -10px;\n  #el_6X7lquFKkl {\n    fill: ",";\n  }\n"])),c.HT),E=(0,s.ZP)((function(e){return i.createElement("svg",u({width:24,height:24,viewBox:"0 0 24 24",xmlSpace:"preserve"},e),i.createElement("g",{transform:"translate(4 4)"},i.createElement("circle",{id:"license-checked-a",cx:8,cy:8,r:8,style:{fill:"#236de7"}}),i.createElement("path",{d:"M7.587 11.338a1.01 1.01 0 0 1-1.433 0L3.933 9.104a1.024 1.024 0 0 1 0-1.442 1.01 1.01 0 0 1 1.433 0l1.323 1.331c.1.1.262.1.362 0l3.583-3.604a1.01 1.01 0 0 1 1.433 0 1.025 1.025 0 0 1 0 1.442l-4.48 4.507Z",style:{fill:"#fff",fillRule:"nonzero"}})))}))(_||(_=(0,a.Z)(["\n  use {\n    fill: #236de7;\n  }\n"])))},39794:function(e,t,n){"use strict";n.d(t,{HT:function(){return r},Qp:function(){return a},qP:function(){return i}});var r="#236DE7",a="#575757",i="#dc3232"},86177:function(e,t,n){"use strict";var r,a=n(4665),i=n(62295),s=n(4296),o=n(79043),l=n(52204),c=n(60050);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}var p,d,m=function(e){return a.createElement("svg",u({xmlns:"http://www.w3.org/2000/svg",width:52,height:52,viewBox:"0 0 52 52"},e),r||(r=a.createElement("g",{fill:"none"},a.createElement("path",{fill:"#FFF",d:"M42.867 30.29h-5.531c0-1 .678-1.813 1.516-1.813.018 0 .036.004.054.004.502-.943 1.383-1.571 2.387-1.571.982 0 1.853.607 2.358 1.52a1.727 1.727 0 0 1 1.16-.462c1.075 0 1.942 1.04 1.942 2.322h-3.886ZM30.797 18.066c-1.39-2.155-.705-5.07 1.534-6.514l.387.603c.834-1.86 2.751-3.16 4.983-3.16v10.036h2c.004-1.167 1.127-2.113 2.517-2.113s2.514.946 2.514 2.113h-2.514c-.007 1.177-3.166 2.134-7.063 2.134-3.9 0-7.063-.957-7.063-2.134h3.575a4.452 4.452 0 0 1-.87-.965Zm-6.334 3.99h-6.688c0-.787.82-1.43 1.827-1.43.087 0 .17.011.25.019.487-.408 1.177-.665 1.95-.665 1.469 0 2.66.928 2.66 2.077Zm-8.873-10.064 1.502 1.593c.03-.412.138-.823.34-1.21.805-1.545 2.755-2.123 4.362-1.289l-2.466 4.745h.953c.004-.653.636-1.184 1.416-1.184.787 0 1.419.53 1.419 1.188l-1.42.003c-.01.661-1.787 1.196-3.983 1.196-2.199 0-3.983-.538-3.983-1.203h1.52c-.884-1.127-.765-2.791.34-3.838Zm-6.8 4.724c.16.173.311.357.452.549a4.704 4.704 0 0 1 4.362 3.643 4.696 4.696 0 0 1-1.722 4.774 7.253 7.253 0 0 1 2.484 1.116 8.147 8.147 0 0 1 3.716-1.957 8.142 8.142 0 0 1 5.037.451 4.34 4.34 0 0 1 6.673 3.9 2.51 2.51 0 0 1 2.254 3.084 7.813 7.813 0 0 1 8.515 6.013c.047.21.083.419.116.628a7.137 7.137 0 0 1 3.426 3.347c-.234.26-.48.52-.725.77C38.953 47.544 32.8 50.35 26 50.35 12.574 50.35 1.65 39.426 1.65 26c0-3.756.856-7.316 2.38-10.494.14-.292.289-.574.44-.86a6.451 6.451 0 0 1 3.58 1.38c.288.177.54.408.74.69ZM26 0C16.127 0 6.944 5.753 2.61 14.658A25.753 25.753 0 0 0 0 26c0 14.336 11.664 26 26 26 6.691 0 13.054-2.553 17.918-7.186C49.13 39.849 52 33.168 52 26 52 11.664 40.336 0 26 0Z"}),a.createElement("path",{fill:"#FFF",d:"M8.637 31.951c.23-.053.456-.068.685-.093-.23.025-.455.04-.685.093a6.098 6.098 0 0 0-1.821.74v.003a6.012 6.012 0 0 1 1.821-.743m9.325 6.798zM9.206 22.781a7.7 7.7 0 0 0-.172-1.578c-.08-.33-.194-.638-.309-.95.115.312.23.62.305.95.122.531.169 1.058.176 1.578m-3.798-6.627c-.018-.014-.04-.021-.057-.032.018.01.04.018.057.032zm17.572 21.55c.937-.84 2.168-1.464 3.568-1.765a9.251 9.251 0 0 0 0 0c-1.4.301-2.635.925-3.568 1.765a7.434 7.434 0 0 0 0 0M36.293 40.353c.895-.22 1.786.193 2.268.953a4.853 4.853 0 0 1 3.577.883 4.833 4.833 0 0 0-3.574-.883c-.485-.76-1.373-1.172-2.271-.953"}),a.createElement("path",{fill:"#81BDD4",d:"M17.569 28.177h1.61a2.66 2.66 0 0 1 3.04-.018l-.007.018h.974c.015-.555.549-1.004 1.21-1.004.673-.003 1.219.46 1.219 1.026h-1.222l-.004.003h.008c0 .42-.83.781-2.018.942v5.153a3.054 3.054 0 0 1-2.81-1.861l-.215.347c-1.272-.854-1.665-2.584-.873-3.862.004-.003.008-.01.011-.014-.567-.186-.923-.43-.923-.705h.014c-.003-.007-.014-.018-.014-.025M3.586 15.213c.436.157.858.347 1.258.577.018.011.04.018.058.033a7.842 7.842 0 0 1 3.28 3.755c.043.103.065.216.101.318.117.317.233.631.313.967.12.54.167 1.077.175 1.606a7.818 7.818 0 0 1-.451 2.84c-.022.061-.04.12-.066.174a7.867 7.867 0 0 1-3.89 4.227c.426.288.804.646 1.135 1.044.414.5.752 1.069.967 1.711a6.168 6.168 0 0 1 1.847-.752c.232-.054.461-.065.694-.095a6.16 6.16 0 0 1 4.73 1.46c.774.672 1.4 1.53 1.774 2.537.211.128.407.277.585.441.011.011.026.019.037.03.033.029.058.062.087.094.41.413.72.928.89 1.522.019.066.044.128.059.194.01.047.015.095.022.142.025.135.05.27.062.402.112-.059.236-.106.356-.161a6.593 6.593 0 0 1 1.102-.409c.047-.014.087-.033.134-.044.015-.003.03-.003.04-.007a7.836 7.836 0 0 1 1.85-.222c.524 0 1.037.05 1.524.146.97-.854 2.247-1.49 3.701-1.796a9.442 9.442 0 0 1 1.978-.204c1.076 0 2.098.175 3.025.489 1.541.525 2.817 1.441 3.624 2.587.306.438.546.905.702 1.402a2.307 2.307 0 0 1 2.898.81 5.413 5.413 0 0 1 5.013 2.003c.251-.251.495-.514.731-.777a7.175 7.175 0 0 0-3.454-3.383 7.75 7.75 0 0 0-.113-.635c-.905-3.964-4.639-6.544-8.576-6.077.047-.182.073-.376.073-.573a2.534 2.534 0 0 0-2.338-2.544c.004-.072.007-.15.007-.226.011-2.42-1.93-4.394-4.344-4.409a4.298 4.298 0 0 0-2.382.694 8.168 8.168 0 0 0-5.071-.456 8.189 8.189 0 0 0-3.745 1.978 7.24 7.24 0 0 0-2.498-1.128 4.756 4.756 0 0 0 1.734-4.825 4.737 4.737 0 0 0-4.392-3.682 6.072 6.072 0 0 0-.458-.555 2.46 2.46 0 0 0-.741-.697 6.47 6.47 0 0 0-3.603-1.394 24.33 24.33 0 0 0-.444.868m41 12.85a1.87 1.87 0 0 0-1.214.458c-.529-.907-1.44-1.51-2.473-1.51-1.047 0-1.97.624-2.495 1.56-.02 0-.038-.003-.057-.003-.877 0-1.588.807-1.588 1.8h9.862c0-1.273-.908-2.306-2.035-2.306"}),a.createElement("path",{fill:"#04223F",d:"M42.746 41.767a5.478 5.478 0 0 0-3.847-.891 2.314 2.314 0 0 0-2.903-.805 5.109 5.109 0 0 0-.704-1.391c-.81-1.138-2.09-2.048-3.636-2.57a9.555 9.555 0 0 0-3.034-.485 9.79 9.79 0 0 0-1.984.203c-1.458.304-2.738.935-3.712 1.783-.489-.094-1-.145-1.528-.145-.646 0-1.27.08-1.856.22-.011.004-.026.004-.04.008-.044.015-.088.033-.135.044-.39.105-.759.242-1.105.405-.12.055-.245.102-.358.16v.004a3.832 3.832 0 0 0-.062-.403c-.007-.047-.01-.094-.022-.137-.014-.07-.04-.13-.058-.196a3.594 3.594 0 0 0-.893-1.515c-.03-.029-.055-.061-.088-.09-.01-.011-.025-.019-.036-.03a3.686 3.686 0 0 0-.587-.438 6.193 6.193 0 0 0-1.78-2.518 6.21 6.21 0 0 0-4.748-1.45c-.23.025-.46.04-.693.094a6.173 6.173 0 0 0-1.852.75v-.003a5.271 5.271 0 0 0-.97-1.7 5.385 5.385 0 0 0-1.138-1.032 7.86 7.86 0 0 0 3.902-4.2l.065-.174c.325-.888.474-1.841.453-2.82a7.498 7.498 0 0 0-.18-1.594c-.076-.333-.192-.645-.31-.96-.036-.102-.057-.214-.101-.316a7.811 7.811 0 0 0-3.29-3.728c-.018-.015-.04-.022-.058-.033a7.91 7.91 0 0 0-1.262-.573 24.168 24.168 0 0 0-2.403 10.531c0 13.473 11.031 24.435 24.59 24.435 6.867 0 13.08-2.816 17.548-7.342a5.567 5.567 0 0 0-1.185-1.098"}),a.createElement("path",{fill:"#81BDD4",d:"M19.648 20.235c-.948 0-1.717.631-1.717 1.417h6.276c0-1.14-1.118-2.062-2.496-2.062-.725 0-1.372.254-1.83.66a1.64 1.64 0 0 0-.233-.015"}),a.createElement("path",{fill:"#04223F",d:"M18.15 17.034c2.097 0 3.794-.533 3.805-1.188l1.355-.003c0-.651-.607-1.177-1.355-1.177-.745 0-1.35.526-1.353 1.173h-.91l2.356-4.702c-1.535-.827-3.398-.254-4.167 1.278-.19.383-.297.79-.321 1.198l-1.439-1.578c-1.055 1.038-1.172 2.688-.324 3.804l-1.452.004c0 .658 1.704 1.191 3.805 1.191m16.874 4.483c3.989 0 7.223-.987 7.23-2.201h2.574c0-1.203-1.154-2.179-2.573-2.179-1.424 0-2.573.976-2.577 2.179H37.63l.004-10.35c-2.288 0-4.251 1.34-5.105 3.258l-.396-.622c-2.292 1.49-2.994 4.496-1.571 6.72.248.387.558.707.89.994h-3.659c0 1.214 3.238 2.201 7.23 2.201m-16.166 7.125-.01.015c-.796 1.286-.402 3.027.874 3.887l.215-.349a3.062 3.062 0 0 0 2.819 1.874v-5.188c1.192-.162 2.024-.526 2.024-.948h-.008l.004-.004H26c0-.57-.547-1.036-1.221-1.032-.664 0-1.2.452-1.215 1.01h-.977l.007-.018c-.973-.654-2.176-.607-3.048.018h-1.615c0 .007.011.015.011.026h-.011c0 .275.357.522.926.709"}))))},f=(n.p,n.p+"static/media/mdb-branding-transparent.edbb2b6f.svg"),_=n(29942),g=(n(88634),n(26532),n(27166)),b=n(33032),h=n(17186),v=n(30348),E=n(75338),w=n(42233),y=v.ZP.div(p||(p=(0,h.Z)(["\n  display: grid;\n"]))),x=n(18489),k=n(88368),Z=n(29890),T=n(31330),S=n.n(T),N=n(10734),P=n.n(N),O=n(6142);function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)}var C=function(e){return a.createElement("svg",A({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 16 16"},e),d||(d=a.createElement("path",{fill:"none",stroke:"#236DE7",strokeWidth:2,d:"M4 8.5 7.2 11 12 5"})))},R=(n.p,function(e){var t=e.deleteItem,n=e.cancelDelete,r=e.item,i=e.index;return a.createElement("div",{className:"flex-container confirm-delete tip"},a.createElement("div",{className:"speech-shadow"},a.createElement("div",{className:"speech-bubble-left"},a.createElement("p",{className:"margin-bottom"},(0,w.__)("Are you sure?","wp-migrate-db")),a.createElement("div",{className:"flex-container"},a.createElement("button",{className:"btn btn-tooltip delete icon",onClick:function(){return t(r,i)}},a.createElement("div",{className:"flex-container"},a.createElement("div",null,a.createElement(C,{className:"styled-check"})),(0,w.__)("Delete","wp-migrate-db"))),a.createElement("button",{className:"btn btn-tooltip-stroke",onClick:function(){return n()}},(0,w.__)("Cancel","wp-migrate-db"))))))}),I=(n(88847),["profilesList","profilesRenaming","profilesDeleting","btn_text","toggled","recentProfilesEmpty"]),D=function(e){var t=e.profilesList,n=e.profilesRenaming,r=e.profilesDeleting,i=e.btn_text,s=e.toggled,o=e.recentProfilesEmpty,c=(0,Z.Z)(e,I),u=[],p=r||n,d=(0,a.useState)(),m=(0,k.Z)(d,2),f=m[0],_=m[1],h=function(){_("")},v=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,n){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!p){e.next=2;break}return e.abrupt("return",!1);case 2:return e.next=4,c.removeSavedProfile(t.guid,n,t.name);case 4:e.sent&&_("");case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),E=function(e,t){if(t)return!1;c.toggleEdit(e)},y=function(e,t){if(t)return!1;!function(e){_(e)}(e)},T=t.map((function(e,t){var o=(0,x.Z)((0,x.Z)({},c),{},{deleteItem:v,cancelDelete:h,item:e,index:t}),d=s.includes(t),m=s.length>0&&!d,_=m?"disabled ":"",g=(0,O.decode)(e.name),b=m?a.createElement("span",{className:"link"},g):a.createElement(l.rU,{to:"/migrate/".concat(e.id),onClick:function(){c.profileLoading()},className:"link"},g);if(-1!==P()(s,t)){var k="btn icon save-profile btn-tooltip profile-screen".concat("success"===i?" btn-success":"");b=a.createElement(a.Fragment,null,a.createElement(S(),{active:d},a.createElement("div",{className:"button-wrap ".concat(d?" toggled":"")},a.createElement("input",{type:"text",defaultValue:g,ref:function(e){return u[t]=e}}),a.createElement("button",{className:"action-btn ".concat(k).concat(r||n?" in-progress":""),onClick:function(){if(p)return!1;c.changeName(e.guid,t,u[t].value)}},a.createElement(C,{className:"styled-check"}),"Save"),a.createElement("button",{onClick:function(){return E(t,m)},className:"action-btn btn btn-tooltip-stroke profile-screen"},(0,w.__)("Cancel","wp-migrate-db")))))}return a.createElement("tr",{className:"".concat(_,"flex-container").concat(d?" toggled":""),key:t},a.createElement("td",null,e.id),a.createElement("td",{className:"table-col-action"},b),!d&&a.createElement(a.Fragment,null,a.createElement("td",null,a.createElement("button",{onClick:function(){return E(t,m)},className:"link"},(0,w.__)("Rename","wp-migrate-db"))),a.createElement("td",null,a.createElement("button",{className:"action-btn delete-profile link text-error".concat(r||n?" in-progress":" text-error"),onClick:function(){return y(t,m)}},(0,w.__)("Delete","wp-migrate-db")))),f===t&&a.createElement("td",{className:"relative"},a.createElement(R,o)))})),N=o&&1===t.length?"one-profile ":"";return a.createElement("table",{className:"".concat(N,"profile-table")},a.createElement("tbody",null,T))},M=n(76554),F=["recentMigrations","savedProfilesEmpty"],L=function(e){var t=e.recentMigrations,n=e.savedProfilesEmpty,r=(0,Z.Z)(e,F),i=t.map((function(e,t){var n=!!r.isSaving;return a.createElement("tr",{className:"flex-container",key:t},a.createElement("td",{className:"table-col-item"},a.createElement(l.rU,{to:"/unsaved/".concat(e.id),onClick:function(){r.profileLoading()},className:"link"},(0,O.decode)(e.name))),a.createElement("td",{className:"date"},(0,M.ee)("M d ".concat(window.wpmdb_data.time_format),1e3*e.date)),a.createElement("td",null,a.createElement("button",{className:r.isSaving?"link saving":"link",disabled:n,onClick:function(){if(n)return!1;r.handleUnsaved(e.id,t)}},(0,w.__)("Save","wp-migrate-db"))))})),s=n&&1===t.length?"one-profile ":"";return a.createElement("table",{className:"".concat(s,"profile-table")},a.createElement("tbody",null,i))},B=n(61358);function U(){return U=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},U.apply(this,arguments)}var j=function(e){return a.createElement("svg",U({height:16,viewBox:"0 0 16 16",width:16,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("path",{d:"M6 1a1 1 0 0 0-2 0v8a1 1 0 0 0 2 0V1Z",style:{fill:"#fff"},transform:"translate(3 3)"}),a.createElement("path",{d:"M6 1a1 1 0 0 0-2 0v8a1 1 0 0 0 2 0V1Z",style:{fill:"#fff"},transform:"rotate(-90 8 5)"}))},z=(n.p,n(29214)),G=n(66055),V=n(22973),H=n(29950),W=n(22633),Y=n(14251),K=n(91828),q=n(27325),J=n(4516),X=n(58696),Q=function(e){switch(e){case"push":case"pull":return"connect";case"find_replace":case"backup":case"savefile":default:return"database";case"import":return"import"}};function $(){return(0,G.m)(V.jJ)}function ee(e,t){return function(){var r=(0,b.Z)((0,g.Z)().mark((function r(a,s){var o,l;return(0,g.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return(0,i.dC)((function(){a((0,Y.oA)()),a($())})),r.prev=1,r.next=4,(0,_.op)("/load-profile",{id:e,unSaved:t},!1,a);case 4:o=r.sent,r.next=11;break;case 7:return r.prev=7,r.t0=r.catch(1),a((0,G.m)(V.KG,(0,w.__)("Error loading profile, please check your server error logs.","wp-migrate-db"))),r.abrupt("return");case 11:if("Profile does not exist."!==o.data){r.next=14;break}return a((0,G.m)(V.KG,o.data)),r.abrupt("return");case 14:if(o.data.hasOwnProperty("profile")){r.next=17;break}return a((0,G.m)(V.KG,o.data)),r.abrupt("return");case 17:if(o.data.profile.value=JSON.parse(o.data.profile.value),l=o.data.profile.value.current_migration.intent,(0,_.Yu)()||["savefile","find_replace","backup_local"].includes(l)){r.next=22;break}return a({type:V.$Q,payload:{}}),r.abrupt("return",!1);case 22:return a({type:H.Ld,payload:o.data}),(0,i.dC)((function(){(0,_.Yu)()&&["push","pull"].includes(l)&&a(function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,r){var a,i,s,o,l;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return a=(0,J.r5)("intent",r()),i=(0,X.C)("connection_state",r()),e.next=4,Promise.resolve().then(n.bind(n,34363));case 4:if(s=e.sent,o=s.changeConnection,l=s.connectToRemote,"push"!==a&&"pull"!==a){e.next=13;break}return e.next=9,t(o("".concat(i.url,"\n").concat(i.key)));case 9:if(!e.sent){e.next=12;break}return e.abrupt("return",!1);case 12:t(l(a));case 13:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),"import"===l&&a((0,G.m)(H.n_,!1)),setTimeout((function(){a((0,W.I4)("action_buttons"))}),100)})),r.abrupt("return",o.data);case 25:case"end":return r.stop()}}),r,null,[[1,7]])})));return function(e,t){return r.apply(this,arguments)}}()}function te(e){return function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){var a,i,s,o,l;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=e.path.includes("unsaved"),i=(0,q.u)("masked_licence",r()),t.next=4,n(ee(e.params.id,a));case 4:if(s=t.sent){t.next=7;break}return t.abrupt("return",!1);case 7:o=s.profile.value.current_migration.intent,l=Q(o),"import"!==o||i||n((0,Y.I8)(!1)),n((0,W.$f)({panel:l,title:K.B[o],intent:o},!0));case 11:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}var ne,re,ae=n(86191),ie=(0,v.ZP)(y)(ne||(ne=(0,h.Z)(["\n  h3 {\n    padding-bottom: 0.5rem;\n  }\n"]))),se=(0,i.$j)((function(e){return{toggled:(0,ae.d)("toggled",e),notifications:e.notifications,profiles:e.profiles,migration:e.migrations,current_migration:e.migrations.current_migration,connection_info:e.migrations.connection_info}}),{handleUnSaved:B.UF,handleItemNameChange:B.hV,removeProfile:B.NF,removeSavedProfile:B.qH,toggleProfileEdit:B.Tv,profileLoading:$,renderSaveProfileButtonText:B.F9})((function(e){var t=e.profiles,n=(0,i.I0)(),r=e.profiles.status,s=r.saving_recent,c=r.saving,u=r.profile_deleting,p=r.profile_renaming,d=r.profile_save_error;(0,a.useEffect)((function(){var e=new URL(window.location.href),t=new URLSearchParams(window.location.search);if(t.has("redirect_profile")){var n="unsaved";"1"===t.get("saved_profile")&&(n="migrate");var r=t.get("redirect_profile");t.delete("saved_profile"),t.delete("redirect_profile"),e.search=t.toString(),window.location.href="".concat(e.toString(),"#").concat(n,"/").concat(r)}}),[]);var m=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.handleUnSaved(n,r);case 2:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}();return a.createElement(ie,{className:"wpmdb-profiles"},d&&"migration"!==d.location&&a.createElement(z.Z,{type:"danger"},d.message),a.createElement("section",{className:"profiles-list"},a.createElement("div",{className:"container-shadow table"},a.createElement("div",{className:"flex-container"},a.createElement("h2",{className:"table-heading"},(0,w.__)("Saved Profiles","wp-migrate-db"))),a.createElement("div",{className:"table-divider-line bg-grey-light"}),t.saved.length>0?a.createElement(D,(0,o.Z)({profilesList:t.saved,changeName:function(t,n,r){e.handleItemNameChange(t,n,r)},removeProfile:function(t,n,r){e.removeSavedProfile(t,n,r)},toggleEdit:function(t){e.toggleProfileEdit(t)},recentProfilesEmpty:0===t.recent.length,toggled:e.profiles.toggled,profilesRenaming:p,profilesDeleting:u,profileLoading:e.profileLoading},e)):a.createElement("div",{className:"no-items-wrap".concat(0===t.saved.length&&1===t.recent.length?" one-profile":"")},a.createElement("p",{className:"no-items"},(0,E.ZP)((0,w.gB)((0,w.__)('There are no saved profiles &mdash; <a href="%s">Get Started</a>.',"wp-migrate-db"),"#migrate"))))),a.createElement("div",{className:"container-shadow table"},a.createElement("div",{className:"flex-container"},a.createElement("h2",{className:"table-heading"},(0,w.__)("Last 10 Unsaved Profiles","wp-migrate-db"))),a.createElement("div",{className:"table-divider-line bg-grey-light"}),t.recent.length>0?a.createElement(L,{handleUnsaved:m,recentMigrations:t.recent,isSaving:c,savedProfilesEmpty:0===t.saved.length,isSavingRecent:s,profileLoading:e.profileLoading}):a.createElement("div",{className:"no-items-wrap".concat(1===t.saved.length&&0===t.recent.length?" one-profile":"")},a.createElement("p",{className:"no-items"},(0,E.ZP)((0,w.gB)((0,w.__)('There are no recent migrations &mdash; <a href="%s">Get started</a>.',"wp-migrate-db"),"#migrate")))))),a.createElement(l.rU,{to:"/migrate",className:"btn new-migration",replace:!0,onClick:function(){n({type:"RESET_APP"})}},a.createElement(j,{"aria-hidden":"true"})," ",(0,w.__)("New Migration","wp-migrate-db")))})),oe=n(8096),le=v.ZP.div(re||(re=(0,h.Z)(["\n  animation-duration: ",";\n  animation-timing-function: ",";\n  animation-delay: ",";\n  animation-iteration-count: ",";\n  animation-direction: ",";\n  animation-fill-mode: ",";\n  animation-play-state: ",";\n  display: ",";\n"])),(function(e){return e.duration}),(function(e){return e.timingFunction}),(function(e){return e.delay}),(function(e){return e.iterationCount}),(function(e){return e.direction}),(function(e){return e.fillMode}),(function(e){return e.playState}),(function(e){return e.display}));le.defaultProps={duration:"1s",timingFunction:"ease",delay:"0s",iterationCount:"1",direction:"normal",fillMode:"both",playState:"running",display:"block"};var ce,ue,pe,de=le,me=n(3460),fe=n(62457),_e=n(66866),ge=["children","animation","bgAnimation","setRunning","shouldClose"],be=v.ZP.div(ce||(ce=(0,h.Z)(["\n  position: fixed; /* Stay in place */\n  z-index: 1; /* Sit on top */\n  left: 0;\n  top: 0;\n  display: flex;\n  justify-content: center;\n  width: 100%; /* Full width */\n  height: 100%; /* Full height */\n  overflow: auto; /* Enable scroll if needed */\n  background-color: rgb(0, 0, 0); /* Fallback color */\n  background-color: rgba(0, 0, 0, 0.6); /* Black w/ opacity */\n"]))),he=(0,v.ZP)(de)(ue||(ue=(0,h.Z)(["\n  animation-name: ",";\n  position: absolute;\n"])),(function(e){return e.animation})),ve=(0,v.ZP)(de)(pe||(pe=(0,h.Z)(["\n  animation-name: ",";\n  z-index: 999999;\n  position: relative;\n"])),(function(e){return e.bgAnimation})),Ee=null;(0,_.Yu)()||(Ee=a.lazy((function(){return Promise.all([n.e(532),n.e(897)]).then(n.bind(n,80897))})));var we,ye,xe,ke,Ze,Te=function(e){var t=e.children,n=e.animation,r=e.bgAnimation,s=e.setRunning,o=e.shouldClose,l=(0,Z.Z)(e,ge),c=l.complete?" migration-complete":"",u=(0,i.I0)();return oe.createPortal(a.createElement(ve,{bgAnimation:r,className:"modal-bg"},a.createElement(be,{onClick:l.onClick},a.createElement(he,{className:"modal-container",duration:"0.8s",delay:"0.2s",animation:n,onAnimationEnd:function(){u(o?s(!1):function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,t((0,me.Z6)("INITIATE_MIGRATION"));case 3:e.next=9;break;case 5:e.prev=5,e.t0=e.catch(0),console.error(e.t0),t((0,fe.m)({error_type:_e.gF,error_message:e.t0}));case 9:case"end":return e.stop()}}),e,null,[[0,5]])})));return function(t){return e.apply(this,arguments)}}())}},a.createElement(S(),{active:!o},a.createElement("div",{className:"migration-progress".concat(c)},t)),Ee&&a.createElement(a.Suspense,{fallback:a.createElement("div",null,(0,w.__)("Loading...","wp-migrate-db"))},a.createElement(Ee,l))))),document.getElementById("wpmdb-main"))},Se=(0,v.F4)(we||(we=(0,h.Z)(["\n  from {\n    opacity: 0;\n    padding-top: 0;\n  }\n  to {\n    opacity: 1;\n    padding-top: 64px;\n  }\n"]))),Ne=(0,v.F4)(ye||(ye=(0,h.Z)(["\n  from {\n    opacity: 1;\n    padding-top: 64px;\n  }\n  to {\n    opacity: 0;\n    padding-top: 40px;\n  }\n"]))),Pe=(0,v.F4)(xe||(xe=(0,h.Z)(["\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n"]))),Oe=(0,v.F4)(ke||(ke=(0,h.Z)(["\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n"]))),Ae=n(58900);function Ce(){return Ce=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ce.apply(this,arguments)}var Re,Ie=function(e){return a.createElement("svg",Ce({xmlns:"http://www.w3.org/2000/svg",width:52,height:52,viewBox:"0 0 52 52"},e),Ze||(Ze=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{cx:26,cy:26,r:26,fill:"#EA4C49",opacity:.202}),a.createElement("circle",{cx:26,cy:26,r:19.067,fill:"#EA4C49"}),a.createElement("rect",{width:3.467,height:12.711,x:24.267,y:16.756,fill:"#FFF",rx:1.733}),a.createElement("circle",{cx:26,cy:33.511,r:1.733,fill:"#FFF"}))))},De=(n.p,n(38068)),Me=n(42222),Fe=(0,i.$j)((function(e){var t=(0,J.xg)("stage_size",e),n=(0,J.xg)("timer",e);return{running_size:t,timerOn:n.on,timerTime:n.time,timerStart:n.start}}),{startTimer:De.Xg,stopTimer:De.N6,resetTimer:De.yo})((function(e){var t=e.timerTime,n=(0,Me.i2)(t,!0),r=(0,Me.er)(t,!0),i=(0,Me.$s)(t,!0);return a.createElement("div",{className:"timer"},a.createElement("div",{className:"timer-display"},i," : ",r," : ",n))})),Le=n(34374),Be=n(15389),Ue=JSON.parse('{"v":"5.5.4","fr":30,"ip":0,"op":40,"w":140,"h":140,"nm":"Success Checkmark","ddd":0,"assets":[],"layers":[{"ddd":0,"ind":1,"ty":4,"nm":"arrow 8","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.611},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[3.212,-3.212,0],"ti":[-7.639,7.639,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.514},"t":30,"s":[88.752,49.779,0],"to":[4.253,-4.253,0],"ti":[-1.788,1.788,0]},{"t":37,"s":[100,40.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":-3,"bm":0},{"ddd":0,"ind":2,"ty":4,"nm":"arrow 7","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.603},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[4.282,0,0],"ti":[-10.185,0,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.498},"t":30,"s":[95.503,70.031,0],"to":[5.671,0,0],"ti":[-2.384,0,0]},{"t":37,"s":[110,70.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":3,"ty":4,"nm":"arrow 6","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.611},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[3.212,3.212,0],"ti":[-7.639,-7.639,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.514},"t":30,"s":[88.752,90.283,0],"to":[4.253,4.253,0],"ti":[-1.788,-1.788,0]},{"t":37,"s":[100,100.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":4,"ty":4,"nm":"arrow 5","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.625},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[0,4.282,0],"ti":[0,-10.185,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.556},"t":30,"s":[70,97.034,0],"to":[0,5.671,0],"ti":[0,-2.384,0]},{"t":37,"s":[70,110.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":5,"ty":4,"nm":"arrow 4","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.642},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[-2.944,2.409,0],"ti":[7.002,-5.729,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.614},"t":30,"s":[49.936,85.22,0],"to":[-3.899,3.19,0],"ti":[1.639,-1.341,0]},{"t":37,"s":[42.5,92.531,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":6,"ty":4,"nm":"arrow 3","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.645},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[-4.282,0,0],"ti":[10.185,0,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.628},"t":30,"s":[41.497,70.031,0],"to":[-5.671,0,0],"ti":[2.384,0,0]},{"t":37,"s":[30,70.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":7,"ty":4,"nm":"arrow 2","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.638},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,70.031,0],"to":[-3.212,-3.212,0],"ti":[7.639,7.639,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.599},"t":30,"s":[48.248,49.779,0],"to":[-4.253,-4.253,0],"ti":[1.788,1.788,0]},{"t":37,"s":[40,40.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":8,"ty":4,"nm":"arrow 1","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10,"x":"var $bm_rt;\\n$bm_rt = $bm_div($bm_mul(index, 360), 8);"},"p":{"a":1,"k":[{"i":{"x":0.578,"y":0.614},"o":{"x":0.167,"y":0.167},"t":22,"s":[70,69.281,0],"to":[0,-4.283,0],"ti":[0,10.186,0]},{"i":{"x":0.703,"y":1},"o":{"x":0.344,"y":0.556},"t":30,"s":[69.75,43.025,0],"to":[0,-5.669,0],"ti":[0,2.384,0]},{"t":37,"s":[70,30.031,0]}],"ix":2},"a":{"a":0,"k":[-3.196,-17.961,0],"ix":1},"s":{"a":0,"k":[67.6,67.6,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0]],"o":[[0,0],[0,0]],"v":[[-3.173,-27.073],[-3.219,-56.647]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427451010311,0.90588241278,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false},{"ty":"tm","s":{"a":0,"k":0,"ix":1},"e":{"a":0,"k":100,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":29,"s":[35]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[100]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":36,"s":[100]},{"t":37,"s":[70]}],"ix":1},"e":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":33,"s":[0]},{"t":37,"s":[79]}],"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":3,"nm":"Trim Paths 2","mn":"ADBE Vector Filter - Trim","hd":false}],"ip":22,"op":37,"st":0,"bm":0},{"ddd":0,"ind":9,"ty":4,"nm":"Glow","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[0]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":2,"s":[10]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":22,"s":[50]},{"t":24,"s":[0]}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[67.13,75.69,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.689,0.689,0.689],"y":[1,1,1]},"o":{"x":[0.254,0.254,0.254],"y":[0.254,0.254,10.184]},"t":0,"s":[25,25,100]},{"i":{"x":[0.833,0.833,0.833],"y":[0.833,0.833,0.833]},"o":{"x":[0.167,0.167,0.167],"y":[0.167,0.167,0.167]},"t":18,"s":[150,150,100]},{"t":24,"s":[126,126,100]}],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"d":1,"ty":"el","s":{"a":0,"k":[63.763,63.763],"ix":2},"p":{"a":0,"k":[0,0],"ix":3},"nm":"Ellipse Path 1","mn":"ADBE Vector Shape - Ellipse","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":0,"s":[60]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":6,"s":[50]},{"t":24,"s":[1]}],"ix":5},"lc":1,"lj":1,"ml":4,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"tr","p":{"a":0,"k":[1.881,-4.119],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Ellipse 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":40,"st":0,"bm":0},{"ddd":0,"ind":10,"ty":4,"nm":"Check Mark","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[70,70,0],"ix":2},"a":{"a":0,"k":[-1.312,6,0],"ix":1},"s":{"a":0,"k":[100,100,100],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"ind":0,"ty":"sh","ix":1,"ks":{"a":0,"k":{"i":[[0,0],[0,0],[0,0]],"o":[[0,0],[0,0],[0,0]],"v":[[-15.75,8],[-8,16],[13.125,-4]],"c":false},"ix":2},"nm":"Path 1","mn":"ADBE Vector Shape - Group","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":25,"s":[0]},{"t":33,"s":[100]}],"ix":1},"e":{"a":0,"k":0,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"st","c":{"a":0,"k":[1,1,1,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"tr","p":{"a":0,"k":[0,0],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Shape 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":40,"st":0,"bm":0},{"ddd":0,"ind":11,"ty":4,"nm":"Circle Flash","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":25,"s":[0]},{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":30,"s":[98]},{"t":38,"s":[0]}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[70,70,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":25,"s":[0,0,100]},{"t":30,"s":[100,100,100]}],"ix":6}},"ao":0,"shapes":[{"d":1,"ty":"el","s":{"a":0,"k":[64,64],"ix":2},"p":{"a":0,"k":[0,0],"ix":3},"nm":"Ellipse Path 1","mn":"ADBE Vector Shape - Ellipse","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false}],"ip":0,"op":40,"st":0,"bm":0},{"ddd":0,"ind":12,"ty":4,"nm":"Circle Stroke","sr":1,"ks":{"o":{"a":0,"k":100,"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[68.925,68.925,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":16,"s":[100,100,100]},{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":22,"s":[80,80,100]},{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":25,"s":[120,120,100]},{"t":29,"s":[100,100,100]}],"ix":6}},"ao":0,"shapes":[{"ty":"gr","it":[{"d":1,"ty":"el","s":{"a":0,"k":[60,60],"ix":2},"p":{"a":0,"k":[0,0],"ix":3},"nm":"Ellipse Path 1","mn":"ADBE Vector Shape - Ellipse","hd":false},{"ty":"tm","s":{"a":1,"k":[{"i":{"x":[0.667],"y":[1]},"o":{"x":[0.333],"y":[0]},"t":0,"s":[0]},{"t":16,"s":[100]}],"ix":1},"e":{"a":0,"k":0,"ix":2},"o":{"a":0,"k":0,"ix":3},"m":1,"ix":2,"nm":"Trim Paths 1","mn":"ADBE Vector Filter - Trim","hd":false},{"ty":"st","c":{"a":0,"k":[0.137254901961,0.427450980392,0.905882352941,1],"ix":3},"o":{"a":0,"k":100,"ix":4},"w":{"a":0,"k":3,"ix":5},"lc":2,"lj":2,"bm":0,"nm":"Stroke 1","mn":"ADBE Vector Graphic - Stroke","hd":false},{"ty":"tr","p":{"a":0,"k":[0.978,0.978],"ix":2},"a":{"a":0,"k":[0,0],"ix":1},"s":{"a":0,"k":[100,100],"ix":3},"r":{"a":0,"k":0,"ix":6},"o":{"a":0,"k":100,"ix":7},"sk":{"a":0,"k":0,"ix":4},"sa":{"a":0,"k":0,"ix":5},"nm":"Transform"}],"nm":"Ellipse 1","np":3,"cix":2,"bm":0,"ix":1,"mn":"ADBE Vector Group","hd":false}],"ip":0,"op":40,"st":0,"bm":0},{"ddd":0,"ind":13,"ty":4,"nm":"Circle Fill","sr":1,"ks":{"o":{"a":1,"k":[{"i":{"x":[0.833],"y":[0.833]},"o":{"x":[0.167],"y":[0.167]},"t":21,"s":[0]},{"t":28,"s":[98]}],"ix":11},"r":{"a":0,"k":0,"ix":10},"p":{"a":0,"k":[70,70,0],"ix":2},"a":{"a":0,"k":[0,0,0],"ix":1},"s":{"a":1,"k":[{"i":{"x":[0.667,0.667,0.667],"y":[1,1,1]},"o":{"x":[0.333,0.333,0.333],"y":[0,0,0]},"t":21,"s":[0,0,100]},{"t":28,"s":[100,100,100]}],"ix":6}},"ao":0,"shapes":[{"d":1,"ty":"el","s":{"a":0,"k":[64,64],"ix":2},"p":{"a":0,"k":[0,0],"ix":3},"nm":"Ellipse Path 1","mn":"ADBE Vector Shape - Ellipse","hd":false},{"ty":"fl","c":{"a":0,"k":[0.137254908681,0.427450984716,0.905882358551,1],"ix":4},"o":{"a":0,"k":100,"ix":5},"r":1,"bm":0,"nm":"Fill 1","mn":"ADBE Vector Graphic - Fill","hd":false}],"ip":0,"op":40,"st":0,"bm":0}],"markers":[]}'),je=n(73042);function ze(){return ze=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ze.apply(this,arguments)}var Ge,Ve,He=function(e){return a.createElement("svg",ze({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24"},e),Re||(Re=a.createElement("path",{fill:"#236DE7",d:"M22 5.894a8.653 8.653 0 0 1-2.363.638A4.026 4.026 0 0 0 21.441 4.3a8.272 8.272 0 0 1-2.6.978A4.139 4.139 0 0 0 15.846 4c-2.27 0-4.097 1.814-4.097 4.038 0 .32.027.628.095.92a11.666 11.666 0 0 1-8.451-4.222 4.017 4.017 0 0 0-.562 2.04c0 1.399.732 2.638 1.821 3.356A4.098 4.098 0 0 1 2.8 9.634v.045c0 1.962 1.421 3.591 3.285 3.967-.334.09-.697.132-1.075.132a3.68 3.68 0 0 1-.776-.068 4.132 4.132 0 0 0 3.831 2.812 8.32 8.32 0 0 1-5.084 1.722c-.336 0-.659-.015-.981-.056A11.674 11.674 0 0 0 8.29 20c7.545 0 11.67-6.154 11.67-11.488 0-.178-.006-.35-.015-.522A8.111 8.111 0 0 0 22 5.894Z"})))},We=(n.p,function(e,t,n,r){var a=(0,Me.UJ)(e),i=(0,Me.TJ)(e),s=(0,Ae.s2)(t.time),o=(0,w.__)("I've just %1$s my %2$s %3$s WordPress database in %4$s using @wpmigratedbpro","wp-migrate-db"),l=(0,w.__)("I've just migrated my %1$s %2$s WordPress %3$s in %4$s using @wpmigratedbpro","wp-migrate-db");switch(n){case"push":case"pull":var c=(0,w.__)("database","wp-migrate-db");return["media_files","plugin_files","theme_files","muplugin_files","other_files"].some((function(e){return r.includes(e)}))&&(c=(0,w.__)("site","wp-migrate-db")),(0,w.gB)(l,a,i,c,s);case"backup_local":case"savefile":case"import":case"find_replace":var u=function(e){switch(e){case"import":return(0,w.__)("imported","wp-migrate-db");case"find_replace":return(0,w.__)("updated","wp-migrate-db");case"backup_local":return(0,w.__)("backed up","wp-migrate-db");default:return(0,w.__)("exported","wp-migrate-db")}}(n);return(0,w.gB)(o,u,a,i,s);default:return""}}),Ye=(0,i.$j)((function(e){return{migration_size:(0,J.xg)("migration_size",e),timer:(0,J.xg)("timer",e),stages:(0,J.r5)("stages",e),intent:(0,J.r5)("intent",e)}}),{})((function(e){var t=e.migration_size,n=e.timer,r=e.stages,i=e.intent;return a.createElement("a",{href:"https://twitter.com/intent/tweet?url=".concat(encodeURI("https://deliciousbrains.com/wp-migrate-db-pro/ "),"&text=").concat(We(t,n,i,r)),className:"twitter",title:"@dliciousbrains twitter link",target:"_blank",rel:"noopener noreferrer"},a.createElement(He,null))})),Ke=n(29816),qe=n(80401),Je=function(e){var t=e.link,n=e.content,r=e.utmContent,i=e.utmCampaign,s=e.screenReaderText,o=e.anchorLink,l=void 0===o?"":o,c=e.hasArrow,u=void 0!==c&&c;return a.createElement(qe.Z,{link:t,content:n,utmContent:r,utmCampaign:i,classes:"btn btn-sm btn-stroke release-post-link",screenReaderText:s,hasArrow:u,anchorLink:l})};function Xe(){return Xe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xe.apply(this,arguments)}var Qe,$e,et=function(e){return a.createElement("svg",Xe({width:94,height:36,viewBox:"0 0 94 36",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Ge||(Ge=a.createElement("g",{clipPath:"url(#clip0_60_1330)"},a.createElement("path",{d:"M69.008 24.706c1.492 0 2.882-.576 4.046-1.666.062-.041.123-.123.184-.185.164-.185.143-.39.02-.597 0 0-.388-.637-.49-.781-.102-.144-.184-.206-.306-.227-.143-.02-.286.062-.45.206-.94.802-1.92 1.214-2.922 1.214-2.697 0-3.924-2.572-3.924-4.958 0-2.366 1.206-4.752 3.924-4.752.92 0 1.88.35 2.78 *************.47.144.592-.062l.51-.864c.164-.288.123-.473-.06-.658a1.166 1.166 0 0 0-.164-.144 5.862 5.862 0 0 0-3.74-1.317c-1.532 0-2.881.555-3.944 1.625-1.267 1.276-1.941 3.127-1.941 5.205 0 1.995.572 3.703 1.574 4.916 1.021 1.255 2.533 2.016 4.311 2.016ZM55.746 10.759c-3.413 0-5.967 2.942-5.967 6.85 0 4.012 2.554 7.036 5.967 7.036 3.392 0 5.947-3.024 5.947-7.036 0-3.888-2.555-6.85-5.947-6.85Zm0 11.808c-2.187 0-3.76-2.098-3.76-4.958 0-2.798 1.553-4.772 3.76-4.772s3.76 1.975 3.76 4.772c0 2.942-1.553 4.958-3.76 4.958ZM48.92 22.464h-4.842V11.397a.411.411 0 0 0-.409-.412h-1.328a.411.411 0 0 0-.409.412v12.651c0 .226.184.412.409.412h6.58a.411.411 0 0 0 .409-.412v-1.172a.411.411 0 0 0-.41-.412ZM93.428 22.464h-4.843V11.397a.411.411 0 0 0-.409-.412h-1.328a.411.411 0 0 0-.409.412v12.651c0 .226.184.412.409.412h6.58a.411.411 0 0 0 .409-.412v-1.172a.411.411 0 0 0-.41-.412ZM84.763 23.925l-4.087-12.631a.418.418 0 0 0-.388-.288H78.92c-.163 0-.286.103-.347.247l-4.149 12.672a.406.406 0 0 0 .389.534h1.328a.418.418 0 0 0 .388-.287l1.104-3.477h3.964l1.124 3.477a.418.418 0 0 0 .388.287h1.247c.306 0 .49-.267.408-.534Zm-6.64-5.143 1.225-3.868c.102-.308.205-.72.286-***********.184.762.286 1.07l1.227 3.868h-3.025Z",fill:"#53BA7D"}),a.createElement("g",{clipPath:"url(#clip1_60_1330)",fill:"#53BB7D"},a.createElement("path",{d:"M17.004 7.22V1.27c0-.41-.264-.778-.63-.942-.365-.163-.812-.06-1.096.225L2.113 13.806C.71 15.217.04 16.587.02 18.019c0 1.432.65 2.781 2.032 4.172l13.226 13.314a.95.95 0 0 0 .71.307c.122 0 .265-.02.387-.123a1.02 1.02 0 0 0 .63-.94v-6.013c0-.266-.123-.532-.305-.716L6.725 17.978 16.7 7.936a.963.963 0 0 0 .304-.715Zm-2.031 21.966v3.129l-9.63-9.694c-.04-.062-.081-.123-.142-.184-.305-.307-1.22-1.228.142-2.946l9.63 9.695Zm0-22.354L4.429 17.446c-.813.818-1.361 1.677-1.646 2.516-.508-.675-.751-1.309-.751-1.922.02-.86.508-1.78 1.503-2.782L14.973 3.744v3.088Z"}),a.createElement("path",{d:"M9.548 17.998v.082c0 .43.346.777.772.777h15.095a.774.774 0 0 0 .772-.777V18a.774.774 0 0 0-.772-.778H10.32a.774.774 0 0 0-.772.777ZM12.454 15.033v.082c0 .43.345.777.772.777h9.264a.774.774 0 0 0 .772-.777v-.082a.774.774 0 0 0-.772-.777h-9.264a.774.774 0 0 0-.772.777ZM19.625 12.906a.774.774 0 0 0 .772-.777v-.082a.774.774 0 0 0-.772-.777h-3.677a.774.774 0 0 0-.772.777v.082c0 .43.345.777.772.777h3.677ZM15.948 24.87h3.677a.774.774 0 0 0 .772-.777v-.082a.774.774 0 0 0-.772-.777h-3.677a.774.774 0 0 0-.772.777v.082c0 .43.345.777.772.777ZM13.226 21.864h9.264a.774.774 0 0 0 .772-.777v-.082a.774.774 0 0 0-.772-.777h-9.264a.774.774 0 0 0-.772.777v.082c0 .43.345.777.772.777Z"}),a.createElement("path",{d:"M33.826 13.888 20.6.574a.993.993 0 0 0-1.097-.225 1.02 1.02 0 0 0-.63.94v6.034c0 .266.122.532.305.716l9.975 10.041-9.975 10.042a.963.963 0 0 0-.305.716v5.951c0 .41.244.777.63.941a.876.876 0 0 0 .386.082c.264 0 .508-.102.711-.286l13.165-13.253c1.401-1.411 2.072-2.782 2.092-4.213 0-1.432-.65-2.781-2.031-4.172ZM20.905 6.873v-3.13l9.63 9.695c.*************.142.184.304.307 1.219 1.227-.142 2.945l-9.63-9.694ZM32.322 20.8 20.884 32.315v-3.068l10.544-10.635c.813-.818 1.361-1.677 1.646-2.515.508.674.752 1.308.752 1.922-.02.859-.508 1.78-1.504 2.781Z"})))),Ve||(Ve=a.createElement("defs",null,a.createElement("clipPath",{id:"clip0_60_1330"},a.createElement("path",{fill:"#fff",d:"M0 0h94v36H0z"})),a.createElement("clipPath",{id:"clip1_60_1330"},a.createElement("path",{fill:"#fff",transform:"translate(.02 .206)",d:"M0 0h35.959v35.79H0z"})))))},tt=(n.p,n(67072),function(e){var t="modal"===e.location?"local-callout-export-modal":"local-callout-migrate-tab";return a.createElement("div",{className:"local-callout migrate-notice"},a.createElement(et,{className:"local-logo"}),a.createElement("p",{className:"callout-content"},a.createElement("span",{className:"question"},(0,w.__)("Need to work on this site in a local development environment?","wp-migrate-db"))," ",a.createElement("span",{className:"answer"},(0,w.__)("Drag and drop an exported ZIP archive into Local. It's that easy!","wp-migrate-db"))),a.createElement(Je,{link:"https://deliciousbrains.com/wp-migrate-db-pro/doc/importing-wordpress-local-development-environment/",content:(0,w.__)("Learn More","wp-migrate-db"),utmContent:t,utmCampaign:"wp-migrate-documentation",screenReaderText:(0,w.__)("about importing into Local","wp-migrate-db")}))}),nt=["title","intent","progress_message","closeModal","fileTransferStats","migration_id","highPerformanceTransfersStatus","stages_complete"],rt=(0,i.$j)((function(e){var t=(0,J.xg)("title",e),n=(0,J.r5)("intent",e),r=(0,J.r5)("migration_id",e),a=(0,J.r5)(["highPerformanceTransfersStatus","fileTransferStats","stages_complete"],e),i=(0,k.Z)(a,3),s=i[0],o=i[1],l=i[2];return{title:t,progress_message:(0,J.xg)("progress_message",e),intent:n,fileTransferStats:o,migration_id:r,highPerformanceTransfersStatus:s,stages_complete:l}}),{})((function(e){var t=e.title,n=e.intent,r=e.progress_message,i=e.closeModal,s=e.fileTransferStats,o=e.migration_id,l=(e.highPerformanceTransfersStatus,e.stages_complete),u=((0,Z.Z)(e,nt),{loop:!1,autoplay:!0,animationData:Ue,rendererSettings:{preserveAspectRatio:"xMidYMid slice"}}),p=(0,c.k6)(),d=function(e){27===e.keyCode&&i()};(0,a.useEffect)((function(){return window.addEventListener("keydown",d),function(){window.removeEventListener("keydown",d)}}),[]);var m=l&&l.some((function(e){return Ke.AG.includes(e)}));return a.createElement("div",{className:"migration-complete"},a.createElement("div",{className:"column flex-container"},a.createElement("button",{className:"popup-close align-right btn-no-outline",onClick:function(){return i()}},a.createElement(je.r,{"aria-label":(0,w.__)("Close migration modal","wp-migrate-db")})),a.createElement("h1",{className:"migration-title"},(0,E.ZP)(t)),a.createElement(Le.Z,{options:u,"aria-hidden":"true",tabIndex:-1,"aria-label":"",height:156,width:156}),a.createElement("p",{className:"migration-complete-summary"},(0,E.ZP)(r)),l&&m&&a.createElement(Be.Z,{datas:s,columns:[{id:"currentStage",displayName:"Stage"},{id:"status",displayName:"Status"},{id:"maxPayloadSize",displayName:"Max Request Size (MB)"},{id:"currRequestSize",displayName:"Actual Request Size (MB)"},{id:"time",displayName:"Time"}],filename:"".concat(o,"-file-stats"),className:"btn btn-sm csv-download-btn",text:(0,w.__)("Download Report","wp-migrate-db")})),a.createElement("div",{className:"flex-container pos-relative"},"savefile"===n&&a.createElement(tt,{location:"modal"}),(0,_.Yu)()&&"savefile"!==n&&a.createElement(a.Fragment,null,a.createElement("button",{className:"btn ".concat(m?"btn-stroke":""," btn-sm migration-complete-close-btn"),onClick:function(){return function(){if("backup_local"===n&&(0,_.Yu)())return document.body.style.overflow="auto",void p.push("/backups");i()}()},autoFocus:!0},"backup_local"===n?(0,w.__)("View Backups","wp-migrate-db"):(0,w.__)("Close","wp-migrate-db")),a.createElement(Ye,null))),!(0,_.Yu)()&&a.createElement("div",{className:"rate-mdb"},(0,E.ZP)((0,w.gB)((0,w.__)('Please consider <a href="%s" target="_blank" rel="noopener noreferrer">reviewing WP Migrate</a> on WordPress.org.',"wp-migrate-db"),"https://wordpress.org/support/plugin/wp-migrate-db/reviews/?filter=5#new-post"))))})),at=n(83115),it=v.ZP.div(Qe||(Qe=(0,h.Z)(["\n  height: 5px;\n  width: ","%;\n  background: blue;\n  transition: 0.4s linear;\n  transition-property: width, background-color;\n"])),(function(e){return e.width})),st=(0,i.$j)((function(e){var t=e.dry_run.results,n=(0,J.r5)("preview",e),r=(0,J.xg)("item_progress",e);return{running_size:(0,J.xg)("stage_size",e),total_stage_size:(0,J.xg)("total_stage_size",e),isPreview:n,tables_selected:t,item_progress:r,progress_stage:(0,J.xg)("progress_stage",e),status:(0,J.xg)("status",e),showDryRunResults:(0,J.xg)("showDryRunResults",e)}}),{})((function(e){var t=e.running_size,n=e.total_stage_size,r=e.isPreview,s=e.tables_selected,o=e.item_progress,l=e.showDryRunResults,c=e.progress_stage,u=e.status,p=(0,Me.UJ)(n),d=(0,Me.UJ)(t),m=(0,i.I0)(),f=Math.round(parseInt(t)/n*100)||0;(0,a.useEffect)((function(){!l&&"COMPLETE"===c&&"PAUSED"===u&&r&&m((0,at.dy)(!0))}));var g=(0,_.Yu)();return a.createElement(a.Fragment,null,a.createElement("div",{className:"flex-container flex-align-baseline"},a.createElement("p",{className:"migration-percentage"},f,"%"),a.createElement("p",{className:"migration-data-transferred semibold"},"(",d," ",(0,Me.TJ)(t)," /"," ",p," ",(0,Me.TJ)(n),")"),r&&a.createElement(a.Fragment,null,a.createElement("p",{className:"migration-tables-searched"},Object.keys(o).length," ",g?"of ".concat(s.length," "):"",(0,w.__)("Tables Searched","wp-migrate-db")))),a.createElement("div",{className:"migration-progress-bar bg-grey-light"},a.createElement(it,{width:f,className:"migration-progress-bar-running bg-primary"})))})),ot=n(12544),lt=n.n(ot),ct=n(19085),ut=(0,i.$j)((function(e){return{current_stage:(0,J.r5)("current_stage",e),stages_complete:(0,J.r5)("stages_complete",e)}}),{})((function(e){var t=e.current_stage,n=e.stage,r=e.stages_complete,i=e.hasError,s=e.status;return i?a.createElement(je.r,null):lt()(r,n)?a.createElement(C,null):t===n||"migrate"===t&&"tables"===n||"find_replace"===t&&"tables"===n?a.createElement(ct.Q,{className:"PAUSED"===s?"paused":""}):null})),pt={backup:(0,w.__)("Backup","wp-migrate-db"),tables:(0,w.__)("Tables","wp-migrate-db"),upload:(0,w.__)("Upload","wp-migrate-db"),import:(0,w.__)("Import","wp-migrate-db"),find_replace:(0,w.__)("Find & Replace","wp-migrate-db"),theme_files:(0,w.__)("Themes","wp-migrate-db"),plugin_files:(0,w.__)("Plugins","wp-migrate-db"),muplugin_files:(0,w.__)("Must-Use Plugins","wp-migrate-db"),other_files:(0,w.__)("Other Files","wp-migrate-db"),core_files:(0,w.__)("WordPress Core Files","wp-migrate-db"),media_files:(0,w.__)("Media Uploads","wp-migrate-db")},dt=n(27114),mt=function(e){var t=(0,i.v9)((function(e){return e.migrations.migration_progress})).status,n=(0,i.v9)((function(e){return e.migrations.current_migration})),r=n.intent,s=n.current_stage,o="";(0,_.Yu)()&&(o=(0,i.v9)((function(e){return e.migrations.remote_site})).url);var l={savefile:(0,w.__)("Export","wp-migrate-db"),import:(0,w.__)("Import","wp-migrate-db"),push:(0,w.__)("Push","wp-migrate-db"),pull:(0,w.__)("Pull","wp-migrate-db"),backup_local:(0,w.__)("Backup","wp-migrate-db"),find_replace:(0,w.__)("Find & Replace","wp-migrate-db")},c={migrate:(0,w.__)("Table Migration","wp-migrate-db"),backup:(0,w.__)("Backup","wp-migrate-db"),media_files:(0,w.__)("Media","wp-migrate-db"),find_replace:"import"===r?(0,w.__)("Find & Replace","wp-migrate-db"):(0,w.__)("Migrate Tables","wp-migrate-db"),upload:(0,w.__)("Upload","wp-migrate-db"),import:(0,w.__)("Import","wp-migrate-db"),theme_files:(0,w.__)("Themes","wp-migrate-db"),plugin_files:(0,w.__)("Plugins","wp-migrate-db"),muplugin_files:(0,w.__)("Must-Use Plugins","wp-migrate-db"),other_files:(0,w.__)("Other Files","wp-migrate-db"),core_files:(0,w.__)("WordPress Core Files","wp-migrate-db")},u=function(e){return"backup_local"===e?"":["push","pull"].includes(e)?'<span class="black">'.concat("push"===e?(0,w.__)("to","wp-migrate-db"):(0,w.__)("from","wp-migrate-db"),"</span> ").concat((0,_.zj)(o)):"migration"},p=function(e){return"initiate_migration"===s?a.createElement("div",null,(0,E.ZP)((0,w.gB)((0,w.__)("The <span>%s</span> <span>%s</span> failed during the migration initialization stage.","wp-migrate-db"),l[r],u(r)))):a.createElement("div",null,(0,E.ZP)((0,w.gB)((0,w.__)("The <span>%s</span> <span>%s</span> failed during the <span>%s</span> stage.","wp-migrate-db"),l[r],u(r),c[s])))};return a.createElement("div",{className:"mdb-migration-error"},a.createElement("h4",null,a.createElement(p,null)),a.createElement("div",{className:"mdb-migration-error-message"},a.createElement("div",{className:"error-block consolas"},(0,E.ZP)((0,dt.Y)(t)))))},ft=n(38930),_t=(n(34901),function(e){var t=e.table,n=e.onClick,r=e.id,i=(0,_.Yu)(),s=(0,_.ME)();return a.createElement(a.Fragment,null,a.createElement("button",{onClick:n,className:"table-results-button","data-tip":!0,"data-for":"action-tip-dry-run-".concat(r)},t),(!i||s)&&a.createElement(ft.Z,{effect:"solid",place:"left",type:"light",delayUpdate:500,delayHide:250,border:!0,className:"action-tooltip",id:"action-tip-dry-run-".concat(r),getContent:function(){return(0,E.ZP)((0,_.mF)("dryRun",i))}}))}),gt=(0,i.$j)((function(e){var t=e.dry_run.results,n=(0,J.xg)("status",e),r=(0,J.xg)("progress_stage",e);return{results:t,migrationStatus:n,isPreview:(0,J.r5)("preview",e),migrationStage:r}}))((function(e){var t=e.results,n=e.isPreview,r=e.migrationStatus,s=e.migrationStage,o=(0,i.I0)(),l=(0,_.Yu)(),c=(0,_.ME)(),u=function(e){return function(){o((0,at.$1)(e))}};return a.createElement(a.Fragment,null,a.createElement("table",{className:"dry-run-results-table container-shadow table"},a.createElement("thead",null,a.createElement("tr",{className:"flex-container"},a.createElement("th",{className:"table-heading"},(0,w.__)("Table","wp-migrate-db")),a.createElement("th",{className:"table-heading"},(0,w.__)("Matches Found","wp-migrate-db")),a.createElement("th",{className:"table-heading"},(0,w.__)("Time","wp-migrate-db")))),a.createElement("tbody",{tabIndex:0},t.map((function(e,t){return a.createElement("tr",{key:t,className:"flex-container result-item"},a.createElement("td",null,a.createElement("span",{className:"table-name"},e.table)),a.createElement("td",null,!e.executed&&a.createElement("span",{className:"waiting-dash"},"-"),e.executed&&e.count>0&&a.createElement(_t,{table:e.count,onClick:e.count>0&&!c&&l?u(t):null,id:t}),e.executed&&0===e.count&&a.createElement("span",{className:"table-results-none"},e.count)),a.createElement("td",null,!e.executed&&a.createElement("span",{className:"waiting-dash"},"-"),e.executed&&(0,Me.q5)(e.time)))})))),n&&("PAUSED"===r||"CANCELLED"===r)&&"COMPLETE"===s&&a.createElement("p",{className:"dry-run-notice"},a.createElement("strong",null,(0,w.__)("Everything look good?","wp-migrate-db"))," ",(0,w.__)("Apply these changes to update WordPress database. If any data has been modified since this preview was started, that data will be overwritten.","wp-migrate-db")))})),bt=n(56381),ht=n.n(bt),vt=function(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")},Et=function(e){var t=e.item,n=t.column,r=t.row,i=t.original,s=t.replace,o=(0,a.useState)(""),l=(0,k.Z)(o,2),c=l[0],u=l[1];return(0,a.useEffect)((function(){var e=new(ht())({timeout:2,editCost:20}),t=e.main(i,s);e.cleanupSemantic(t);var n=function(e){var t="",n="";for(var r in e){var a=(0,k.Z)(e[r],2),i=a[0],s=a[1];if(0!==i)0>i?t+="<del>".concat(vt(s),"</del>"):0<i&&(t+="<ins>".concat(vt(s),"</ins>"));else{var o=s;s.length>=100&&(o="".concat(s.slice(0,25),"...").concat(s.slice(-25))),t+="<span>".concat(vt(o),"</span>"),n+="<span>".concat(vt(o),"</span>")}}return[t,n]}(t),r=(0,k.Z)(n,1)[0];u(r)}),[t]),a.createElement("li",{className:"dry-run-result"},a.createElement("div",{className:"dry-run-result-meta"},a.createElement("span",null,a.createElement("strong",null,(0,w.__)("Row ID:","wp-migrate-db"))," ",r),a.createElement("span",null,a.createElement("strong",null,(0,w.__)("Column:","wp-migrate-db"))," ",n)),a.createElement("code",{className:"dry-run-result-code"},(0,E.ZP)(c)))},wt=function(e){var t=e.currentPage,n=e.pageCount,r=e.setCurrentPage,i=e.scrollableContentRef;return a.createElement("div",{className:"pagination-controls"},a.createElement("span",null,t," of ",0===n?1:n),a.createElement("button",{className:"btn btn-sm btn-stroke migration-progress-btn ".concat(1===t?"btn-disabled":""),onClick:function(){i.current.scrollTop=0,r(t-1)},disabled:1===t},(0,w.__)("Prev","wp-migrate-db")),a.createElement("button",{className:"btn btn-sm btn-stroke migration-progress-btn ".concat(t>=n?"btn-disabled":""),onClick:function(){i.current.scrollTop=0,r(t+1)},disabled:t>=n},(0,w.__)("Next","wp-migrate-db")))},yt=(0,i.$j)((function(e){return{currentPreviewItem:e.dry_run.currentPreviewItem,results:e.dry_run.results}}))((function(e){var t=e.currentPreviewItem,n=e.results,r=(0,a.useState)(null),i=(0,k.Z)(r,2),s=i[0],o=i[1],l=(0,a.useState)([]),c=(0,k.Z)(l,2),u=c[0],p=c[1],d=(0,a.useState)(1),m=(0,k.Z)(d,2),f=m[0],_=m[1],g=(0,a.useState)(0),b=(0,k.Z)(g,2),h=b[0],v=b[1],E=(0,a.useRef)();return(0,a.useEffect)((function(){"undefined"!==typeof n[t]&&o(n[t])}),[t,n[t]]),(0,a.useEffect)((function(){if(s){var e=50*f;p(s.data.slice(e-50,e))}}),[f,s]),(0,a.useEffect)((function(){s&&v(Math.round(s.count/50))}),[s]),null===s?null:a.createElement(a.Fragment,null,a.createElement("div",{className:"flex-container migration-progress-controls dry-run-controls"},a.createElement("h1",null,s.table),h>1&&a.createElement(wt,{currentPage:f,pageCount:h,setCurrentPage:_,scrollableContentRef:E})),a.createElement("ul",{className:"dry-run-results-single-table container-shadow",tabIndex:"0",ref:E},u.map((function(e){return a.createElement(Et,{item:e})}))))})),xt=n(86645),kt=["stages","pause_before_finalize","intent","status","hasError","closer","isPreview","currentDryRunPreviewItem","progress_stage"],Zt=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,n,r,a,i){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if("PAUSED"!==r){e.next=4;break}return e.next=3,a();case 3:return e.abrupt("return",e.sent);case 4:return e.abrupt("return",i());case 5:case"end":return e.stop()}}),e)})));return function(t,n,r,a,i){return e.apply(this,arguments)}}(),Tt=(0,i.$j)((function(e){var t=e.dry_run.currentPreviewItem,n=(0,J.r5)("current_stage",e),r=(0,J.xg)("pause_before_finalize",e),a=(0,J.r5)("preview",e),i=(0,J.r5)("running",e),s=(0,J.r5)("intent",e),o=(0,J._P)("this_temp_prefix",e),l=(0,J.xg)("status",e),c=(0,J.r5)("stages",e),u=(0,J.xg)("title",e),p=(0,J.xg)("progress_stage",e),d=(0,J.r5)("stages_complete",e),m=(0,J.r5)(["highPerformanceTransfersStatus","forceHighPerformanceTransfers"],e),f=(0,k.Z)(m,2),_=f[0],g=f[1];return{current_stage:n,status:l,running:i,intent:s,tmp_prefix:o,total_stage_size:(0,J.xg)("total_stage_size",e),running_size:(0,J.xg)("stage_size",e),stages:c,stages_complete:d,pause_before_finalize:r,title:u,progress_message:(0,J.xg)("progress_message",e),progress_stage:p,showDryRunResults:(0,J.xg)("showDryRunResults",e),isPreview:a,currentDryRunPreviewItem:t,highPerformanceTransfersStatus:_,forceHighPerformanceTransfers:g}}),{setPauseBeforeFinalize:Y.LR,cancelMigration:me.Gn,pauseMigration:me.Kh,resumeMigration:me.Rw})((function(e){var t=e.stages,n=e.pause_before_finalize,r=e.intent,i=e.status,s=e.hasError,o=e.closer,l=e.isPreview,c=e.currentDryRunPreviewItem,u=e.progress_stage,p=(0,Z.Z)(e,kt),d="PAUSED"===i,m="COMPLETE"===u,f=l&&m?(0,w.__)("Apply Changes","wp-migrate-db"):d?(0,w.__)("Resume","wp-migrate-db"):(0,w.__)("Pause","wp-migrate-db"),_=p.current_stage;if("COMPLETE"===p.status)return null;if(null!==c)return a.createElement(yt,null);var g=["media_files","theme_files","plugin_files","other_files","muplugin_files"].includes(_);return a.createElement(a.Fragment,null,"initiate_migration"!==_&&a.createElement(st,null),a.createElement(a.Fragment,null,"initiate_migration"!==_&&a.createElement("ul",{className:"flex-container"},t.map((function(e,t){return a.createElement("li",{className:"migration-progress-steps",key:t},a.createElement(ut,{stage:e,hasError:s,status:i}),pt[e])}))),l&&a.createElement(gt,null),s&&a.createElement(mt,p),g&&a.createElement(xt.Z,null),s?a.createElement("div",{className:"text-center error-btn"},a.createElement("button",{onClick:function(){return o(!0)},className:"btn btn-sm"},(0,w.__)("Close","wp-migrate-db"))):a.createElement("div",{className:"flex-container migration-progress-controls ".concat(l||g?"no-top-margin":"")},a.createElement("button",{onClick:function(){Zt(o,p,i,p.resumeMigration,p.pauseMigration)},className:"btn btn-sm ".concat(l&&m?"":"btn-stroke"," migration-progress-btn")},f)," ",a.createElement("button",{onClick:function(){(0,p.cancelMigration)()},className:"btn btn-sm btn-stroke migration-progress-btn"},(0,w.__)("Cancel","wp-migrate-db")),!["backup_local","savefile"].includes(r)&&"MIGRATE"===u&&!l&&a.createElement("div",{className:"align-right wpmdb-form"},a.createElement("input",{type:"checkbox",className:"checkbox-default",checked:n,id:"pause-before-finalize",onChange:function(){p.setPauseBeforeFinalize(!n)}}),a.createElement("label",{htmlFor:"pause-before-finalize"},(0,w.__)("Pause before replacing migrated tables","wp-migrate-db"))))))})),St=["title","closer","status","hasError"],Nt=["closer","title","status","closeHandler","intent","remoteURL","isPreview","currentPreviewItem"],Pt=function(e){var t=e.closeModal,n=e.isPreview;return a.createElement("div",{className:"migration-progress-content"},a.createElement("div",null,a.createElement("h1",{className:"migration-title cancelled"},window.wpmdb_strings.migration_cancelled),a.createElement("p",null,n&&(0,w.__)("The operation has been stopped and no changes were made.","wp-migrate-db"),!n&&(0,E.ZP)(window.wpmdb_strings.migration_cancelled_success))),a.createElement("div",null,a.createElement("button",{onClick:t,className:"btn btn-sm"},(0,w.__)("Close","wp-migrate-db"))))},Ot=function(e){var t=e.title,n=e.closer,r=e.status,i=e.hasError;(0,Z.Z)(e,St);return a.createElement("div",{className:"migration-progress-content migration-error"},a.createElement("div",{className:"flex-container"},a.createElement("h1",{className:"migration-title flex-container error"},a.createElement(Ie,{className:"error-icon"}),(0,E.ZP)(t)),a.createElement("div",{className:"migration-timer align-right"},a.createElement(Fe,null))),a.createElement(Tt,{closer:n,status:r,hasError:i}))},At=function(e){var t=e.closer,n=e.title,r=e.status,s=e.closeHandler,l=e.intent,c=e.remoteURL,u=e.isPreview,p=e.currentPreviewItem,d=(0,Z.Z)(e,Nt);(0,a.useEffect)((function(){u&&"CANCEL_COMPLETE"===r&&(t(!0),setTimeout((function(){m((0,G.m)(_e.MG,"CANCELLED"))}),300))}),[r]);var m=(0,i.I0)(),f="",g=u&&null!==p,b="object"===typeof r?r.hasOwnProperty("error_type"):void 0;if(b)return a.createElement(Ot,(0,o.Z)({},d,{hasError:b,closer:function(){return t(!0)},title:n}));switch(r){case"COMPLETE":f=a.createElement(rt,{closeModal:function(e){t(!0),s("COMPLETE")}});break;case"CANCEL_COMPLETE":if(!u){f=a.createElement(Pt,(0,o.Z)({},d,{isPreview:u,closeModal:function(){return t(!0)}}));break}default:f=a.createElement("div",{className:"migration-progress-content"},null!==p&&a.createElement("button",{className:"dry-run-back-btn link",onClick:function(){m((0,at.$1)(null))}},"\u2190 ",(0,w.__)("Back to All Tables","wp-migrate-db")),a.createElement("div",{className:"flex-container ".concat(g?"hidden":"")},a.createElement("h1",{className:["push","pull"].includes(l)?"push-pull":null},function(e,t){var n={push:(0,w.gB)((0,w.__)("<span>Pushing</span> to <span>%s</span>","wp-migrate-db"),t),pull:(0,w.gB)((0,w.__)("<span>Pulling</span> from <span>%s</span>","wp-migrate-db"),t),find_replace:(0,w.__)("Find & Replace","wp-migrate-db"),savefile:(0,w.__)("Exporting","wp-migrate-db"),import:(0,w.__)("Importing","wp-migrate-db"),backup_local:(0,w.__)("Backing up","wp-migrate-db")};return(0,E.ZP)(n[e])}(l,(0,_.zj)(c)),u&&a.createElement("span",{className:"preview-migration-heading"},(0,w.__)("(Preview)","wp-migrate-db"))),a.createElement("div",{className:"migration-timer align-right"},a.createElement(Fe,null))),a.createElement("h2",{className:"migration-title flex-container ".concat(g?"hidden":"")},(0,E.ZP)(n)),a.createElement(Tt,{closer:t,status:r,hasError:b}))}return f},Ct=function(e){var t=Se,n=Pe,r=(0,a.useState)(!1),i=(0,k.Z)(r,2),s=i[0],l=i[1];(0,a.useEffect)((function(){return function(){l(!1)}}),[]);return s&&(t=Ne,n=Oe),a.createElement(Te,{animation:t,bgAnimation:n,onClick:function(t){var n=e.status;"COMPLETE"!==n&&"CANCEL_COMPLETE"!==n||t.target===t.currentTarget&&(l(!0),e.closeHandler(n))},complete:"COMPLETE"===e.status,setRunning:me.al,shouldClose:s},a.createElement(At,(0,o.Z)({closer:function(e){l(e)},closeHandler:e.closeHandler},e)))},Rt=(0,i.$j)((function(e){var t=(0,J.r5)("preview",e),n=e.dry_run.currentPreviewItem,r=(0,J.xg)("pause_before_finalize",e),a=(0,J.r5)("running",e),i=(0,J.r5)("intent",e),s=(0,J._P)("this_temp_prefix",e),o=(0,J.xg)("status",e),l=(0,J.r5)("stages",e),c=(0,J.xg)("title",e),u=(0,_.Yu)()?(0,J.FY)("url",e):"",p=(0,J.xg)("progress_stage",e),d=(0,J.r5)("stages_complete",e);return{status:o,running:a,intent:i,tmp_prefix:s,total_stage_size:(0,J.xg)("total_stage_size",e),running_size:(0,J.xg)("stage_size",e),stages:l,stages_complete:d,pause_before_finalize:r,title:c,progress_message:(0,J.xg)("progress_message",e),progress_stage:p,remoteURL:u,isPreview:t,currentPreviewItem:n}}),{cancelMigration:me.Gn,pauseMigration:me.Kh,resumeMigration:me.Rw,setPauseBeforeFinalize:Y.LR,maybeRefresh:Ae.K_,closeHandler:Ae.B3})(a.memo(Ct)),It=n(40795);function Dt(){return Dt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Dt.apply(this,arguments)}var Mt,Ft=function(e){return a.createElement("svg",Dt({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),$e||($e=a.createElement("path",{d:"M6 11V8a6 6 0 1 1 12 0v3h1a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V12a1 1 0 0 1 1-1zm3 0h6V8a3 3 0 0 0-6 0zm3.969 6.75a2 2 0 1 0-1.938 0c-.02.08-.031.164-.031.25v1a1 1 0 0 0 2 0v-1c0-.086-.01-.17-.031-.25z",fill:"#b2b2b2",fillRule:"evenodd"})))},Lt=(n.p,{push:(0,w.__)("Push","wp-migrate-db"),pull:(0,w.__)("Pull","wp-migrate-db"),import:(0,w.__)("Import Database","wp-migrate-db"),savefile:(0,w.__)("Export","wp-migrate-db"),backup_local:(0,w.__)("Backup Database","wp-migrate-db"),find_replace:(0,w.__)("Find & Replace","wp-migrate-db")}),Bt={push:(0,w.__)("Push","wp-migrate-db"),pull:(0,w.__)("Pull","wp-migrate-db"),savefile:(0,w.__)("Export","wp-migrate-db"),import:(0,w.__)("Import Database","wp-migrate-db"),find_replace:(0,w.__)("Find & Replace","wp-migrate-db"),backup_local:(0,w.__)("Backup Database","wp-migrate-db")};n(71989);function Ut(){return Ut=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ut.apply(this,arguments)}var jt,zt=function(e){return a.createElement("svg",Ut({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#326eff",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}},Mt||(Mt=a.createElement("path",{d:"M19.5 12h-14M12.5 4.5l-7 7.5 7 7.5"}))))};n.p;function Gt(){return Gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Gt.apply(this,arguments)}var Vt,Ht=function(e){return a.createElement("svg",Gt({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#326eff",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}},jt||(jt=a.createElement("path",{d:"M20 15v3.333c0 .92-.796 1.667-1.778 1.667H5.778C4.796 20 4 19.254 4 18.333V15M16 11l-4 4-4-4M12 4v9"}))))};n.p;function Wt(){return Wt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wt.apply(this,arguments)}var Yt,Kt=function(e){return a.createElement("svg",Wt({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#236de7",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}},Vt||(Vt=a.createElement("path",{d:"M5.5 12h14M12.5 4.5l7 7.5-7 7.5"}))))};n.p;function qt(){return qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},qt.apply(this,arguments)}var Jt,Xt,Qt=function(e){return a.createElement("svg",qt({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#236de7",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}},Yt||(Yt=a.createElement("path",{d:"M20 15v3.333c0 .92-.796 1.667-1.778 1.667H5.778C4.796 20 4 19.254 4 18.333V15M16 8l-4-4-4 4M12 4v11"}))))};n.p;function $t(){return $t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$t.apply(this,arguments)}var en,tn=function(e){return a.createElement("svg",$t({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),a.createElement("g",{style:{stroke:"#236de7",strokeWidth:2,fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},transform:"translate(4 4)"},Jt||(Jt=a.createElement("circle",{cx:7,cy:7,r:7})),Xt||(Xt=a.createElement("path",{d:"m16 16-4-4"}))))};n.p;function nn(){return nn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},nn.apply(this,arguments)}var rn=function(e){return a.createElement("svg",nn({height:24,viewBox:"0 0 24 24",width:24,xmlns:"http://www.w3.org/2000/svg"},e),en||(en=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("path",{d:"M22 12.5H2M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",stroke:"#236de7",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2}),a.createElement("g",{fill:"#236de7",transform:"translate(2 4)"},a.createElement("circle",{cx:4,cy:12,r:1}),a.createElement("circle",{cx:7,cy:12,r:1})))))},an=(n.p,a.forwardRef((function(e,t){var n=e.panelHandler,r=e.config,i=e.description,s=e.disabled,o=e.connecting,l=e.intent,c=e.toolTipOpen,u=e.setToolTipOpen,p=" action-".concat(r.intent),d=l===r.intent?"".concat(p," active"):p,m=s?"".concat(d," disabled"):d,f=function(e){u(e)};return a.createElement("div",{className:"action-btn-wrap"},a.createElement("button",{className:"action-icon btn-no-outline".concat(m),onClick:function(){if(s||o)return!1;n(r)},"data-tip":!0,"data-for":"action-tip-".concat(r.intent),onMouseEnter:function(){return f(r.intent)},onFocus:function(){return f(r.intent)}},e.children),a.createElement(ft.Z,{effect:"solid",place:"bottom",type:"light",delayUpdate:500,delayHide:250,border:!0,className:"action-tooltip",id:"action-tip-".concat(r.intent),getContent:function(){return r.intent!==c?null:(0,E.ZP)(i)}}))}))),sn=function(e){return(0,i.v9)((function(e){return e.panels.panelsOpen})).includes(e.panelName)?null:a.createElement("div",{className:"panel-summary"},e.panelTitle)},on=function(e){var t=!1,n=["actions-panel"];(0,i.v9)((function(e){return e.panels.panelsOpen})).includes(e.panelName)||(t=!0,n.push("has-divider"));var r=function(){var t=(0,_.ME)(),n=(0,a.useRef)(null),r=e.connecting,i=(0,a.useState)(""),s=(0,k.Z)(i,2),l=s[0],c=s[1],u=(0,a.useState)([]),p=(0,k.Z)(u,2),d=p[0],m=p[1],f=(0,x.Z)({},e);return f.toolTipContent=l,f.setToolTipContent=c,a.createElement("div",{className:"".concat(r?"connecting ":"","action-buttons btn-section")},a.createElement("div",{className:"action-row",ref:n},a.createElement("div",{className:"action-button-group"},a.createElement("h4",null,"Replace This Site with Another\xa0Site"),a.createElement("div",{className:"buttons"},a.createElement(an,(0,o.Z)({config:{panel:"connect",title:K.B.pull,intent:"pull"},description:t?(0,_.mF)("pull",(0,_.Yu)()):(0,w.__)("Replace <b>this site's</b> database and files with a <b>remote site's</b> database&nbsp;and&nbsp;files","wp-migrate-db"),disabled:t,wrapperRef:n,toolTipOpen:d,setToolTipOpen:m},f),a.createElement("p",{className:"action-title text-center"},Bt.pull),t?a.createElement(Ft,null):a.createElement(zt,null)),a.createElement(an,(0,o.Z)({config:{panel:"import",title:K.B.import,intent:"import"},description:t?(0,_.mF)("import",(0,_.Yu)()):(0,w.__)("Replace <b>this site's</b> database with the contents of an&nbsp;SQL&nbsp;file","wp-migrate-db"),disabled:t,wrapperRef:n,toolTipOpen:d,setToolTipOpen:m},f),a.createElement("p",{className:"action-title text-center"},Bt.import),t?a.createElement(Ft,null):a.createElement(Ht,null)))),a.createElement("div",{className:"action-button-group"},a.createElement("h4",null,"Replace Another Site with This\xa0Site"),a.createElement("div",{className:"buttons"},a.createElement(an,(0,o.Z)({config:{panel:"connect",title:K.B.push,intent:"push"},description:t?(0,_.mF)("push",(0,_.Yu)()):(0,w.__)("Replace a <b>remote site's</b> database and files with <b>this site's</b> database and files","wp-migrate-db"),disabled:t,wrapperRef:n,toolTipOpen:d,setToolTipOpen:m},f),a.createElement("p",{className:"action-title text-center"},Bt.push),t?a.createElement(Ft,null):a.createElement(Kt,null)),a.createElement(an,(0,o.Z)({config:{panel:"database",title:K.B.savefile,intent:"savefile"},description:(0,E.ZP)((0,w.__)("Export this site's database and files","wp-migrate-db")),disabled:!1,wrapperRef:n,toolTipOpen:d,setToolTipOpen:m},f),a.createElement("p",{className:"action-title text-center"},Bt.savefile),a.createElement(Qt,null)))),a.createElement("div",{className:"action-button-group"},a.createElement("h4",null,"Tools for This Site"),a.createElement("div",{className:"buttons"},a.createElement(an,(0,o.Z)({config:{panel:"database",title:K.B.find_replace,intent:"find_replace"},description:(0,E.ZP)((0,w.__)("Replace text in this site's database","wp-migrate-db")),disabled:!1,wrapperRef:n,toolTipOpen:d,setToolTipOpen:m},f),a.createElement("p",{className:"action-title text-center"},Bt.find_replace),a.createElement(tn,null)),a.createElement(an,(0,o.Z)({config:{panel:"database",title:K.B.backup_local,intent:"backup_local"},description:(0,E.ZP)((0,w.__)("Save your database to an SQL file on your&nbsp;server","wp-migrate-db")),disabled:!1,wrapperRef:n,toolTipOpen:d,setToolTipOpen:m},f),a.createElement("p",{className:"action-title text-center"},Bt.backup_local),a.createElement(rn,null))))))};return a.createElement(It.Z,{title:e.title,panelName:e.panelName,panelKey:1,hasSummary:!0,forceDivider:t,panelSummary:a.createElement(sn,(0,o.Z)({panelTitle:e.panelSummary,panelName:e.panelName},e)),className:n.join(" ")},a.createElement(r,null))},ln=n(50166),cn=n(70659),un=n.n(cn),pn=function(e){var t=e.selected_existing_profile,n=e.saved,r=(0,a.useRef)(null),i=e.match.url;null===t&&n.length>0&&(t=e.profiles.saved[0].id),(0,a.useEffect)((function(){r.current&&r.current.scrollIntoView()}),[r]);var s=function(e){var t=e.checked,n=e.item,i=t?r:null;return a.createElement("input",{type:"radio",name:"saved-profile",checked:t,onChange:function(t){e.setSelectedExistingProfile(n.id)},ref:i})};if(n.length>0){var l=n.map((function(n,r){var l=!1;return i.includes("unsaved")||parseInt(t)===n.id&&(l=!0),a.createElement("li",{key:r},a.createElement("label",{className:"recent-list"},a.createElement(s,(0,o.Z)({checked:l,item:n},e))," ",a.createElement("b",null,n.id),a.createElement("span",null,n.name)))}));return a.createElement("div",{className:"scroll-div"},a.createElement("ul",null,l))}return a.createElement("div",{className:"scroll-div empty"},"No saved profiles")},dn=function(e){var t=e.current_migration.profile_name;return a.createElement("div",{className:"flex-group"},a.createElement("div",{className:"save-profile-input"},a.createElement("label",{htmlFor:"save-new-profile"},"Profile Name"),a.createElement("br",null),a.createElement("input",{id:"save-new-profile",type:"text",value:t,onChange:function(t){return function(e,t){t.setProfileName(e.target.value)}(t,e)},className:"consolas"})))},mn=(n(28637),n(52650)),fn=function(e){var t=e.tabOpen,n=e.saved;return"save_new"===t?a.createElement(dn,e):a.createElement("div",{className:"flex-group"},a.createElement(pn,(0,o.Z)({saved:n},e)))};var _n,gn,bn,hn,vn,En,wn,yn=(0,i.$j)((function(e){var t=(0,ae.d)("ui",e),n=(0,J.r5)("selected_existing_profile",e);return{ui_data:t,btn_text:t.btn_text,selected_existing_profile:n,panels:e.panels,migration:e.migrations,profiles:e.profiles,current_migration:e.migrations.current_migration}}),{addProfile:B.qU,setProfileName:Y.fJ,setSelectedExistingProfile:B.zD,overwriteProfile:B.Vu,renderButtonStatus:B.F9})((function(e){var t=e.profiles.saved,n=e.profiles.status,r=n.profile_save_error,s=n.saving,l=(0,a.useState)("save_new"),c=(0,k.Z)(l,2),u=c[0],p=c[1],d=e.saveProfileOpen,m=e.setSaveProfileOpen,f=(0,a.useState)(0),_=(0,k.Z)(f,2),h=_[0],v=_[1],E=(0,i.I0)(),y=(0,a.useRef)(null),Z=(0,a.useRef)(null),T=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(){var n,r;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(0!==(n=e.current_migration.profile_name).length){t.next=3;break}return t.abrupt("return");case 3:return t.next=5,e.addProfile({name:n});case 5:if((r=t.sent).success){t.next=8;break}return t.abrupt("return",!1);case 8:e.history.push("/migrate/".concat(r.data.id));case 9:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),N=e.selected_existing_profile;null===N&&t.length>0&&(N=e.profiles.saved[0].id);var P=function(){return!s&&("save_new"===u?T():function(){var n=un()(t,{id:parseInt(N)});e.overwriteProfile(n.guid,n.name),parseInt(e.match.params.id)!==parseInt(n.id)&&e.history.push("/migrate/".concat(n.id))}())},O=function(e){Z.current.contains(e.target)||R()};(0,a.useEffect)((function(){return d?document.addEventListener("mousedown",O):document.removeEventListener("mousedown",O),function(){document.removeEventListener("mousedown",O)}}),[d]),(0,a.useEffect)((function(){if(d)return function(){};var t="save_new";null===e.profiles.current_profile||e.match.url.includes("unsaved")||(t="overwrite"),p(t)}),[e.profiles.current_profile]);var A=function(e){var t=e.x,n=document.body.clientWidth,r=160;return n<960&&(r=36),n<=782&&(r=10),{leftPos:t,sidebarWidth:r}}(h),C=A.leftPos-A.sidebarWidth-32,R=function(){m(!1)},I=(0,ln.Yz)(d,null,{from:{opacity:0,top:-25},enter:{opacity:1,top:0},leave:{opacity:0,top:-25},onRest:function(){null===e.profiles.current_profile||e.match.url.includes("unsaved")||p("overwrite")}});return a.createElement("span",{ref:Z},a.createElement("button",{style:{marginLeft:15},ref:y,onClick:function(){E((0,mn.b)())&&(v(y.current.getBoundingClientRect()),m(!d))},className:"btn btn-stroke".concat(d?" active":"")},"Save Profile"),I.map((function(n){var i=n.item,s=n.key,l=n.props;return i&&a.createElement(S(),{active:d},a.createElement(ln.q.div,{key:s,style:(0,x.Z)({},l),className:"wpmdb-save-profile save-profile-wrap"},a.createElement("div",{className:"save-profile-box",style:{left:C}},a.createElement("div",{className:"header-row"},a.createElement("button",{className:"save_new"===u?"active":"",onClick:function(){p("save_new")}},(0,w.__)("Create new","wp-migrate-db")),a.createElement("button",{className:"overwrite"===u?"active":"",onClick:function(){p("overwrite")}},(0,w.__)("Overwrite existing","wp-migrate-db"))),a.createElement("div",{className:"row wpmdb-form"},a.createElement("div",{className:"save-inner"},a.createElement(fn,(0,o.Z)({},e,{saved:t,tabOpen:u}))),(null!==N&&"overwrite"===u||"save_new"===u)&&a.createElement("button",{className:"btn btn-sm save-btn",onClick:function(){return P()}},e.renderButtonStatus(e.ui_data,R)),r&&"migration"===r.location&&a.createElement("div",{className:"profile-save-error"},a.createElement(z.Z,{type:"danger"},(0,w.__)("Problem saving profile:","wp-migrate-db"),a.createElement("br",null),a.createElement("code",null,r.message))),a.createElement("div",{className:"close-wrap"},a.createElement("button",{onClick:function(){m(!1)},className:"close-picker"},a.createElement(je.r,{"aria-hidden":!0}),a.createElement("span",{className:"screen-reader-text"},(0,w.__)("Close Save Profile Dialog","wp-migrate-db"))))))))})))})),xn=n(76178),kn=n(41459),Zn=(0,i.$j)((function(e){return{panelsOpen:(0,xn.O)("panelsOpen",e)}}))((function(e){var t=e.outdatedSettings,n=e.panelsOpen,r=e.dispatch;(0,a.useEffect)((function(){t.forEach((function(e){n.includes(e)||r({type:kn.kW,payload:e})}))}));var i={media_files:(0,w.__)("Media Files","wp-migrate-db"),post_types:(0,w.__)("Post Types","wp-migrate-db")},s=t.map((function(e){return"<li>".concat(i[e],"</li>")})).join("");return a.createElement(z.Z,{type:"danger"},a.createElement("strong",null,(0,w.__)("Profile Settings Have Changed","wp-migrate-db"))," ","\u2014",(0,w.__)("The profile you are using was imported from an older version of WP Migrate. Please review and update the following settings:","wp-migrate-db"),a.createElement("br",null),a.createElement("br",null),a.createElement("ul",null,(0,E.ZP)(s)))})),Tn=n(39794),Sn=(v.ZP.button(_n||(_n=(0,h.Z)(["\n  border-radius: 3px;\n"]))),v.ZP.div(gn||(gn=(0,h.Z)(["\n  background: #ffffff;\n  border: 1px solid #d6d6d6;\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05), 0 2px 1px 0 rgba(0, 0, 0, 0.05);\n  border-radius: 6px;\n"])))),Nn=((0,v.ZP)(Sn)(bn||(bn=(0,h.Z)(["\n  padding: 0.75rem;\n  margin-bottom: 0.8rem;\n"]))),v.ZP.span(hn||(hn=(0,h.Z)(["\n  display: block;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n  max-width: 100%;\n  font-size: 0.8rem;\n  color: ",";\n"])),Tn.Qp));v.ZP.p(vn||(vn=(0,h.Z)(["\n  color: ",";\n  margin: 1rem 0;\n  line-height: 1.5 !important;\n"])),Tn.qP),v.ZP.div(En||(En=(0,h.Z)(["\n  color: ",";\n  a{\n    color:","\n    text-decoration: underline;\n    \n    &:hover{\n      text-decoration: none;\n    }\n  }\n"])),Tn.qP,Tn.qP);function Pn(){return Pn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pn.apply(this,arguments)}var On,An,Cn=function(e){return a.createElement("svg",Pn({height:16,viewBox:"0 0 16 16",width:16,xmlns:"http://www.w3.org/2000/svg"},e),wn||(wn=a.createElement("path",{d:"m8.393 11.34 4.945-4.9a.548.548 0 0 0 0-.779.558.558 0 0 0-.785 0L8 10.173 3.448 5.662a.559.559 0 0 0-.786 0 .548.548 0 0 0 0 .779l4.945 4.9a.564.564 0 0 0 .786 0z"})))},Rn=(n.p,(0,v.ZP)(Sn)(On||(On=(0,h.Z)(["\n  padding: 0.9rem 1.25rem;\n  margin-bottom: 1.1rem;\n  line-height: 1.2rem;\n  a.link {\n    font-size: inherit;\n  }\n  .breadcrumbs {\n    margin-top: -2px;\n  }\n"])))),In=function(e){var t=e.recentProfiles,n=(0,i.v9)((function(e){return t?e.profiles.recent:e.profiles.saved})),r=(0,i.v9)((function(e){return e.profiles.current_profile}));return a.createElement(Rn,null,a.createElement("div",{className:"breadcrumbs"},a.createElement(l.OL,{className:"link",to:"/"},t?(0,w.__)("Unsaved Profiles","wp-migrate-db"):(0,w.__)("Saved Profiles","wp-migrate-db"))," ",a.createElement(Cn,{className:"profile-header-arrow"})," ",a.createElement("b",null,function(e,t){var n=un()(e,{id:parseInt(t)});return n?a.createElement(a.Fragment,null,(0,O.decode)(n.name)):null}(n,r))))},Dn=n(74094),Mn=n(9412),Fn=n(51286),Ln=(n(9253),{selected:null}),Bn=a.createContext(Ln),Un=function(e,t){return"SET_SELECTED"===t.type?(0,x.Z)((0,x.Z)({},e),{},{selected:t.payload}):(0,x.Z)({},e)},jn=function(e){var t=e.label,n=e.name,r=e.description,i=e.onClick,s=(0,a.useContext)(Bn).state.selected===n;return a.createElement("button",{className:"combo-button-child-container",onClick:i},a.createElement("div",{className:"combo-button-child ".concat(s?"selected":"")},a.createElement("div",{className:"text"},a.createElement("span",{className:"label"},t),a.createElement("span",{className:"description"},r))))},zn=function(e){var t=e.className,n=e.children,r=e.selected,i=e.setSelected,s=e.expandAriaLabel,o=(0,a.useState)(""),l=(0,k.Z)(o,2),c=l[0],u=l[1],p=(0,a.useState)(null),d=(0,k.Z)(p,2),m=d[0],f=d[1],_=(0,a.useState)([]),g=(0,k.Z)(_,2),b=g[0],h=g[1],v=(0,a.useState)(!1),E=(0,k.Z)(v,2),w=E[0],y=E[1],x=(0,a.useReducer)(Un,Ln),Z=(0,k.Z)(x,2),T=Z[0],N=Z[1],P=function(e){return{type:"SET_SELECTED",payload:e}},O=(0,a.useRef)(null),A=(0,a.useRef)(null),C=function(e){O.current.contains(e.target)||setTimeout((function(){y(!1)}),50)};return(0,a.useEffect)((function(){return w?document.addEventListener("mousedown",C):document.removeEventListener("mousedown",C),function(){document.removeEventListener("mousedown",C)}}),[w]),(0,a.useEffect)((function(){h(a.Children.map(n,(function(e,t){var n=e.props.onClick,s=e.props.name,o=e.props.label;return r||0!==t||(u(o),f((function(){return n})),N(P(s)),i(s)),r&&s===r&&(u(o),f((function(){return n})),N(P(s)),i(s)),a.cloneElement(e,{onClick:function(){u(o),f((function(){return n})),N(P(s)),i(s),y(!1),A.current.blur(),setTimeout((function(){n(),A.current.focus()}),0)}})})))}),[n]),a.createElement(Bn.Provider,{value:{state:T,dispatch:N}},a.createElement(S(),{active:w},a.createElement("div",{ref:O,className:"combo-button-container ".concat(t||"")},a.createElement("div",{className:"btn-large"},a.createElement("button",{ref:A,className:"primary-btn",onClick:m},c),a.createElement("button",{"aria-label":s,className:"secondary-btn",onMouseDown:function(e){y(!w)},onKeyDown:function(e){"Enter"!==e.key&&" "!==e.key||y(!w)}},a.createElement(Fn.r,{"aria-hidden":"true",className:w?"expanded":""}))),a.createElement("div",{className:"combo-button-children ".concat(w?"show":"")},b))))},Gn=n(81294),Vn=function(e){var t=e.local_site,n=e.remote_site,r=e.current_migration,s=e.intent,o=e.selectedOption,l=e.onClick,c=(0,i.I0)(),u=(0,a.useState)(o),p=(0,k.Z)(u,2),d=p[0],m=p[1],f=(0,Gn.O)(t,n,r,s);return a.createElement(zn,{className:"run-migration",selected:null!==d&&void 0!==d?d:"preview",setSelected:m,expandAriaLabel:(0,w.__)("Expand Find & Replace options","wp-migrate-db")},a.createElement(jn,{label:(0,w.__)("Find & Replace","wp-migrate-db"),description:(0,w.__)("Run the full process with no prompt","wp-migrate-db"),name:"run",onClick:function(e){c((0,at.G$)(!1)),l(e)}}),a.createElement(jn,{name:"preview",label:(0,w.__)("Preview Changes","wp-migrate-db"),description:(0,w.__)("Show changes with prompt to save or cancel","wp-migrate-db"),onClick:function(){(0,Mn.isArray)(f)&&f.forEach((function(e){e.includes("_mig_")||c((0,at._s)({table:e,data:[],time:0}))})),c((0,at.g1)())}}))},Hn=(0,i.$j)((function(e){return{remote_site:(0,J.NR)("remote_site",e),local_site:(0,J.NR)("local_site",e),current_migration:e.migrations.current_migration}}))((function(e){var t=e.onClick,n=e.intent,r=e.selectedOption,i=e.local_site,s=e.remote_site,o=e.current_migration;return"find_replace"===n?a.createElement(Vn,{current_migration:o,onClick:t,selectedOption:r,remote_site:s,local_site:i,intent:n}):a.createElement("button",{onClick:t,className:"btn run-migration ".concat(n)},Lt[n])})),Wn=function(e){var t=(0,i.I0)(),n=t((0,Dn.J)()),r=(0,i.v9)((function(e){return e.migrations.current_migration})).status,s=e.match,l=(0,i.v9)((function(e){return e.panels})),c=l.panelsToDisplay,u=l.panelTitles,p=l.panelsOpen,d=(0,i.v9)((function(e){return(0,J._P)("this_uploads_dir",e)})),m=(0,i.v9)((function(e){return e.profiles})),f=(0,i.v9)((function(e){return(0,ae.d)("status",e)})).profile_loading,h=(0,i.v9)((function(e){return e.migrations.current_migration})),v=h.intent,E=h.migration_enabled,y=h.running,x=h.selectedComboOption,Z=(0,a.useState)(!1),T=(0,k.Z)(Z,2),S=T[0],N=T[1],P=(0,_.fX)(m.loaded_profile),O=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(n){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!y){e.next=3;break}return n.preventDefault(),e.abrupt("return",!1);case 3:if(t({type:"WPMDB_PRE_MIGRATION"}),t((0,mn.b)())){e.next=7;break}return e.abrupt("return",!1);case 7:t({type:"MIGRATION_STARTED"}),t((0,me.Cy)());case 9:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),A=s.params.hasOwnProperty("id"),C=!1;return(null===f&&!1===A||!1===f&&A)&&(C=!0),a.createElement(a.Fragment,null,null!==m.current_profile&&a.createElement(a.Fragment,null,P&&0!==P.length&&a.createElement(Zn,{outdatedSettings:P}),a.createElement(In,{profiles:e.match.path.includes("unsaved")?a.createElement("profiles",{className:"recent"}):m.saved,recentProfiles:e.match.path.includes("unsaved"),profileID:e.current_profile})),C&&a.createElement(on,(0,o.Z)({connecting:n,title:u.action_buttons,panelName:"action_buttons",panelsToShow:c,panelHandler:function(n){if(n.intent===v)return!1;"/migrate"!==e.match.path&&e.history.push({pathname:"/migrate"}),t((0,W.$f)(n))},baseTitle:(0,w.__)("Action","wp-migrate-db"),updateOpenPanels:e.updateOpenPanels,panelState:p,panelSummary:Bt[v]},e)),""===v&&C&&a.createElement(tt,{location:"migrate-tab"}),e.children,"backup_local"===v&&a.createElement("div",{className:"migration-message"},(0,w.__)("An SQL file will be saved to ".concat(d," on your server"),"wp-migrate-db")),v&&a.createElement(a.Fragment,null,(0,mn.a)(r,"EMPTY_MIGRATION_STAGES")&&a.createElement(z.Z,{type:"danger"},(0,w.__)("Select at least one type of content to be migrated.","wp-migrate-db")),E&&!n&&a.createElement("div",{className:"migration-buttons"},a.createElement(Hn,{onClick:O,intent:v,selectedOption:x})," ",a.createElement(yn,(0,o.Z)({title:"Save Profile",intent:v,saveProfileOpen:S,setSaveProfileOpen:N,panelName:"save_profile",baseTitle:(0,w.__)("Save Profile","wp-migrate-db")},e)))))},Yn=["ChildPanels"],Kn=(0,v.ZP)(ct.Q)(An||(An=(0,h.Z)(["\n  position: relative;\n  top: 5px;\n  #el_6X7lquFKkl {\n    fill: #b3aeae;\n  }\n"]))),qn=function(e){var t=e.ChildPanels,n=(0,Z.Z)(e,Yn),r=(0,i.I0)(),s=n.match,o=n.current_migration,l=n.profileData,c=l.profile_loading,u=l.profile_load_error,p=o.running;return(0,a.useEffect)((function(){s.params.id&&r(te(s))}),[]),(0,a.useEffect)((function(){document.body.style.overflow=p?"hidden":"auto"}),[p]),c?a.createElement("div",{className:"padded-container"},u?a.createElement("h2",null,u):a.createElement("h2",null,(0,w.__)("Loading profile... ","wp-migrate-db"),a.createElement("span",null,a.createElement(Kn,null)))):a.createElement(a.Fragment,null,a.createElement(Wn,n,t),p&&a.createElement(Rt,{running:p}))},Jn=(0,i.$j)((function(e){return{current_migration:e.migrations.current_migration,profileData:e.profiles}}))(a.memo(qn)),Xn=n(19063),Qn=(0,c.EN)((function(e){var t=e.location,n=e.isPro,r=(0,i.I0)(),s=function(e){return!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?t.pathname===e?"nav-item-active":"":-1!==t.pathname.indexOf("unsaved")&&"/migrate"===e||-1!==t.pathname.indexOf(e)?"nav-item-active":""};return a.createElement("ul",{className:"nav bg-white flex-container"},a.createElement("li",{className:s("/")},a.createElement(l.OL,{to:"/",replace:!0,exact:!0,strict:!0,onClick:function(){r((0,Y.oA)())}},(0,w.__)("Profiles","wp-migrate-db"))),a.createElement("li",{className:s("/migrate",!1)},a.createElement(l.OL,{to:"/migrate",replace:!0,onClick:function(){r((0,Y.oA)())}},(0,w.__)("Migrate","wp-migrate-db"))),n&&a.createElement("li",{className:s("/backups")},a.createElement(l.OL,{to:"/backups",replace:!0,exact:!0},(0,w.__)("Backups","wp-migrate-db"))),a.createElement("li",{className:s("/settings",!1)},a.createElement(l.OL,{to:"/settings",replace:!0},(0,w.__)("Settings","wp-migrate-db"))),a.createElement("li",{className:s("/addons")},a.createElement(l.OL,{to:"/addons",replace:!0,exact:!0},(0,w.__)("Upgrades","wp-migrate-db"))),a.createElement("li",{className:s("/help")},a.createElement(l.OL,{to:"/help",replace:!0,exact:!0},(0,w.__)("Help","wp-migrate-db"))),a.createElement("li",{className:s("/whats-new")},a.createElement(l.OL,{to:"/whats-new",replace:!0,exact:!0},(0,w.__)("What's New","wp-migrate-db"))))})),$n=function(e){return 0<=navigator.userAgent.indexOf("MSIE")||0<=navigator.userAgent.indexOf("Trident")?a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.__)("<strong>Internet Explorer Not Supported</strong> &mdash; Less than 2% of our customers use IE, so we've decided not to spend time supporting it. We ask that you use Firefox or a Webkit-based browser like Chrome or Safari instead. If this is a problem for you, please let us know.","wp-migrate-db"))):0<=navigator.userAgent.indexOf("Edge")?a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.__)("<strong>Microsoft Edge Not Supported</strong> &mdash; Less than 2% of our customers use Microsoft Edge, so we've decided not to spend time supporting it. We ask that you use Firefox or a Webkit-based browser like Chrome or Safari instead. If this is a problem for you, please let us know.","wp-migrate-db"))):null},er=n(7354),tr=n(68424),nr=n(15925),rr=n(46415),ar=function(e){var t=e.status,n=e.children,r=e.errors,i=(0,a.useState)(),s=(0,k.Z)(i,2),l=s[0],c=s[1],u=(0,a.useCallback)((function(e){null!==e&&c(e.getBoundingClientRect())}),[]);return n?a.createElement(a.Fragment,null,a.createElement("span",{ref:u},n),a.createElement("div",{className:"relative"},!0===t&&a.createElement(ct.HW,null)),a.createElement(nr.u,{in:"success"===t},a.createElement(rr.X2,(0,o.Z)({locationInfo:l,classNames:"toggle-success"},e))),a.createElement(nr.u,{in:"errored"===t},a.createElement(rr.Uj,(0,o.Z)({error:r,locationInfo:l,classNames:"toggle-error"},e)))):null},ir=function(e){return e.notifications};function sr(e,t){return(0,J.fX)(ir,"notifications",e,t)}var or,lr=n(87326),cr=(0,a.lazy)((function(){return(0,_.Yu)()?n.e(513).then(n.bind(n,76513)):Promise.resolve(null)})),ur=(0,c.EN)((0,i.$j)((function(e){return{notifications:e.notifications,errors:sr("errors",e),status:sr("status",e),settingsStatus:(0,q.u)("status",e),dbi_api_data:e.dbi_api_data}}),{handleNoticeLink:tr.Rp,toggleSetting:lr.Xn,setAction:G.m})((function(e){var t=[],n=e.status,r=e.errors,i=e.notifications,s=e.dbi_api_data,o={settings:["licence_expired","wpmdb_invalid_license"],addons:["licence_expired","wpmdb_invalid_license"],help:["wpmdb_invalid_license"]},l=e.location.pathname.replace("/","");return Object.entries(i.messages).forEach((function(c){var u=(0,k.Z)(c,2),p=u[0],d=u[1],m=!1;if((0,er.includes)(i.hidden,p)&&(m=!0),Object.entries(o).forEach((function(t){var n=(0,k.Z)(t,2),r=n[0],a=n[1];(0,er.includes)(a,p)&&e.location.pathname.includes(r)&&(m=!0)})),m)return null;if(s&&s.dbi_down_status&&"wpmdb_invalid_license"===p)return null;var f=d.message,h=null;d.link&&(h=function(t,n){var r=[],i=Object.entries(t),s=0;return i.forEach((function(o){var l,c,u=(0,k.Z)(o,2),p=u[0],d=u[1];"dismiss"===p&&(l=(0,w.__)("Dismiss","wp-migrate-db")),"reminder"===p&&(l=(0,w.__)("Remind Me Later","wp-migrate-db"),c=t.reminder_time),r.push(a.createElement(a.Fragment,null,a.createElement("button",{className:"link",onClick:function(){e.handleNoticeLink(d,p,c,n)}},l),0===s&&i.length>1?" | ":"")),s++})),a.createElement(a.Fragment,null,r.map((function(e,t){return a.createElement(a.Fragment,{key:t},e)})))}(d.link,p)),d.custom_link&&"usage_tracking"===d.custom_link&&(h=function(e){var t=e.settingsStatus,n=function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n){return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.toggleSetting("allow_tracking",n,"allow_tracking");case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();return a.createElement("div",{className:"flex-container"},a.createElement("button",{className:"link",onClick:function(){n(!0)}},(0,w.__)("Yes, I'd Like to Help","wp-migrate-db"))," ","\xa0|\xa0",a.createElement("button",{className:"link",onClick:function(){n(!1)}},(0,w.__)("No","wp-migrate-db")),a.createElement("div",{className:"relative"},!0===t.allow_tracking&&a.createElement(ct.HW,null)))}(e)),"wpmdb_invalid_license"===p&&(f=d.message[l]||d.message.default||d.message,h=a.createElement(cr,null));var v=d.error?"danger":"warning";t.push(a.createElement(z.Z,{type:v},(0,E.ZP)(f),a.createElement(ar,{status:n[p],errors:r[p]},(0,_.Yu)()&&a.createElement(a.Suspense,{fallback:a.createElement("div",null,(0,w.__)("Loading...","wp-migrate-db"))},h),!(0,_.Yu)()&&h)))})),t.map((function(e,t){return a.createElement("div",{key:t}," ",e)}))}))),pr=function(e){var t=e.componentStack,n=e.error;return a.createElement(z.Z,{type:"danger"},a.createElement("p",null,a.createElement("strong",null,"Oops! An error occured!")),a.createElement("p",null,"Here\u2019s what we know\u2026"),a.createElement("p",null,a.createElement("strong",null,"Error:")," ",n.toString()),a.createElement("p",null,a.createElement("strong",null,"Stacktrace:")," ",t))},dr=(0,a.lazy)((function(){return(0,_.Yu)()?Promise.resolve(null):n.e(613).then(n.bind(n,3613))})),mr=function(e){var t=e.isPro;return a.createElement(a.Fragment,null,a.createElement("div",{className:"nav-wrap"},a.createElement("div",{className:"wrapper"},a.createElement(Qn,{isPro:t}),a.createElement($n,null))),a.createElement("div",{className:"wrapper"},a.createElement(ur,null)),a.createElement(Xn.ErrorBoundary,{FallbackComponent:pr},a.createElement("div",{className:"wrapper".concat(t?"":" wrapper-free"," ").concat((0,c.TH)().pathname.replace("/",""))},a.createElement(y,null,a.createElement("div",null,e.children)),!(0,_.Yu)()&&a.createElement(a.Suspense,{fallback:a.createElement("div",null,(0,w.__)("Loading...","wp-migrate-db"))},a.createElement(dr,e)))))},fr=v.ZP.div(or||(or=(0,h.Z)(["\n  a,\n  button {\n    &:hover {\n      cursor: pointer;\n    }\n  }\n"]))),_r=(n(94849),n(96480)),gr=n.n(_r);var br,hr=n(78677),vr=n(18832),Er=(0,i.$j)((function(e){var t=(0,q.u)("whitelist_plugins",e);return{settings:e.settings,whitelist_plugins:t}}),{updateSetting:lr.m7,saveWhitelistPlugins:function(){return function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,n){var r,a,i;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r="save-whitelist-plugins",a=(0,q.u)("whitelist_plugins",n()),t((0,lr.c9)(r,!0)),t((0,lr.rT)(r)),e.next=6,(0,_.op)("/whitelist-plugins",{whitelist_plugins:a});case 6:if((i=e.sent).success){e.next=9;break}return e.abrupt("return",t((0,lr.nE)(r,i)));case 9:t((0,lr.c9)(r,"success")),setTimeout((function(){t((0,lr.c9)(r,!1))}),1500);case 11:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()}})((function(e){var t=e.settings,n=t.plugins,r=t.status,i=t.errors,s=e.whitelist_plugins,l=(0,a.useState)(),c=(0,k.Z)(l,2),u=c[0],p=c[1],d=(0,a.useCallback)((function(e){null!==e&&p(e.getBoundingClientRect())}),[]),m=function(e){return Object.entries(e).map((function(e){return(0,k.Z)(e,1)[0]}))},f=function(t){e.updateSetting("whitelist_plugins",t),e.saveWhitelistPlugins()};function g(e){var t=(0,_.XV)(s,e.target.value);f(t)}return a.createElement("div",{className:"compatibility-mode-block"},a.createElement("div",{className:"flex-container"},a.createElement("h3",{className:"title",ref:d},(0,w.__)("Load Select Plugins for Migration Requests","wp-migrate-db")),a.createElement("div",{className:"relative"},!0===r["save-whitelist-plugins"]&&a.createElement(ct.Q,{className:"settings-spinner"}))),a.createElement(nr.u,{in:"success"===r["save-whitelist-plugins"]},a.createElement(rr.X2,(0,o.Z)({locationInfo:u,classNames:"toggle-success"},e))),a.createElement(nr.u,{in:"errored"===r["save-whitelist-plugins"]},a.createElement(rr.Uj,(0,o.Z)({error:i["save-whitelist-plugins"],locationInfo:u,classNames:"toggle-error"},e))),a.createElement("span",null,(0,w.__)("By default, plugins are not loaded for migration requests. This enhances performance and reduces the likelihood of a third-party plugin interfering with migrations.","wp-migrate-db"),a.createElement("br",null),a.createElement("br",null),(0,w.__)("To load certain plugins for migration requests, select the plugins below.","wp-migrate-db"),a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/doc/compatibility-mode/",target:"_blank",rel:"noopener noreferrer"}," "+(0,w.__)("More Details \u2192","wp-migrate-db"))),a.createElement(hr.Z,{id:"compatibility-multiselect",options:m(n),iterator:function(){return Object.entries(n).map((function(e){var t=(0,k.Z)(e,2),n=t[0],r=t[1];return a.createElement("li",{key:n},a.createElement("input",{id:"checkbox-".concat(n),type:"checkbox",value:n,onChange:g,checked:s.includes(n)}),a.createElement("label",{htmlFor:"checkbox-".concat(n),key:gr()()},r[0].name))}))},className:"wpmdb-form compat-plugin-list",visible:!0,updateSelected:f,selectInverse:function(){var e=s,t=m(n);return(0,vr.Z)((function(e){f(e)}),t,e)}}))})),wr=n(20271),yr=n(8214),xr=(0,n(14107).Z)({overrides:{MuiSlider:{root:{color:Tn.HT,height:8},thumb:{height:18,width:18,marginTop:-8,marginLeft:0},active:{},track:{height:4,borderRadius:2},rail:{height:4,borderRadius:2}}}}),kr=function(e){var t=(0,i.v9)((function(e){return e.settings})),n=t.status,r=t.errors,s=e.settingsKey,l=e.formattedSize,c=e.prefix,u=e.sliderOpts,p=e.callback,d=e.title,m=(0,a.useState)(l),f=(0,k.Z)(m,2),_=f[0],g=f[1],b=l,h=(0,a.useState)(),v=(0,k.Z)(h,2),E=v[0],w=v[1],y=(0,a.useCallback)((function(e){null!==e&&w(e.getBoundingClientRect())}),[]),x="slider-".concat(s);return a.createElement(a.Fragment,null,a.createElement("div",{className:"slider-wrap ".concat(e.className?e.className:"")},a.createElement("div",{className:"grid-container slider-header"},a.createElement("h3",{className:"slider-title",id:x},d),a.createElement("div",{ref:y,className:"slider-details"},_," ",c,a.createElement("div",{className:"relative"},a.createElement(nr.u,{in:"success"===n[s]},a.createElement(rr.X2,(0,o.Z)({locationInfo:E,classNames:"toggle-success"},e))),!0===n[s]&&a.createElement(ct.Q,{className:"settings-spinner"}),a.createElement(nr.u,{in:"errored"===n[s]},a.createElement(rr.Uj,(0,o.Z)({error:r[s],locationInfo:E,classNames:"toggle-error"},e)))))),a.createElement(wr.Z,{theme:xr},a.createElement(yr.Z,{step:u.step,min:u.min,value:_,max:u.max,valueLabelDisplay:"off",disabled:!0===n[s],onChange:function(e,t){g(t)},onBlur:function(){_!==b&&p(_)},onMouseUp:function(){_!==b&&p(_)},"aria-labelledby":x}))))},Zr=(0,i.$j)((function(e){return{settings:e.settings}}),{setMaxRequest:lr.Lc})((function(e){var t=e.settings.max_request/1024/1024;return a.createElement("div",{className:"wpmdb-settings"},a.createElement("div",{className:"wpmdb-settings-page free"},a.createElement("div",{className:"settings-row advanced-settings settings-row"},a.createElement("h3",null,(0,w.__)("Compatibility Mode","wp-migrate-db")),a.createElement(Er,null)),a.createElement("div",{className:"settings-row request-settings"},a.createElement("h2",{className:"section-title"},(0,w.__)("Request Settings","wp-migrate-db")),a.createElement("div",null,a.createElement(kr,(0,o.Z)({},e,{settingsKey:"max_request",formattedSize:t,prefix:"MB",sliderOpts:{step:.25,min:.25,max:25},callback:e.setMaxRequest,title:a.createElement("span",{className:"has-tooltip"},(0,w.__)("Maximum Request Size","wp-migrate-db"),a.createElement(ct.Ag,{"data-tip":!0,"data-for":"max-request-tip"}))})))),a.createElement(ft.Z,{effect:"solid",place:"right",type:"light",delayUpdate:500,border:!0,className:"action-tooltip",id:"max-request-tip"},(0,w.__)("We've detected that your server supports requests up to 25 MB, but\n          it's possible that your server has limitations that we could not\n          detect. To be on the safe side, we set the default to 1 MB, but you\n          can try throttling it up to get better performance. If you're getting\n          a 413 error or having trouble with time outs, try throttling this\n          setting down.","wp-migrate-db"))))})),Tr=(n(86393),["addon","name","readMoreLink"]),Sr={multisiteTools:(0,w.__)("Subsite options can be found within migration profiles for Push, Pull, Export, and Find & Replace.","wp-migrate-db"),themeFiles:(0,w.__)("Themes can now be selected within Push and Pull migration profiles.","wp-migrate-db"),pluginFiles:(0,w.__)("Plugins can now be selected within Push and Pull migration profiles.","wp-migrate-db"),otherFiles:(0,w.__)("Other files can now be selected within Push and Pull migration profiles.","wp-migrate-db"),mediaFiles:(0,w.__)("Media uploads can now be selected within Push and Pull migration profiles.","wp-migrate-db"),cli:(0,w.__)(" Additional commands to push, pull, and import are now available when using WP-CLI.","wp-migrate-db")},Nr=(0,w.gB)((0,w.__)('Feature is available, but will not receive updates or support until you <a href="%s" target="_blank" rel="noopener noreferrer">log in to My Account</a> and renew your license.',"wp-migrate-db"),"https://deliciousbrains.com/my-account"),Pr=function(e){var t=e.addon,n=e.name,r=e.readMoreLink,s=(0,Z.Z)(e,Tr),o=null,l=!1,c=(0,i.v9)((function(e){return e.dbi_api_data}));if((0,_.Yu)()){var u=c.licence.licence_status;l="subscription_expired"===u}if((0,_.Yu)()){var p=(0,i.v9)((function(e){return e.dbi_api_data}));o=p.api_data}var d=function(){return(0,_.Yu)()&&o&&o.addons_available_list?o.hasOwnProperty("addons_available_list")&&o.addons_available_list.hasOwnProperty(t)?o.hasOwnProperty("addon_content")?a.createElement(z.Z,{type:l?"warning":"success",className:"installed-activated-text ".concat(l?"license-expired":"")},(0,E.ZP)((0,w.gB)("<b>%s</b> - %s",(0,w.__)(l?(0,w.__)("License Expired","wp-migrate-db"):(0,w.__)("Activated","wp-migrate-db"),"wp-migrate-db"),l?Nr:Sr[n]))):null:a.createElement("div",{className:"addon-upgrade-container"},a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/upgrade/?utm_campaign=WP%2BMigrate%2BDB%2BPro%2BUpgrade&utm_source=MDB%2BFree&utm_medium=insideplugin",target:"_blank",rel:"noopener noreferrer",className:"btn btn-sm addon-btn"},(0,w.__)("Upgrade","wp-migrate-db")),a.createElement("div",{className:"edit-key-link"},a.createElement("span",null,"or "),a.createElement("a",{href:"#settings/enter"},(0,w.__)("Edit License Key","wp-migrate-db")))):null};return a.createElement("div",{className:"addon-container container-shadow"},s.icon,a.createElement("div",{className:"addon-content"},a.createElement("h2",{className:"addon-title"},s.title),a.createElement("p",null,s.desc," ",r&&a.createElement("a",{className:"more-details-link",href:r,target:"_blank",rel:"noopener noreferrer"},(0,E.ZP)((0,w.__)("More&nbsp;Details&nbsp;\u2192","wp-migrate-db"))))),(0,_.Yu)()&&a.createElement("div",{className:"addon-bottom-controls"},a.createElement(d,{readMoreLink:s.readMoreLink})))};function Or(){return Or=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Or.apply(this,arguments)}var Ar,Cr=function(e){return a.createElement("svg",Or({height:64,viewBox:"0 0 64 64",width:64,xmlns:"http://www.w3.org/2000/svg"},e),br||(br=a.createElement("g",{fill:"#236de7",fillRule:"evenodd"},a.createElement("circle",{cx:32,cy:34,r:8}),a.createElement("path",{d:"M7 19h50v30H7zm3 3v24h44V22z",fillRule:"nonzero"}),a.createElement("path",{d:"M27 15h11l2 4H25zM11 15h6v3h-6z"}),a.createElement("circle",{cx:16,cy:28,r:2}))))};n.p;function Rr(){return Rr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Rr.apply(this,arguments)}var Ir,Dr=function(e){return a.createElement("svg",Rr({height:64,viewBox:"0 0 64 64",width:64,xmlns:"http://www.w3.org/2000/svg"},e),Ar||(Ar=a.createElement("g",{fill:"#236de7",fillRule:"evenodd"},a.createElement("path",{d:"M8 15h48v34H8zm3 6v25h42V21z"}),a.createElement("path",{d:"M30 39h18v3H30zM22.208 34.051l-6.363-6.364 2.12-2.121 8.486 8.485-8.485 8.485-2.121-2.121z"}))))};n.p;function Mr(){return Mr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Mr.apply(this,arguments)}var Fr,Lr=function(e){return a.createElement("svg",Mr({height:64,viewBox:"0 0 64 64",width:64,xmlns:"http://www.w3.org/2000/svg"},e),Ir||(Ir=a.createElement("path",{d:"M28.679 17.821a50.65 50.65 0 0 0-3.018-1.348C27.13 12.572 29.253 10 32 10c2.676 0 4.76 2.44 6.224 6.173-.94.396-1.905.837-3.002 1.385-1.122-2.939-2.403-4.265-3.223-4.265-.844 0-2.176 1.406-3.321 4.528zM32 51.707c-.82 0-2.1-1.326-3.223-4.265-1.106.553-2.064.99-3.001 1.385C27.24 52.56 29.324 55 32 55c2.748 0 4.87-2.572 6.34-6.473a50.588 50.588 0 0 1-3.018-1.348C34.177 50.3 32.844 51.707 32 51.707zm-8.988-4.154c-3.661 1.323-9.123 2.61-11.294-.594-1.072-1.584-1.473-4.628 2.71-10.14 1.11-1.461 2.395-2.898 3.699-4.216 2.923-2.913 6.217-5.718 10.508-8.622-2.066-1.118-4.32-2.188-6.521-2.984-2.252-.814-4.235-1.266-5.755-1.266-.97 0-1.69.203-1.927.542-.25.36-.263 1.467.85 3.454a3.622 3.622 0 1 1-2.752 1.818c-2.12-3.675-1.682-5.886-.8-7.154.62-.89 1.926-1.952 4.629-1.952 4.8 0 11.266 3.112 15.43 5.523 4.226-2.555 10.913-5.925 15.852-5.925 2.714 0 4.022 1.09 4.64 2.004 1.073 1.584 1.474 4.628-2.71 10.14-5.887 7.756-17.594 16.134-26.56 19.372zM46.949 26.19c2.874-3.786 2.962-5.777 2.607-6.302-.24-.354-.938-.558-1.915-.558-3.726 0-9.403 2.736-12.67 4.588a75.558 75.558 0 0 1 6.721 4.907 55.033 55.033 0 0 1-2.426 2.27 72.068 72.068 0 0 0-7.446-5.27 71.933 71.933 0 0 0-8.787 6.659 70.624 70.624 0 0 0 7.105 5.386 59.318 59.318 0 0 1-3.154 1.838l-.026-.019a72.861 72.861 0 0 1-6.33-4.935c-5.38 5.397-6.896 9.307-6.184 10.36.24.354.939.558 1.915.558 4.486 0 11.608-3.782 15.82-6.495 5.173-3.33 11.072-8.114 14.77-12.987zm4.537 13.295a3.622 3.622 0 1 0-2.71 1.894c1.055 1.921 1.038 2.996.791 3.35-.235.338-.955.54-1.926.54-3.058 0-7.318-1.771-10.06-3.108a80.287 80.287 0 0 1-3.167 2.098c3.677 1.945 9.084 4.303 13.227 4.303 2.703 0 4.009-1.062 4.629-1.952.88-1.264 1.317-3.468-.784-7.125z",fill:"#236de7"})))};n.p;function Br(){return Br=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Br.apply(this,arguments)}var Ur,jr=function(e){return a.createElement("svg",Br({height:64,viewBox:"0 0 64 64",width:64,xmlns:"http://www.w3.org/2000/svg"},e),Fr||(Fr=a.createElement("g",{fill:"#236de7",fillRule:"evenodd"},a.createElement("path",{d:"M27 24.5c0-.828 2.239-1.5 5-1.5s5 .672 5 1.5v3c0 .828-2.239 1.5-5 1.5s-5-.672-5-1.5v-3zm5 1.5c1.657 0 3-.448 3-1s-1.343-1-3-1-3 .448-3 1 1.343 1 3 1zM27 14.5c0-.828 2.239-1.5 5-1.5s5 .672 5 1.5v3c0 .828-2.239 1.5-5 1.5s-5-.672-5-1.5v-3zm5 1.5c1.657 0 3-.448 3-1s-1.343-1-3-1-3 .448-3 1 1.343 1 3 1zM17 19.5c0-.828 2.239-1.5 5-1.5s5 .672 5 1.5v3c0 .828-2.239 1.5-5 1.5s-5-.672-5-1.5v-3zm5 1.5c1.657 0 3-.448 3-1s-1.343-1-3-1-3 .448-3 1 1.343 1 3 1zM37 19.5c0-.828 2.239-1.5 5-1.5s5 .672 5 1.5v3c0 .828-2.239 1.5-5 1.5s-5-.672-5-1.5v-3zm5 1.5c1.657 0 3-.448 3-1s-1.343-1-3-1-3 .448-3 1 1.343 1 3 1zM33 32l24-12v12L33 44zM33 46l24-12v12L33 58zM31 32 7 20v12l24 12zM31 46 7 34v12l24 12z"}),a.createElement("path",{d:"M54 21.5 32 10 10 21.5 7 20 32 7l25 13z"}))))};n.p;function zr(){return zr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},zr.apply(this,arguments)}var Gr,Vr=function(e){return a.createElement("svg",zr({width:64,height:64,viewBox:"0 0 64 64",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Ur||(Ur=a.createElement("path",{d:"M29.467 52h5.066v-5.467L44 36.133V23.4H20v12.733l9.467 10.4V52Zm-4 4v-7.867L16 37.733V23.4c0-1.111.389-2.056 1.167-2.833.777-.778 1.722-1.167 2.833-1.167h4.8l-2 2V8h4v11.4h10.4V8h4v13.4l-2-2H44c1.111 0 2.056.389 2.833 1.167.778.777 1.167 1.722 1.167 2.833v14.333l-9.467 10.4V56H25.467Z",fill:"#2572E4"})))};n.p;function Hr(){return Hr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Hr.apply(this,arguments)}var Wr=function(e){return a.createElement("svg",Hr({width:64,height:64,viewBox:"0 0 64 64",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),Gr||(Gr=a.createElement("path",{d:"M11.349 49.72c-.856 0-1.628-.343-2.316-1.032C8.344 48 8 47.228 8 46.372V17.35c0-.856.344-1.628 1.033-2.316.688-.689 1.46-1.033 2.316-1.033h15.684l3.348 3.349h18.921c.856 0 1.628.344 2.317 1.032.688.689 1.032 1.46 1.032 2.317h-23.72l-3.35-3.35H11.35v29.024l5.693-22.325H56l-5.972 23.107c-.223.892-.633 1.544-1.228 1.953-.595.41-1.358.614-2.288.614H11.349Zm3.516-3.348h31.926l4.688-18.977H19.554l-4.689 18.977Zm0 0 4.688-18.977-4.688 18.977ZM11.35 20.698v-3.35 3.35Z",fill:"#2572E4"})))},Yr=(n.p,"true"===(0,_.Yu)()?"MDB%2BPaid":"MDB%2BFree"),Kr=function(e){return a.createElement("div",{className:"addons-container"},a.createElement(Pr,(0,o.Z)({icon:a.createElement(Lr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-multisite-tools",name:"multisiteTools",title:(0,w.__)("Multisite Tools","wp-migrate-db-pro"),desc:(0,w.__)("Export, push, and pull subsites from multisite networks.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/multisite-tools-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Yr)},e)),a.createElement(Pr,(0,o.Z)({icon:a.createElement(Cr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-media-files",name:"mediaFiles",title:(0,w.__)("Media Uploads","wp-migrate-db"),desc:(0,w.__)("Push and pull media uploads between two WordPress installs.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/media-files-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Yr)},e)),a.createElement(Pr,(0,o.Z)({icon:a.createElement(jr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-theme-plugin-files",name:"themeFiles",title:(0,w.__)("Themes","wp-migrate-db"),desc:(0,w.__)("Push and pull theme files between two WordPress installs.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/theme-plugin-files-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Yr)},e)),a.createElement(Pr,(0,o.Z)({icon:a.createElement(Vr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-theme-plugin-files",name:"pluginFiles",title:(0,w.__)("Plugins & MU-Plugins","wp-migrate-db"),desc:(0,w.__)("Push and pull plugin files and mu-plugin files between two WordPress installs.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/theme-plugin-files-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Yr)},e)),a.createElement(Pr,(0,o.Z)({icon:a.createElement(Wr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-theme-plugin-files",name:"otherFiles",title:(0,w.__)("Other Files","wp-migrate-db"),desc:(0,w.__)("Push and pull all other files such as languages from the wp-content directory.","wp-migrate-db")},e)),a.createElement(Pr,(0,o.Z)({icon:a.createElement(Dr,{className:"addon-icon"}),addon:"wp-migrate-db-pro-cli",name:"cli",title:(0,w.__)("WP-CLI Integration","wp-migrate-db"),desc:(0,w.__)("Integrates with WP-CLI to run custom migrations and saved profiles from the command line.","wp-migrate-db"),readMoreLink:"https://deliciousbrains.com/wp-migrate-db-pro/doc/cli-addon/?utm_campaign=addons%2Binstall&utm_medium=insideplugin&utm_source=".concat(Yr)},e)))},qr=function(e){return a.createElement("section",{className:"wpmdb-addons"},a.createElement(a.Fragment,null,a.createElement(Kr,null)))},Jr=function(e){return a.createElement("div",{className:"flex-container license-message flex-grow"},a.createElement("div",{className:"license-block"},a.createElement("div",null,a.createElement("p",{className:"license-status text-brand-dark"},(0,w.__)("Unlicensed version","wp-migrate-db"))),a.createElement("div",null,a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/upgrade/",target:"_blank",rel:"noopener noreferrer",className:"btn btn-sm"},(0,w.__)("Upgrade","wp-migrate-db")))))},Xr=(n(72054),n(42430),(0,a.forwardRef)((function(e,t){var n=e.children,r=e.callback,i=e.classNames,s=e.ajaxData,o=s.endpoint,l=s.args,c=(0,a.useState)(),u=(0,k.Z)(c,2),p=u[0],d=u[1],m=(0,a.useState)(),f=(0,k.Z)(m,2),g=f[0],b=f[1];return a.createElement(ar,{status:p,errors:g},a.createElement("button",{className:i,onClick:function(e){d(!0),(0,_.op)(o,l).then((function(e){if(!e.success)throw new Error("AJAX error: ".concat(e.data));r(e.data),d("success"),setTimeout((function(){d()}),800)})).catch((function(e){d(!1),b(e.message),console.error(e),console.error(e.message)}))}},n))}))),Qr=["log","setLog"],$r=function(e){var t=e.log,n=e.setLog,r=((0,Z.Z)(e,Qr),(0,i.I0)().dispatch);return(0,a.useEffect)((function(){var e=!0;return(0,_.op)("/get-log",{},!1,r).then((function(t){if(e){if(!t.success)throw new Error("Unable to retrieve diagnostic log");n(t.data)}})).catch((function(e){console.error(e.message)})),function(){e=!1}}),[]),a.createElement(a.Fragment,null,a.createElement("h2",{id:"diagnostic-log-title"},(0,w.__)("Diagnostic Info & Error Log","wp-migrate-db")),a.createElement("textarea",{"aria-labelledby":"diagnostic-log-title",value:t||"",readOnly:!0}),a.createElement("div",{className:"flex-container"},a.createElement("a",{href:window.wpmdb_data.diagnostic_log_download_url,className:"btn btn-sm btn-stroke"},(0,E.ZP)((0,w.__)('Download <span class="screen-reader-text">error log</span>',"wp-migrate-db"))),a.createElement(Xr,{callback:n,classNames:"btn btn-sm btn-stroke clear-log-btn",ajaxData:{endpoint:"/clear-log",args:{}}},(0,w.__)("Clear Error Log","wp-migrate-db"))))},ea=(0,i.$j)((function(e){var t=(0,J._P)("this_url",e),n=(0,q.u)("license_errors",e);return{settings:e.settings,license_errors:n,url:t}}))((function(e){var t=(0,a.useState)("Loading..."),n=(0,k.Z)(t,2),r=n[0],i=n[1],s=e.dispatch;return(0,a.useEffect)((function(){var e=!0;return(0,_.op)("/get-log",{},!1,s).then((function(t){if(e){if(!t.success)throw new Error("Unable to retrieve diagnostic log");i(t.data)}})).catch((function(e){console.error(e.message)})),function(){e=!1}}),[]),a.createElement(a.Fragment,null,a.createElement("div",{className:"wpmdb-help-wrap help-free"},a.createElement("div",{className:"help-message"},a.createElement("h2",null," ",(0,w.__)("Support","Get help from the community","wp-migrate-db")),a.createElement("p",null,(0,w.__)("As this is a free plugin, we do not provide support.","wp-migrate-db")),a.createElement("p",null,(0,E.ZP)((0,w.gB)((0,w.__)('You may ask the WordPress community for help by posting to the <a href="%s">WordPress.org support forum</a>. Response time can range from a few days to a few weeks and will likely be from a non-developer.',"wp-migrate-db"),"http://wordpress.org/support/plugin/wp-migrate-db"))),a.createElement("p",null,(0,E.ZP)((0,w.gB)((0,w.__)('If you want a <strong>timely response via email from a developer</strong> who works on this plugin, <a href="%s">upgrade to WP Migrate</a> and send us an email.',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/?utm_campaign=WP%2BMigrate%2BDB%2BPro%2BUpgrade&utm_source=MDB%2BFree&utm_medium=insideplugin"))),a.createElement("p",null,(0,E.ZP)((0,w.gB)((0,w.__)('If you\'ve found a bug, please <a href="%s">submit an issue at Github</a>.',"wp-migrate-db"),"https://github.com/bradt/wp-migrate-db/issues")))),a.createElement("div",{className:"diagnostic-log-wrap"},a.createElement("div",{className:"diagnostic-log"},a.createElement($r,{log:r,setLog:i})))))})),ta=function(e){var t=e.version,n=e.title,r=e.content,i=e.postLink,s=e.imgSrc,o=e.imgAlt;return a.createElement("article",{className:"release-post"},a.createElement("h2",null,a.createElement("span",{className:"release-version"},"v",t," "),n),a.createElement("img",{src:s,alt:o}),a.createElement("div",{className:"release-content"},(0,E.ZP)(r)),a.createElement(Je,{link:i,content:(0,w.__)("View Full Release Notes","wp-migrate-db"),utmContent:"view-full-release-notes",utmCampaign:"wp-migrate-whats-new",screenReaderText:(0,w.gB)((0,w.__)("about %s","wp-migrate-db"),n),hasArrow:!0}))},na=(n(63662),n.p+"static/media/wp-migrate-2-6-0.8d26599e.png"),ra=n(90534),aa=n.n(ra),ia=function(){var e=[{version:"2.6.0",title:"Full-Site Exports & Import to Local",imgSrc:(0,_.Nh)(na),imgAlt:"Drop to import WP Migrate export to Local",content:"<p>In addition to exporting the database, WP Migrate can now bundle media, themes, plugins, and other files necessary to fully replicate your site in a new environment. Plus a seamless integration with Local\u2014the #1 local WordPress development tool\u2014means the resulting ZIP archive can be imported into a new local development environment in minutes.</p>",postLink:"https://deliciousbrains.com/wp-migrate-2-6-released/"}],t=function(e){return e.releasePosts.map((function(e){return a.createElement(ta,e)}))},n={backgroundImage:"url(".concat(aa(),")")};return a.createElement("div",{className:"whats-new"},a.createElement("div",{className:"release-posts"},a.createElement(t,{releasePosts:e}),a.createElement("div",{className:"more-releases",style:n},a.createElement("h2",null,"We've got more releases!"),a.createElement(Je,{link:"https://deliciousbrains.com/wp-migrate-db-pro/whats-new/",content:(0,w.__)("Check them out","wp-migrate-db"),utmContent:"more-releases",utmCampaign:"wp-migrate-whats-new",hasArrow:!0}))))},sa=function(e){var t=(0,a.useRef)(e);(0,a.useEffect)((function(){t.current=e}),[e]),(0,a.useEffect)((function(){var e=function(){for(var e,n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];return null===(e=t.current)||void 0===e?void 0:e.call.apply(e,[t].concat(r))};return window.addEventListener("beforeunload",e),function(){return window.removeEventListener("beforeunload",e)}}),[])},oa=function(){var e=(0,i.v9)((function(e){return e.migrations.migration_progress}));sa((function(t){!1===e.allow_page_leave&&(t.preventDefault(),t.returnValue="")}))};function la(e){var t="",n=[],r=e.items,i=e.selected,s=e.defaultTitle,o=e.panelsOpen,l=e.showDefault,c=e.panelName;if(lt()(o,e.type))return a.createElement(Nn,null);var u=", ";e.hasOwnProperty("separator")&&(u=e.separator),i.forEach((function(e){n.push(e)}));var p=r.length,d=i.length,m="(<b>".concat(d," of ").concat(p,"</b>)");return d>0&&!l?(t="".concat(m," ").concat(n.join(u)),e.hideCount&&(t="".concat(n.join(u)))):0===d&&["tables","backups","post_types"].includes(c)&&(t="<em>"+(0,w.__)("Empty selection","wp-migrate-db")+"</em>"),l&&(t=s),a.createElement(Nn,{className:e.panelName},(0,E.ZP)(t))}var ca,ua=n(9712),pa=(0,v.ZP)(ua.r)(ca||(ca=(0,h.Z)(["\n  position: absolute;\n  top: -4px;\n  right: -30px;\n"])));var da,ma,fa,_a,ga=function(e){var t=e.advancedOption,n=e.advancedOptions;return a.createElement("input",{type:"checkbox",className:t,checked:lt()(n,t),onChange:function(){!function(e,t){e.updateAdvancedOptions(t)}(e,t)}})},ba={replace_guids:(0,w.__)("Replace GUIDs","wp-migrate-db"),exclude_spam:(0,w.__)("Exclude spam comments","wp-migrate-db"),exclude_transients:(0,w.__)("Exclude transients","wp-migrate-db"),keep_active_plugins:(0,w.__)("Keep active plugins","wp-migrate-db"),compatibility_older_mysql:(0,w.__)("Compatible with older versions of MySQL (pre-5.5)","wp-migrate-db"),gzip_file:(0,w.__)("Compress file with gzip","wp-migrate-db"),exclude_post_revisions:(0,w.__)("Exclude revisions","wp-migrate-db")},ha=(0,i.$j)((function(e){return{panelsOpen:(0,xn.O)("panelsOpen",e),intent:(0,J.r5)("intent",e),current_migration:e.migrations.current_migration}}),{updateAdvancedOptions:Y.vq})((function(e){var t=e.current_migration.advanced_options_selected,n=[];return Object.keys(ba).forEach((function(e){lt()(t,e)&&n.push(ba[e])})),a.createElement(It.Z,(0,o.Z)({title:e.title,panelName:e.panelName,className:"advanced-options",isOpen:!0,selected:t,panelSummary:a.createElement(la,(0,o.Z)({},e,{items:H.Zz,selected:n,type:e.panelName,panelsOpen:e.panelsOpen,hideCount:!0,separator:" | "}))},e),a.createElement("fieldset",{className:"options-wrap","aria-label":e.title},a.createElement("div",{className:"options-list"},"import"!==e.intent&&a.createElement(a.Fragment,null,"backup_local"!==e.intent&&a.createElement("div",null,a.createElement("label",{style:{position:"relative"}},a.createElement(ga,(0,o.Z)({advancedOption:"replace_guids",advancedOptions:t},e)),ba.replace_guids,a.createElement(ft.Z,{effect:"solid",place:"right",type:"light",delayUpdate:500,delayHide:500,border:!0,className:"action-tooltip",id:"wpmdb-replace-guids-tip"},(0,w.__)("Although the WordPress Codex emphasizes that GUIDs should not be changed, this is limited to sites that are already live. If the site has never been live, we recommend replacing the GUIDs. For example, you may be developing a new site locally at dev.somedomain.com and want to migrate the site live to somedomain.com.","wp-migrate-db")),a.createElement(pa,{"data-tip":!0,"data-for":"wpmdb-replace-guids-tip",className:"question-mark"}))),a.createElement("div",null,a.createElement("label",null,a.createElement(ga,(0,o.Z)({advancedOption:"exclude_spam",advancedOptions:t},e)),ba.exclude_spam)),a.createElement("div",null,a.createElement("label",null,a.createElement(ga,(0,o.Z)({advancedOption:"exclude_transients",advancedOptions:t},e)),(0,E.ZP)((0,w.gB)((0,w.__)('Exclude <a href="%s" target="_blank" rel="noopener noreferrer">transients</a>',"wp-migrate-db"),"https://developer.wordpress.org/apis/handbook/transients/"))," ",a.createElement("span",{className:"dark"},(0,w.__)("(temporary cached data)","wp-migrate-db"))))),lt()(["push","pull","import"],e.intent)&&a.createElement("div",null,a.createElement("label",null,a.createElement(ga,(0,o.Z)({advancedOption:"keep_active_plugins",advancedOptions:t},e)),(0,w.gB)((0,w.__)("Do not %s the 'active_plugins' setting","wp-migrate-db"),e.intent),a.createElement("br",null),a.createElement("small",null,(0,w.__)("(i.e. which plugins are activated/deactivated)","wp-migrate-db")))),"savefile"===e.intent&&a.createElement("div",null,a.createElement("label",null,a.createElement(ga,(0,o.Z)({advancedOption:"compatibility_older_mysql",advancedOptions:t},e)),(0,w.__)("Compatible with older versions of MySQL","wp-migrate-db")," ",a.createElement("span",{className:"dark"},(0,w.__)("(pre-5.5)","wp-migrate-db")))),lt()(["savefile","backup_local"],e.intent)&&a.createElement("div",null,a.createElement("label",null,a.createElement(ga,(0,o.Z)({advancedOption:"gzip_file",advancedOptions:t},e)),ba.gzip_file)),a.createElement("div",null,a.createElement("label",null,a.createElement(ga,(0,o.Z)({advancedOption:"exclude_post_revisions",advancedOptions:t},e)),ba.exclude_post_revisions)))))})),va=n(31125),Ea=(n(75358),v.ZP.section(da||(da=(0,h.Z)(["\n  display: grid;\n"])))),wa=function(e){var t=e.intent,n=e.className,r=e.children;return a.createElement(Ea,{className:n||""},a.createElement("div",{className:"header-row row"},a.createElement("div",{className:"header-2"},(0,w.__)("Find","wp-migrate-db")),a.createElement("div",{className:"header-4"},"savefile"===t&&(0,w.__)("Replace (Optional)","wp-migrate-db"),"savefile"!==t&&(0,w.__)("Replace","wp-migrate-db"))),r)},ya=n(4669),xa=n(69479),ka=v.ZP.span(ma||(ma=(0,h.Z)(["\n  padding: 0 0.5rem;\n  border-left: 1px solid ",";\n\n  &.first {\n    border-left: none;\n    padding-left: 0;\n  }\n"])),Tn.Qp),Za=(0,v.ZP)(xa.r)(fa||(fa=(0,h.Z)(["\n  margin: 0 0.3rem;\n"]))),Ta=v.ZP.span(_a||(_a=(0,h.Z)(["\n  position: relative;\n  top: 4px;\n"])));function Sa(e){var t="",n=e.fields,r=e.panelsOpen,i=e.searchKeys;if(lt()(r,e.type))return a.createElement(Nn,null);var s=[];return n.forEach((function(e){e[i.search].length&&s.push(a.createElement(a.Fragment,null,e[i.search],a.createElement(Ta,null,a.createElement(Za,null)),e[i.replace]))})),s.length>0&&(t=a.createElement(a.Fragment,null,s.map((function(e,t){return a.createElement(ka,{key:t,className:0===t?"first":""},e)})))),a.createElement(Nn,null,t)}var Na=function(e){return e.migrations.search_replace};function Pa(e,t){return(0,J.fX)(Na,"search_replace",e,t)}var Oa=function(e){var t=e.count,n=e.panelsOpen,r=e.title,i=e.panelKey,s=e.panelName;return t>0&&!n.includes(i)?a.createElement("h4",{id:"panel-title-".concat(s),className:"panel-title"},r," ",a.createElement("b",{className:"count"},"(",t,")")):a.createElement("h4",{id:"panel-title-".concat(s),className:"panel-title"},r)},Aa=n(95402),Ca=n(22497),Ra=function(e){var t=e.enabled,n=e.screenReaderText,r=e.onClick;return t?a.createElement("button",{onClick:r},a.createElement(xa.r,{"aria-hidden":"true"}),a.createElement("span",{className:"screen-reader-text"},n)):a.createElement(xa.r,{"aria-hidden":"true"})};var Ia=function(e,t){e.toggleStandardSearchReplace(t)},Da=(0,i.$j)((function(e){var t=(0,xn.O)("panelsOpen",e),n=(0,J.NR)("search_replace",e),r=Pa("standard_search_replace",e),a=Pa("standard_options_enabled",e),i=(0,J.r5)("selected_existing_profile",e),s=e.multisite_tools;return{search_replace:n,standard_search_replace:r,standard_options_enabled:a,panelsOpen:t,migrations:e.migrations,existingProfile:i,mst:s}}),{updateStandardSearchReplace:ya.mX,toggleStandardSearchReplace:ya.rL})((function(e){var t,n,r=e.standard_search_replace,s=e.standard_options_enabled,l=e.panelsOpen,c=e.migrations,u=e.existingProfile,p=e.mst,d=c.current_migration.intent,m=r.domain,f=r.path,_=(0,a.useState)(""),g=(0,k.Z)(_,2),b=g[0],h=g[1],v=(0,a.useState)(null!==(t=m.replace)&&void 0!==t?t:""),y=(0,k.Z)(v,2),x=y[0],Z=y[1],T=(0,a.useState)(null!==(n=f.replace)&&void 0!==n?n:""),S=(0,k.Z)(T,2),N=S[0],P=S[1],O=(0,i.I0)(),A=[];return(0,a.useEffect)((function(){if("savefile"===d&&null===u){var t=(0,va.Z)(e.panelsOpen);t.push("standard_fields"),O((0,W.G7)(t))}}),[]),(0,a.useEffect)((function(){if("savefile"===d){var e=(0,Aa.F)({intent:d,remote_site:{url:x,path:N},local_site:c.local_site});e=O((0,Ca.O)("wpmdb_standard_replace_values",e)),O((0,ya.mX)(e))}}),[x,N,p]),lt()(s,"domain")&&("savefile"===d?m.replace&&A.push(m):A.push(m)),lt()(s,"path")&&("savefile"===d?f.replace&&A.push(f):A.push(f)),a.createElement(It.Z,(0,o.Z)({title:e.title,panelName:e.panelName,isOpen:!0,header:a.createElement(Oa,(0,o.Z)({count:s.length,panelsOpen:l,title:e.title,panelKey:"standard_fields"},e)),selected:s,panelSummary:a.createElement(Sa,(0,o.Z)({},e,{fields:A,enabled:s,type:e.panelName,searchKeys:{search:"search",replace:"replace"},panelsOpen:e.panelsOpen})),bodyClass:"standard-search-replace search-replace"},e),a.createElement(wa,{intent:d},a.createElement("div",{className:"content-row-wrap"},a.createElement("div",{className:"row content-row"},a.createElement("div",{className:"cell"},"savefile"!==d&&a.createElement("input",{type:"checkbox",onChange:function(){Ia(e,"domain")},checked:lt()(s,"domain"),"aria-label":(0,w.__)("Find and replace URLs","wp-migrate-db")})),a.createElement("div",{className:"cell search"},m.search),a.createElement("div",{className:"cell custom-search-arrow"},a.createElement(Ra,{screenReaderText:(0,w.__)("Copy find field to replace field","wp-migrate-db"),enabled:"savefile"===d,onClick:function(){Z(m.search)}})),a.createElement("div",{className:"cell replace"},"savefile"===d&&a.createElement("input",{type:"text","aria-label":(0,w.__)("Replace","wp-migrate-db"),onChange:function(e){Z(e.target.value?e.target.value.trim():"")},defaultValue:x}),"savefile"!==d&&m.replace),a.createElement("div",{className:"question cell"},a.createElement(ft.Z,{effect:"solid",place:"left",type:"light",delayHide:500,delayUpdate:500,border:!0,className:"action-tooltip",id:"wpmdb-search-replace-url-tip",getContent:function(){return"url"!==b?null:(0,E.ZP)((0,w.gB)((0,w.__)('This find & replace updates the domain of all the matching URLs in\n                  your database. You may wonder why we have double slashes in front\n                  of the domain name. If we excluded those, it could replace your\n                  domain in unwanted places like email addresses. You probably don\'t\n                  want <NAME_EMAIL> with\n                  <EMAIL>. More details in our\n                  <a\n                    href="%s"\n                    target="_blank"\n                    rel="noopener noreferrer"\n                  >\n                    Find & Replace Doc \u2192\n                  </a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source=MDB%2BFree&utm_medium=insideplugin&utm_content=new%2Burl%2Bhelp%2Bbubble"))}}),a.createElement(ua.r,{"data-tip":!0,"data-for":"wpmdb-search-replace-url-tip",onMouseOver:function(){return h("url")}})))),a.createElement("div",{className:"content-row-wrap"},a.createElement("div",{className:"row content-row"},a.createElement("div",{className:"cell"},"savefile"!==d&&a.createElement("input",{type:"checkbox",onChange:function(){Ia(e,"path")},checked:lt()(s,"path"),"aria-label":(0,w.__)("Find and replace web root path","wp-migrate-db")})),a.createElement("div",{className:"cell search"},f.search),a.createElement("div",{className:"cell custom-search-arrow"},a.createElement(Ra,{screenReaderText:(0,w.__)("Copy find field to replace field","wp-migrate-db"),enabled:"savefile"===d,onClick:function(){P(f.search)}})),a.createElement("div",{className:"cell replace"},"savefile"===d&&a.createElement("input",{type:"text","aria-label":(0,w.__)("Replace","wp-migrate-db"),onChange:function(e){P(e.target.value?e.target.value.trim():"")},defaultValue:N}),"savefile"!==d&&f.replace),a.createElement("div",{className:"question cell"},a.createElement(ft.Z,{effect:"solid",place:"left",type:"light",delayHide:500,delayUpdate:500,border:!0,className:"action-tooltip",id:"wpmdb-search-replace-path-tip",getContent:function(){return"path"!==b?null:(0,E.ZP)((0,w.gB)((0,w.__)('The web root paths differ between your two sites. We\'ll update any\n                              matching file paths that exist in your database. More details in\n                              our<br /><a href="%s" target="_blank" rel="noopener noreferrer">Find & Replace Doc \u2192</a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source=MDB%2BFree&utm_medium=insideplugin&utm_content=new%2Burl%2Bhelp%2Bbubble"))}}),a.createElement(ua.r,{"data-tip":!0,"data-for":"wpmdb-search-replace-path-tip",onMouseOver:function(){return h("path")}}))))),s.length<2&&"savefile"!==d&&a.createElement(z.Z,{type:"warning"},(0,w.__)("Turning off these standard find & replace operations\n          could break this site. Only disable them if you are confident in\n          what you're doing.","wp-migrate-db")))})),Ma=function(e){var t=e.intent;return a.createElement(z.Z,{type:"warning"},a.createElement("h4",null,(0,w.__)("Mixed Case Table Name","wp-migrate-db")),a.createElement("p",null,(0,E.ZP)((0,w.gB)((0,w.__)("Whoa! We've detected that your <b>%s</b> site has the MySQL setting <code>lower_case_table_names</code> set to <code>1</code>.","wp-migrate-db"),"push"===t?(0,w.__)("remote","wp-migrate-db"):(0,w.__)("local","wp-migrate-db")))),a.createElement("p",null,(0,w.__)("As a result, uppercase characters in table names will be converted to lowercase during the migration.","wp-migrate-db")),a.createElement("p",null,(0,E.ZP)((0,w.gB)((0,w.__)('You can read more about this in <a href="%s" target="_blank" rel="noopener noreferrer">our documentation</a>, proceed with caution.',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/mixed-case-table-names/?utm_campaign=error%2Bmessages&utm_source=MDB%2BPaid&utm_medium=insideplugin"))))},Fa=function(e){var t=[],n=e.status,r=void 0===n?{}:n,i=e.intent;return r.mixed_case_table_name_warning&&t.push(a.createElement(Ma,(0,o.Z)({intent:i},e))),t},La={push:{all:(0,w.__)("Push all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Push only the selected tables below","wp-migrate-db"),backup:(0,w.__)("Backup the remote database before replacing it","wp-migrate-db")},pull:{all:(0,w.__)("Pull all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Pull only the selected tables below","wp-migrate-db"),backup:(0,w.__)("Backup the local database before replacing it","wp-migrate-db")},import:{all:(0,w.__)("Import all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Import only selected tables below","wp-migrate-db"),backup:(0,w.__)("Backup the database before running the import","wp-migrate-db"),backup_selected:(0,w.__)("Backup only the tables that will be replaced during the import","wp-migrate-db")},savefile:{all:(0,w.__)("Export all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Export only selected tables below","wp-migrate-db"),post_types_all:(0,w.__)("Export all post types","wp-migrate-db"),post_types_selected:(0,w.__)("Export only post types selected below","wp-migrate-db")},backup_local:{all:(0,w.__)("Backup all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Backup only selected tables below","wp-migrate-db"),post_types_all:(0,w.__)("Backup all post types","wp-migrate-db"),post_types_selected:(0,w.__)("Backup only post types selected below")},find_replace:{all:(0,w.__)("Search in all tables with prefix","wp-migrate-db"),selected:(0,w.__)("Search only in selected tables below","wp-migrate-db"),backup:(0,w.__)("Backup the database before running the find & replace","wp-migrate-db"),post_types_all:(0,w.__)("Search in all post types","wp-migrate-db"),post_types_selected:(0,w.__)("Search in only post types selected below","wp-migrate-db")}},Ba=function(e){var t=e.intent,n="find_replace"===t?(0,w.__)("The post types you <em>don't</em> select will not be included in the find & replace.","wp-migrate-db"):(0,w.__)("The post types you <em>don't</em> select will be absent in the destination posts table after migration.","wp-migrate-db");return a.createElement("div",{className:"options-list"},a.createElement("div",null,a.createElement("input",{type:"radio",name:"post-types[]",checked:"all"===e.postTypesOption,onChange:function(){return e.togglePostTypesSelector("all")},id:"posttypes-all"}),a.createElement("label",{htmlFor:"posttypes-all"},["push","pull"].includes(t)?(0,w.__)("Migrate all post types","wp-migrate-db"):La[t].post_types_all)),a.createElement("div",null,a.createElement("input",{type:"radio",name:"post-types[]",checked:"selected"===e.postTypesOption,onChange:function(){return e.togglePostTypesSelector("selected")},id:"posttypes-selected"}),a.createElement("label",{htmlFor:"posttypes-selected"},["push","pull"].includes(t)?(0,w.__)("Migrate only selected post types below","wp-migrate-db"):La[t].post_types_selected),"selected"===e.postTypesOption&&a.createElement(z.Z,{type:"warning"},(0,E.ZP)(n))))};var Ua=(0,i.$j)((function(e){var t=(0,xn.O)("panelsOpen",e),n=(0,J.r5)("post_types_option",e),r=(0,J.r5)("post_types_selected",e),a=(0,J.r5)("advanced_options_selected",e);return{panelsOpen:t,post_types_selected:r,post_types_option:n,status:(0,J.r5)("status",e),advanced_options_selected:a}}),{updatePostTypesOption:Y.t9,updatePostTypesSelected:Y.gy})((function(e){var t=e.postTypes,n=e.post_types_option,r=e.post_types_selected,i=e.intent,s=t.filter((function(e){return"revision"!==e})),l=function(t){e.updatePostTypesOption(t,s)};return a.createElement(It.Z,(0,o.Z)({title:e.title,panelName:e.panelName,isOpen:!0,selected:r,hasDefault:!0,panelSummary:a.createElement(la,(0,o.Z)({},e,{items:s,selected:r,title:e.title,type:e.panelName,panelsOpen:e.panelsOpen,defaultTitle:["push","pull"].includes(i)?(0,w.__)("Migrate all post types","wp-migrate-db"):La[i].post_types_all,showDefault:"selected"!==n}))},e),a.createElement(hr.Z,{id:"post-type-multiselect",options:s,stateManager:function(t){e.updatePostTypesSelected(t)},selected:r,handleToggle:l,visible:"selected"===n,updateSelected:e.updatePostTypesSelected,selectInverse:function(){return(0,vr.Z)(e.updatePostTypesSelected,s,r)},ToggleButtons:a.createElement(Ba,{postTypesOption:n,togglePostTypesSelector:l,intent:i})}),(0,mn.a)(e.status,"POST_TYPES_SELECTED_EMPTY")&&a.createElement(z.Z,{type:"danger"},(0,w.__)("Please select post types to exclude or migrate all post types","wp-migrate-db")))})),ja=function(e){return a.createElement("div",{className:"options-list",role:"radiogroup","aria-label":(0,w.__)("Tables migration options","wp-migrate-db")},a.createElement("div",null,a.createElement("input",{type:"radio",name:"tables",checked:"all"===e.tablesOption,onChange:function(){return e.toggleTableSelector("all")},id:"all-tables"}),a.createElement("label",{htmlFor:"all-tables"},La[e.intent].all,' "',e.prefix,'"')),a.createElement("div",null,a.createElement("input",{type:"radio",name:"tables",checked:"selected"===e.tablesOption,onChange:function(){return e.toggleTableSelector("selected")},id:"selected-tables"}),a.createElement("label",{htmlFor:"selected-tables"},La[e.intent].selected)))};var za=(0,i.$j)((function(e){var t=(0,xn.O)("panelsOpen",e),n=(0,J.r5)("tables_option",e),r=(0,J.r5)("tables_selected",e),a=(0,J.r5)("databaseEnabled",e);return{panelsOpen:t,tablesOption:n,tablesSelected:r,status:(0,J.r5)("status",e),databaseEnabled:a}}),{updateSelectedTables:Y.E2,updateTablesOption:Y.S5})((function(e){var t=e.intent,n=e.tables,r=e.prefix,i=e.tablesSelected,s=e.tablesOption,l=e.tableSizes,c=e.databaseEnabled,u=function(t){if(e.disabled)return!1;e.updateTablesOption(t,n)},p=(0,x.Z)({},l);for(var d in l)p[d]=' <span class="table-size">('.concat(l[d],")</span>");var m=function(){return c?(0,mn.a)(e.status,"TABLES_SELECTED_EMPTY")&&a.createElement(z.Z,{type:"danger"},(0,w.__)("Please select at least one table to migrate","wp-migrate-db")):null};return a.createElement(It.Z,(0,o.Z)({title:e.title,selected:i,hasDefault:!0,panelSummary:a.createElement(la,(0,o.Z)({},e,{items:n,selected:i,type:e.panelName,panelsOpen:e.panelsOpen,defaultTitle:"".concat(La[t].all,' "').concat(e.prefix,'"'),showDefault:"selected"!==s})),panelName:e.panelName,isOpen:!0},e),a.createElement(hr.Z,{id:"tables-multiselect",options:n,extraLabels:p,stateManager:function(t){e.updateSelectedTables(t)},selected:i,handleToggle:u,visible:"selected"===s,updateSelected:e.updateSelectedTables,selectInverse:function(){return(0,vr.Z)(e.updateSelectedTables,n,i)},ToggleButtons:a.createElement(ja,{tablesOption:s,toggleTableSelector:u,intent:t,prefix:r})}),a.createElement(m,null))})),Ga=function(e){var t=e.uploads_dir;return a.createElement(z.Z,{type:"danger"},a.createElement("strong",null,(0,w.__)("Migration disabled","wp-migrate-db"))," \u2014",(0,E.ZP)((0,w.gB)((0,w.__)("This migration type has been disabled because the local uploads directory is not writable: <code>%s</code>. Please update the permissions of this directory and try again.","wp-migrate-db"),t))," ",a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/doc/uploads-folder-permissions/",target:"_blank",rel:"noopener noreferrer"},(0,w.__)("More information.","wp-migrate-db")))},Va=n(47895),Ha=function(e){var t=e.prefix,n=(0,i.v9)((function(e){return e.migrations.import_data})).status;return a.createElement("div",{className:"options-list"},a.createElement("div",null,a.createElement("input",{type:"radio",name:"backup[]",checked:"none"===e.backupOption,onChange:function(){return e.toggleBackupsSelector("none")},id:"no-backup"}),a.createElement("label",{htmlFor:"no-backup"},"No backup")),a.createElement("div",null,a.createElement("input",{type:"radio",name:"backup[]",checked:"backup_only_with_prefix"===e.backupOption,onChange:function(){return e.toggleBackupsSelector("backup_only_with_prefix")},id:"prefix-backup"}),a.createElement("label",{htmlFor:"prefix-backup"},(0,w.gB)((0,w.__)('Backup all tables with "%s" prefix',"wp-migrate-db"),t))),"NON_MDB_IMPORT_ERROR"!==n&&a.createElement("div",null,a.createElement("input",{type:"radio",name:"backup[]",checked:"backup_selected"===e.backupOption,onChange:function(){return e.toggleBackupsSelector("backup_selected")},id:"backup-selected"}),a.createElement("label",{htmlFor:"backup-selected"},"import"===e.intent?La.import.backup_selected:(0,w.__)("Backup only tables selected for migration","wp-migrate-db"))),a.createElement("div",null,a.createElement("input",{type:"radio",name:"backup[]",checked:"backup_manual_select"===e.backupOption,onChange:function(){return e.toggleBackupsSelector("backup_manual_select")},id:"manual-backup"}),a.createElement("label",{htmlFor:"manual-backup"},(0,w.__)("Backup only selected tables below","wp-migrate-db"))))},Wa=["tables_selected","backup_option","status","tables","tableSizes","prefix","local_site","remote_site","backup_tables_selected","intent"];var Ya,Ka=function(e){return{none:(0,w.__)("No backup","wp-migrate-db"),backup_only_with_prefix:(0,w.gB)((0,w.__)('Backup all tables with "%s" prefix',"wp-migrate-db"),e.prefix),backup_selected:(0,w.__)("Backup tables selected for migration","wp-migrate-db"),backup_manual_select:(0,w.__)("Backup selected tables","wp-migrate-db")}},qa=(0,i.$j)((function(e){var t=(0,xn.O)("panelsOpen",e),n=(0,J.r5)("backup_tables_selected",e),r=(0,J.r5)(["tables_selected","backup_option","status"],e),a=(0,k.Z)(r,3);return{panelsOpen:t,backup_tables_selected:n,tables_selected:a[0],backup_option:a[1],status:a[2],remote_site:e.migrations.remote_site,local_site:e.migrations.local_site,panels:e.panels}}),{updateBackupsTables:Y.qg,updateBackupsOption:Y.P_,updatePanelTitle:W.H5})((function(e){e.tables_selected;var t=e.backup_option,n=e.status,r=e.tables,i=e.tableSizes,s=e.prefix,l=e.local_site,c=e.remote_site,u=e.backup_tables_selected,p=e.intent,d=(0,Z.Z)(e,Wa),m=(0,x.Z)({},d),f=s;"import"!==p&&"pull"!==p||(f=l.this_prefix),"push"===p&&(f=c.prefix),m.prefix=f;var g=(0,_.qu)(p,l,c),b=g.disabled,h=g.uploads_dir,v=function(e){if(b)return null;d.updateBackupsOption(e,Ka(m))},E=(0,x.Z)({},i);for(var y in i)E[y]='  <span class="table-size">('.concat(i[y],")</span>");return a.createElement(a.Fragment,null,a.createElement(It.Z,(0,o.Z)({title:d.title,panelName:d.panelName,isOpen:!0,disabled:b,selected:u,hasDefault:!0,panelSummary:a.createElement(la,(0,o.Z)({},d,{items:r,selected:u,title:d.title,type:d.panelName,panelsOpen:d.panelsOpen,defaultTitle:Ka(m)[t],showDefault:"backup_manual_select"!==t}))},d),a.createElement(hr.Z,{id:"backups-multiselect",options:r,extraLabels:E,visible:"backup_manual_select"===t,stateManager:function(e){d.updateBackupsTables(e)},selected:u,handleToggle:v,updateSelected:d.updateBackupsTables,selectInverse:function(){return(0,vr.Z)(d.updateBackupsTables,r,u)},ToggleButtons:a.createElement(Ha,{backupOption:t,toggleBackupsSelector:v,prefix:f,intent:p,disabled:b})}),a.createElement(z.Z,{type:"info",className:"backup-dir"},a.createElement("p",null,(0,w.gB)((0,w.__)("A file will be saved to %s","wp-migrate-db"),h))),(0,mn.a)(n,"BACKUP_TABLES_SELECTED_EMPTY")&&a.createElement(z.Z,{type:"danger"},a.createElement("p",null,(0,w.__)("Please select at least one table to backup","wp-migrate-db")))))})),Ja=n(2474),Xa=n.n(Ja),Qa=n(41919),$a=function(e,t,n){var r=Array.from(e),a=r.splice(t,1),i=(0,k.Z)(a,1)[0];return r.splice(n,0,i),r};function ei(){return ei=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ei.apply(this,arguments)}var ti,ni=function(e){return a.createElement("svg",ei({height:16,viewBox:"0 0 16 16",width:16,xmlns:"http://www.w3.org/2000/svg"},e),Ya||(Ya=a.createElement("g",{fill:"#0777ef",fillRule:"evenodd",transform:"translate(3 3)"},a.createElement("rect",{height:10,rx:1,width:2,x:4}),a.createElement("rect",{height:10,rx:1,transform:"rotate(-90 5 5)",width:2,x:4}))))};n.p;function ri(){return ri=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ri.apply(this,arguments)}var ai,ii=function(e){return a.createElement("svg",ri({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24"},e),ti||(ti=a.createElement("g",{fill:"#999",fillRule:"evenodd"},a.createElement("path",{d:"M4 5h16v2H4zM4 9h16v2H4zM4 13h16v2H4zM4 17h16v2H4z"}))))};n.p;function si(){return si=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},si.apply(this,arguments)}var oi,li=function(e){return a.createElement("svg",si({xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24"},e),ai||(ai=a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("circle",{cx:12,cy:12,r:8,fill:"#999"}),a.createElement("path",{fill:"#EEE",fillRule:"nonzero",d:"m13.5 12 2.252-2.251c.33-.33.33-.87 0-1.201l-.3-.3a.851.851 0 0 0-1.2 0L12 10.5 9.748 8.248a.851.851 0 0 0-1.2 0l-.3.3c-.33.33-.33.87 0 1.2L10.497 12l-2.25 2.252c-.33.33-.33.87 0 1.2l.3.3c.33.33.87.33 1.2 0L12 13.502l2.251 2.25c.33.33.871.33 1.201 0l.3-.3c.33-.33.33-.87 0-1.2L13.501 12Z"}))))},ci=(n.p,n(48462)),ui=n.n(ci),pi=function(e){return function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){var a,i,s,o,l;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a=(0,J.NR)("search_replace",r()),i=a.custom_search_replace,!(s=i[e])){t.next=16;break}if(""!==(o=s.replace_old)){t.next=6;break}return t.abrupt("return",!0);case 6:return t.prev=6,t.next=9,(0,_.op)("/regex-validate",{pattern:ui()(o)});case 9:return l=t.sent,t.abrupt("return",l.success&&!0===l.data);case 13:return t.prev=13,t.t0=t.catch(6),t.abrupt("return",!1);case 16:return t.abrupt("return",!1);case 17:case"end":return t.stop()}}),t,null,[[6,13]])})));return function(e,n){return t.apply(this,arguments)}}()},di=function(e,t){return t["regex"===e?"case_sensitive":"regex"]};function mi(){return mi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mi.apply(this,arguments)}var fi,_i,gi=function(e){return a.createElement("svg",mi({width:20,height:12,viewBox:"0 0 20 13",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),oi||(oi=a.createElement("path",{d:"M.942 12.474c.542 0 .845-.259 1.058-.932l.836-2.432h4.39l.836 2.432c.205.665.525.932 1.067.932.578 0 .96-.328.96-.845 0-.216-.054-.449-.151-.734L6.604 1.742C6.302.871 5.867.5 5.058.5c-.81 0-1.245.38-1.556 1.242L.17 10.895c-.116.328-.169.553-.169.76 0 .5.382.82.942.82ZM3.316 7.61l1.67-4.978h.098l1.663 4.978H3.316ZM18.009 11.189h.098v.405c.044.544.39.863.933.863.613 0 .96-.38.96-1.035v-5.22c0-1.984-1.324-3.088-3.698-3.088-.977 0-1.724.164-2.293.466-.791.414-1.165.931-1.165 1.458 0 .414.285.699.72.699.338 0 .57-.087.81-.311.542-.535 1.057-.794 1.804-.794 1.2 0 1.849.535 1.849 1.562v.81H15.68c-2.098.01-3.36 1.036-3.36 2.718 0 1.657 1.236 2.778 3.067 2.778 1.164 0 2.106-.466 2.622-1.311Zm-3.707-1.562c0-.837.605-1.311 1.654-1.311h2.07v.983c0 .984-.88 1.743-2.026 1.743-1.013 0-1.698-.57-1.698-1.415Z",fill:"#30578C"})))};n.p;function bi(){return bi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},bi.apply(this,arguments)}var hi=function(e){return a.createElement("svg",bi({width:16,height:12,viewBox:"0 0 16 13",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),fi||(fi=a.createElement("path",{d:"m15.735 7.569-3.03-1.652 3.03-1.653a.506.506 0 0 0 .196-.699l-.415-.714a.513.513 0 0 0-.708-.18l-2.956 1.78.074-3.432a.51.51 0 0 0-.51-.519h-.831a.51.51 0 0 0-.511.519l.074 3.432-2.956-1.78a.513.513 0 0 0-.708.18l-.415.714a.506.506 0 0 0 .196.7l3.03 1.652-3.03 1.652a.506.506 0 0 0-.196.7l.415.713a.513.513 0 0 0 .708.18l2.956-1.78-.074 3.433a.51.51 0 0 0 .51.518h.831a.51.51 0 0 0 .511-.518l-.074-3.432 2.956 1.78a.513.513 0 0 0 .708-.181l.415-.714a.506.506 0 0 0-.196-.699Z",fill:"#30578C"})),_i||(_i=a.createElement("circle",{cx:1.5,cy:11,r:1.5,fill:"#30578C"})))},vi=(n.p,function(e){var t=e.Icon,n=e.onChange,r=e.tooltip,i=e.index,s=e.className,o=e.value,l=e.disabled,c=e.type,u=e.isDisabled,p=(0,a.useState)(null!==o&&void 0!==o&&o),d=(0,k.Z)(p,2),m=d[0],f=d[1],_=(0,a.useState)(""),g=(0,k.Z)(_,2),b=g[0],h=g[1],v="search-replace-tooltip-".concat(i+c);return a.createElement("div",{className:"search-replace-option ".concat(s)},a.createElement(ft.Z,{effect:"solid",place:"top",type:"light",delayUpdate:500,delayHide:50,border:!0,className:"action-tooltip search-replace-option-tooltip",id:v,getContent:function(){return b!==v?null:(0,E.ZP)(r)}}),a.createElement("input",{"data-for":v,"data-tip":r,className:u?"option-disabled":"",id:c+i,type:"checkbox",value:m,checked:m,disabled:null!==l&&void 0!==l&&l,onChange:function(e){f(e.target.checked),n&&n(e.target.checked)},onFocus:function(){return h(v)}}),a.createElement("label",{"data-for":v,"data-tip":r,htmlFor:c+i,className:"toggle ".concat(m?"active":""),onMouseOver:function(){return h(v)}},a.createElement(t,{className:"option-icon","aria-hidden":"true"}),a.createElement("span",{className:"screen-reader-text"},(0,E.ZP)(r))))}),Ei=function(e){var t=e.index,n=e.item,r=n.regex,s=(0,_.Yu)(),o=(0,_.ME)(),l=o||di("regex",n),c=(0,i.I0)(),u=(0,a.useState)(r),p=(0,k.Z)(u,2),d=p[0],m=p[1],f=(0,w.gB)((0,w.__)("Run a <b>find and replace</b> using a <b>regular expression</b>. Delimiters required and modifiers accepted. <br> <a class='semibold underline' href='%s' target='_blank'>Learn more about Regex</a>"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source=%7B$tracking_base%7D&utm_medium=insideplugin&utm_content=new%2Burl%2Bvalidation#regex-find-replace");l&&o&&(f=(0,_.mF)("regex",s));var h=(0,a.useCallback)((0,er.debounce)((function(e,t){c(function(e,t){return function(){var n=(0,b.Z)((0,g.Z)().mark((function n(r){var a;return(0,g.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!e){n.next=6;break}return n.next=3,r(pi(t));case 3:return a=n.sent,r((0,ya.zf)({key:t,option:"isValidRegex",value:a})),n.abrupt("return",a);case 6:return r((0,ya.zf)({key:t,option:"isValidRegex",value:null})),n.abrupt("return",!0);case 8:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()}(e,t))}),300),[]);return(0,a.useEffect)((function(){n.case_sensitive&&m(!1)}),[n]),(0,a.useEffect)((function(){h(d,t),d?(c((0,ya.zf)({key:t,option:"replace_old_placeholder",value:'/\\[foo id="([0-9]+)"/'})),c((0,ya.zf)({key:t,option:"replace_new_placeholder",value:'[bar id="$1"'}))):(c((0,ya.zf)({key:t,option:"replace_old_placeholder",value:null})),c((0,ya.zf)({key:t,option:"replace_new_placeholder",value:null})))}),[n.replace_old,d]),a.createElement(vi,{Icon:hi,type:"regex",tooltip:f,index:t,value:d,isDisabled:l,disabled:o,onChange:function(e){m(e),c((0,ya.zf)({key:t,option:"regex",value:e})),e&&c((0,ya.zf)({key:t,option:"case_sensitive",value:!1}))}})},wi=function(e){var t=e.index,n=e.item,r=n.case_sensitive,s=(0,i.I0)(),o=(0,a.useState)(r),l=(0,k.Z)(o,2),c=l[0],u=l[1];return(0,a.useEffect)((function(){n.regex&&u(!1)}),[n]),(0,a.useEffect)((function(){s((0,ya.zf)({key:t,option:"case_sensitive",value:c})),c&&s((0,ya.zf)({key:t,option:"regex",value:!1}))}),[c]),a.createElement(vi,{Icon:gi,type:"case-sensitive",tooltip:(0,w.__)("Run a case-sensitive find and replace.","wp-migrate-db"),className:"case-sensitive",index:t,value:r,isDisabled:di("case_sensitive",n),onChange:function(e){u(e)}})},yi=(0,i.$j)((function(e){return{local_site:e.migrations.local_site}}))((function(e){var t=e.index,n=e.item,r=e.local_site,i=r.this_url,s=r.this_path;return(0,_.Ph)(i)===n.replace_old||s===n.replace_old?null:a.createElement("fieldset",{"aria-label":(0,w.__)("Find and replace row options","wp-migrate-db")},a.createElement(Ei,{index:t,item:n}),a.createElement(wi,{index:t,item:n}))}));var xi,ki=function(e,t,n,r){e.updateCustomSearchReplace({key:t,option:n,value:r})},Zi=function(e,t){t.preventDefault();var n="keypress"===t.type;e.addItemCustomSearchReplace(n)},Ti=(0,i.$j)((function(e){var t=(0,J.r5)("status",e),n=(0,xn.O)("panelsOpen",e),r=(0,J.NR)("search_replace",e);return{status:t,panelsOpen:n,custom_search_replace:r.custom_search_replace,search_replace:r,existingProfile:(0,J.r5)("selected_existing_profile",e),intent:(0,J.r5)("intent",e)}}),{updateCustomSearchReplace:ya.zf,reorderCustomSearchReplace:ya.GJ,addItemCustomSearchReplace:ya.FL,deleteItemCustomSearchReplace:ya.Px,setCustomSearchReplace:ya.EX})((function(e){var t=e.custom_search_replace,n=e.panelsOpen,r=e.status,s=e.search_replace,l=e.intent,c=e.existingProfile,u=(0,i.I0)();(0,a.useEffect)((function(){if("savefile"===l&&null!==c){var e=n.filter((function(e){return"custom_fields"!==e}));u((0,W.G7)(e))}}),[]);var p=s.custom_search_domain_locked,d=t.map((function(t,n){return a.createElement(Qa._l,{key:t.id,draggableId:t.id,index:n},(function(r,i){var s,l="custom-search custom-search-row row".concat(i.isDragging?" dragging":"");return a.createElement("fieldset",(0,o.Z)({key:n,ref:r.innerRef},r.dragHandleProps,r.draggableProps,{style:(s=r.draggableProps.style,i.isDragging,(0,x.Z)({userSelect:"none"},s)),className:l,"aria-label":(0,w.__)("Find and replace row","wp-migrate-db")}),a.createElement("div",{className:"handle cell"},a.createElement(ii,{"aria-hidden":"true"})),a.createElement("div",{className:"cell"},a.createElement("input",{type:"text",onChange:function(t){ki(e,n,"replace_old",t.target.value)},value:t.replace_old,placeholder:t.replace_old_placeholder,className:"consolas replace-old".concat(p&&0===n?" disabled":""," ").concat(!1===t.isValidRegex&&!0===t.regex?"invalid-expression":""),disabled:p&&0===n,"aria-label":(0,w.__)("Find","wp-migrate-db"),autoFocus:t.focus})),a.createElement("div",{className:"custom-search-arrow cell"},a.createElement("button",{onClick:function(){return function(e,t,n){e.updateCustomSearchReplace({key:t,option:"replace_new",value:n})}(e,n,t.replace_old)}},a.createElement(xa.r,{"aria-hidden":"true"}),a.createElement("span",{className:"screen-reader-text"},(0,w.__)("Copy find field to replace field","wp-migrate-db")))),a.createElement("div",{className:"cell"},a.createElement("input",{type:"text",onChange:function(t){ki(e,n,"replace_new",t.target.value)},value:t.replace_new,placeholder:t.replace_new_placeholder,className:"consolas","aria-label":(0,w.__)("Replace","wp-migrate-db")})),a.createElement("div",{className:"close cell"},a.createElement("button",{onClick:function(){!function(e,t){e.deleteItemCustomSearchReplace(t)}(e,n)}},a.createElement(li,{"aria-hidden":"true"}),a.createElement("span",{className:"screen-reader-text"},(0,w.__)("Remove row","wp-migrate-db")))),a.createElement("div",{className:"cell search-replace-row-options"},a.createElement(yi,{index:n,item:t})))}))})),m=Xa()(t,(function(e){return e.replace_old.length>0}));return a.createElement(It.Z,(0,o.Z)({title:e.title,panelName:e.panelName,isOpen:!0,selected:m,header:a.createElement(Oa,(0,o.Z)({count:t.length,panelsOpen:n,title:e.title,panelKey:"custom_fields"},e)),panelSummary:a.createElement(Sa,(0,o.Z)({},e,{fields:t,type:e.panelName,searchKeys:{search:"replace_old",replace:"replace_new"},panelsOpen:e.panelsOpen})),bodyClass:"custom-search-replace search-replace free"},e),a.createElement(wa,{className:"custom-search"},a.createElement(Qa.Z5,{onDragEnd:function(n){if(n.destination){var r=$a(t,n.source.index,n.destination.index);e.reorderCustomSearchReplace(r)}}},a.createElement(Qa.bK,{droppableId:"droppable"},(function(e,t){return a.createElement("fieldset",(0,o.Z)({ref:e.innerRef,style:(t.isDraggingOver,{}),"aria-label":(0,w.__)("Custom Find & Replace","wp-migrate-db")},e.droppableProps),d,e.placeholder)})))),(0,mn.a)(r,"COMMON_SEARCH_REPLACE_EMPTY")&&a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.gB)((0,w.__)('Please make sure all find & replace fields have values above. More information about these fields can be found in <a href="%s" target="_blank" rel="noopener roreferrer">our documentation.</a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source={$tracking_base}&utm_medium=insideplugin&utm_content=new%2Burl%2Bvalidation"))),(0,mn.a)(r,"COMMON_SEARCH_REPLACE_INVALID_REGEX")&&a.createElement(z.Z,{type:"danger"},(0,E.ZP)((0,w.gB)((0,w.__)("Please make sure all 'Regular Expression' find & replace patterns are valid. <a href='%s'>Learn more about Regex</a>","wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/find-and-replace/?utm_campaign=support%2Bdocs&utm_source=%7B$tracking_base%7D&utm_medium=insideplugin&utm_content=new%2Burl%2Bvalidation#regex-find-replace"))),a.createElement("p",null,a.createElement("button",{className:"btn btn-sm btn-stroke",onClick:function(t){return Zi(e,t)},onKeyPress:function(t){return Zi(e,t)}},a.createElement("div",{className:"flex-container"},a.createElement("div",{className:"btn-sm-icon icon-16"},a.createElement(ni,{"aria-hidden":"true"})),(0,w.__)("Add Row","wp-migrate-db")))))})),Si=a.forwardRef((function(e,t){var n=e.panelsOpen,r=void 0===n?[]:n,i=e.registeredPanels,s=void 0===i?[]:i,o=e.onClick,l=r.some((function(e){return W.$z.includes(e)&&s.includes(e)}))?(0,w.__)("Collapse All","wp-migrate-db"):(0,w.__)("Expand All","wp-migrate-db");return a.createElement("button",{ref:t,className:"link",onClick:o,"aria-label":(0,w.gB)("%s %s",l,(0,w.__)("database panels.","wp-migrate-db"))},l)})),Ni=v.ZP.div(xi||(xi=(0,h.Z)(["\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  align-items: start;\n  column-gap: 1.35rem;\n"])));var Pi=(0,i.$j)((function(e){var t,n=null!==(t=e.migrations.connection_info)&&void 0!==t?t:{},r=n.connecting,a=void 0!==r&&r,i=n.status,s=e.migrations,o=s.local_site,l=s.remote_site,c=e.migrations.current_migration.intent,u=e.profiles.current_profile;return{connecting:a,status:i,local_site:o,remote_site:l,intent:c,current_migration:e.migrations.current_migration,panels:e.panels,search_replace:e.migrations.search_replace,current_profile:u}}),{updateSelectedTables:Y.E2,updatePanelTitle:W.H5,setCustomSearchReplace:ya.EX,setMigrationEnabled:Y.I8})((function(e){var t=(0,i.I0)(),n=e.current_migration,r=n.intent,s=n.databaseEnabled,o=e.local_site,l=e.remote_site,c=void 0===l?{}:l,u=o.this_tables,p=o.this_table_sizes_hr,d=c.tables,m=c.table_sizes_hr,f=(0,_.qu)(r,o,c),g=f.disabled,b=f.uploads_dir,h=(0,Va.Lk)(r,{tables:u,tableSizes:p},{tables:d,tableSizes:m},"migration"),v=h.tables,E=h.tableSizes,y=(0,a.useRef)(),x=u,k=p;"push"===r&&(x=d,k=m);var Z=e.current_migration.tables_option,T=lt()(["savefile","backup_local"],r)&&g;if((0,a.useEffect)((function(){T&&e.setMigrationEnabled(!1),"import"!==r&&null===e.current_profile&&"all"===Z&&e.updateSelectedTables(v)}),[]),T)return a.createElement(Ga,{uploads_dir:b});var S=e.local_site.this_prefix,N=e.local_site.this_post_types,P=e.search_replace.standard_search_visible;if("pull"!==r&&"import"!==r||(S=e.remote_site.prefix,N=e.remote_site.post_types),e.connecting)return null;var O=Fa(e),A=["push","pull","savefile"].includes(r),C=s||!A,R=function(){t((0,G.m)(H.AV))};return a.createElement("div",null,a.createElement(It.Z,{title:e.panels.panelTitles.database,panelName:"database",enabled:s,hasInput:A,hideArrow:!0,panelOpen:s,panelSummary:C&&a.createElement(Si,{ref:y,onClick:function(){t((0,W.Xl)())},panelsOpen:e.panels.panelsOpen,registeredPanels:e.panels.registeredPanels}),toggle:function(){R()},callback:function(e){if("checkbox"!==e.target.type&&(!y.current||y.current!==e.target)){if(!s)return R();t((0,W.Xl)())}},className:A?"database_panel_has_controls":"database_panel_no_controls"},a.createElement(Ni,null,a.createElement("div",null,"import"!==r&&a.createElement(za,{title:(0,w.__)("Tables","wp-migrate-db"),tables:v,tableSizes:E,prefix:S,intent:r,panelName:"tables",childPanel:!0}),a.createElement(ha,{title:(0,w.__)("Advanced Options","wp-migrate-db"),intent:r,panelName:"advanced_options",childPanel:!0})),a.createElement("div",null,!lt()(["savefile","backup_local"],r)&&a.createElement(qa,{title:La[r].backup,tables:x,tableSizes:k,prefix:S,intent:r,panelName:"backups",childPanel:!0}),"import"!==r&&a.createElement(Ua,{title:(0,w.__)("Post Types","wp-migrate-db"),postTypes:N,intent:r,panelName:"post_types",childPanel:!0}))),lt()(["push","pull","import","savefile"],r)&&P&&a.createElement(Da,{title:(0,w.__)("Standard Find & Replace","wp-migrate-db"),panelName:"standard_fields",childPanel:!0}),"backup_local"!==r&&a.createElement(Ti,{title:(0,w.__)("Custom Find & Replace","wp-migrate-db"),prefix:S,intent:r,panelName:"custom_fields",childPanel:!0}),O.map((function(e,t){return a.createElement(a.Fragment,{key:t},e)}))))})),Oi=n(26547),Ai=n(66441),Ci=n(39881),Ri=function(e){return function(t,n){var r=(0,J.NR)("local_site",n());return"import"===(0,J.r5)("intent",n())&&"undefined"!==typeof e.data.multisite&&"undefined"!==typeof e.data.subsite_export&&("false"===r.is_multisite&&"true"===e.data.multisite&&"true"!==e.data.subsite_export||"true"===r.is_multisite&&"false"===e.data.multisite)}},Ii=function(e){return function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n,r){var a;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e.target.readyState===FileReader.DONE){t.next=2;break}return t.abrupt("return",!1);case 2:return t.prev=2,t.next=5,(0,_.op)("/get-import-info",{file_data:e.target.result});case 5:a=t.sent,t.next=12;break;case 8:throw t.prev=8,t.t0=t.catch(2),console.error(t.t0),new Error(t.t0);case 12:if(!n(Ri(a))){t.next=14;break}throw new Error("mst-error");case 14:if("undefined"===typeof a.wpmdb_error||1!==a.wpmdb_error){t.next=16;break}throw new Error(a.body);case 16:return t.abrupt("return",a);case 17:case"end":return t.stop()}}),t,null,[[2,8]])})));return function(e,n){return t.apply(this,arguments)}}()},Di=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,n){var r,a,i,s;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=new FileReader,a=(0,k.Z)(n,1),i=a[0],n.length){e.next=6;break}throw new Error(window.wpmdb_strings.please_select_sql_file);case 6:if(".sql"===i.name.slice(-4)||".sql.gz"===i.name.slice(-7)){e.next=8;break}throw new Error(window.wpmdb_strings.invalid_sql_file);case 8:return s=n[0].slice(0,1024e3),r.readAsDataURL(s),e.abrupt("return",r);case 11:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Mi=function(e){return function(t,n){return t((0,G.m)(Oi.Ej,e))}},Fi=function(e){return function(t,n){return t((0,G.m)(Oi.EL,e))}},Li=function(e,t,n){return function(r,a){var s,o=(0,J.r5)("intent",a());r((s={upload_file:t,file_uploaded:!0,file_size:n},function(e,t){return e((0,G.m)(Ai.S_)),e((0,G.m)(Oi.ci,s))}));var l=!0;e.data.tables||(l=!1,r(Mi("NON_MDB_IMPORT_ERROR")),r((0,ya.EX)("import",!0))),(0,i.dC)((function(){r((0,G.m)(Oi.WH,e.data.tables)),r((0,ya.CN)(l)),r((0,Y.E2)(e.data.tables||[])),r((0,Ci.G9)(e,!1,o))}));var c="PREFIX_MISMATCH";if((0,_.Ol)(e.data.prefix)&&e.data.tables)return r(Mi(c)),c}},Bi=function(e){return function(t,n){t((0,G.m)(Oi.Qy,e))}},Ui=function(e){var t=e.migrations.local_site.is_multisite,n=(0,w.gB)((0,w.__)('It looks like the file you are trying to import is from a multisite install and this install is a single site. To run this type of import you\'ll need to use the <a href="%1$s" target="_blank" rel="noopener noreferrer">Multisite tools addon</a> to export a subsite as a single site. <a href="%2$s" target="_blank">Learn more \xbb</a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/multisite-tools-addon/","https://deliciousbrains.com/wp-migrate-db-pro/doc/multisite-tools-addon/#export-subsite");return"true"===t&&(n=(0,w.gB)((0,w.__)('It looks like the file you are trying to import is from a single site install and this install is a multisite. This type of migration isn\'t currently supported. <a href="%s" target="_blank" rel="noopener noreferrer">Learn more \xbb</a>',"wp-migrate-db"),"https://deliciousbrains.com/wp-migrate-db-pro/doc/multisite-tools-addon/")),a.createElement(z.Z,{type:"danger"},(0,E.ZP)(n))},ji=n(67821),zi=function(e){var t=e.panels.panelsOpen,n=e.panelName,r=e.fileUploaded,i=e.uploadFile;return t.includes(n)?null:a.createElement("div",{className:"panel-summary"},r?i:"")},Gi=(0,i.$j)((function(e){var t=(0,ji.selectFromImportData)("status",e),n=(0,J.r5)("intent",e),r=(0,J.NR)("import_data",e),a="LOADING"===t,i=(0,ji.selectFromImportData)("error",e),s=(0,J.FY)("prefix",e),o=e.migrations;return{intent:n,import_status:t,loading:a,import_error:i,remote_prefix:s,import_data:r,panels:e.panels,migrations:o}}),{updateOpenPanels:W.G7,updateMigrationPanels:W.rC,updatePanelTitle:W.H5,addAdditionalPanel:W.qb,removePanel:W.WJ,setFileUploadFilePath:function(e){return function(t,n){return t((0,G.m)(Oi.Dx,e))}},updateImportStatus:Mi,resetImportData:function(){return function(e,t){return e((0,G.m)(Oi.yQ))}},initialFileUpload:function(e,t,n){return function(){var n=(0,b.Z)((0,g.Z)().mark((function n(r,a){var i;return(0,g.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,Di(t,e.current.files);case 3:n.sent.addEventListener("loadend",function(){var t=(0,b.Z)((0,g.Z)().mark((function t(n){var a,s,o,l,c;return(0,g.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r(Mi("LOADING")),t.prev=1,t.next=4,r(Ii(n));case 4:i=t.sent,t.next=13;break;case 7:return t.prev=7,t.t0=t.catch(1),a=t.t0.message,r("mst-error"===a?Mi("MST_WARNING"):Fi({error:!0,message:t.t0.message})),r((0,W.WJ)("database")),t.abrupt("return",!1);case 13:return r(Mi("")),s=e.current.files[0].name,o=e.current.files[0].size,r(Bi(e.current.files[0])),l=r(Li(i,s,o)),r((0,Y.I8)(!0)),c=["database","custom_fields","standard_fields"],i.data.tables&&"PREFIX_MISMATCH"!==l||c.push("import"),r((0,W.G7)(c)),t.abrupt("return",r((0,W.qb)("database","import")));case 23:case"end":return t.stop()}}),t,null,[[1,7]])})));return function(e){return t.apply(this,arguments)}}()),n.next=12;break;case 7:n.prev=7,n.t0=n.catch(0),r(Fi({error:!0,message:n.t0.message})),r((0,W.WJ)("database")),r((0,Y.I8)(!1));case 12:case"end":return n.stop()}}),n,null,[[0,7]])})));return function(e,t){return n.apply(this,arguments)}}()}})((function(e){var t=e.import_status,n=e.loading,r=e.import_error,i=e.remote_prefix,s=e.import_data,l=e.panels.panelsOpen,c=(0,a.useState)((0,w.__)("No file selected","wp-migrate-db")),u=(0,k.Z)(c,2),p=u[0],d=u[1],m=(0,a.useRef)(),f=function(){return a.createElement(a.Fragment,null,(0,E.ZP)((0,w.__)("<strong>Unrecognized File</strong> &mdash; This file does not appear to have been generated by WP Migrate. Importing files generated by phpMyAdmin, WP-CLI, etc. should work, but we recommend using WP Migrate's Export feature.","wp-migrate-db"))," ",a.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/doc/why-use-our-export/",target:"blank"},(0,w.__)("Why?","wp-migrate-db")))};if((0,_.ME)())return null;var g=!1,b=["import-panel"];return l.includes("import")||(g=!0,b.push("has-divider")),a.createElement(It.Z,(0,o.Z)({title:e.panels.panelTitles.import,panelName:"import",panelSummary:a.createElement(zi,(0,o.Z)({},e,{fileUploaded:s.file_uploaded,uploadFile:s.upload_file,panelName:"import"})),forceDivider:g,bodyClass:"import-panel",className:b.join(" ")},e),a.createElement("div",{className:"flex-container import-message"},a.createElement("input",{type:"file",ref:m,id:"import-file",onChange:function(t){e.resetImportData(),m.current.files[0]&&d(m.current.files[0].name),e.initialFileUpload(m,t,e.baseTitle)},className:"input-button"}),a.createElement("label",{htmlFor:"import-file",className:"btn btn-stroke btn-sm"},"choose file"),a.createElement("span",{className:"muted"},p)),n&&a.createElement("h3",null,"Loading file..."),r.error&&a.createElement(z.Z,{type:"danger"},r.message),"PREFIX_MISMATCH"===t&&a.createElement(z.Z,{type:"danger"},a.createElement("h4",{style:{color:Tn.qP}},(0,w.__)("Warning: Different Table Prefixes","wp-migrate-db")),(0,w.gB)((0,w.__)('Whoa! We\'ve detected that the database table prefix in the import file does not match the database prefix of this install. Clicking the Import button below will create new database tables with the prefix "%s"',"wp-migrate-db"),i)),"NON_MDB_IMPORT_ERROR"===t&&a.createElement(z.Z,{type:"danger"},a.createElement(f,null)),"MST_WARNING"===t&&a.createElement(Ui,e))})),Vi=n(34363),Hi=function(e){var t=e.e,n=e.field,r=e.dispatch,a=e.authState,i=e.connectionState,s=(0,x.Z)({},a);s[n]=t.target.value,r((0,Vi.setConnectionStatus)("auth_form",s));var o=(0,x.Z)({},i);if(o.value){var l=function(e,t){var n=(0,x.Z)({},e),r=t.username,a=t.password,i=e.url,s=new URL(i),o=s.host+s.pathname;return n.url="".concat(s.protocol,"//").concat(r,":").concat(a,"@").concat(o),0===r.length&&0===a.length&&(n.url="".concat(s.protocol,"//").concat(o)),n.value=n.url+"\n"+n.key,n}(o,{username:encodeURIComponent(s.username),password:encodeURIComponent(s.password)});r((0,Vi.updateConnectionState)(l))}},Wi=function(e){var t=(0,i.I0)(),n=(0,i.v9)((function(e){return e.migrations.current_migration.connected})),r=(0,i.v9)((function(e){return e.migrations.connection_info.status})).auth_form,s=(0,i.v9)((function(e){return e.migrations.connection_info})).connection_state;return n?null:a.createElement("div",{className:"grid-container auth-form"},a.createElement("input",{type:"text",placeholder:(0,w.__)("Username"),onChange:function(e){return Hi({e:e,field:"username",dispatch:t,authState:r,connectionState:s})},value:r.username,key:"username"}),a.createElement("input",{type:"text",placeholder:(0,w.__)("Password"),onChange:function(e){return Hi({e:e,field:"password",dispatch:t,authState:r,connectionState:s})},value:r.password,key:"password"}))},Yi=function(e){return a.createElement(z.Z,{type:"warning"},a.createElement("strong",null,(0,w.__)("HTTPS Disabled","wp-migrate-db"))," \u2014"," ",(0,w.__)("We couldn't connect over HTTPS but regular HTTP appears to be working so we've switched to that. If you run a push or pull, your data will be transmitted unencrypted. Most people are fine with this, but just a heads up.","wp-migrate-db"))},Ki=n(81690),qi=n.n(Ki),Ji=n(53273),Xi=n(91490),Qi=function(e){var t=e.shortName,n=e.success,r=(0,a.useState)(!1),s=(0,k.Z)(r,2),o=s[0],l=s[1],c=(0,i.v9)((function(e){return e.theme_plugin_files})),u=null;return c&&(u=c.remotePluginUpdated),(0,a.useEffect)((function(){t===Ji.LD?l(u):l(n),setTimeout((function(){l(!1)}),1500)}),[u,n]),a.createElement(Xi.Z,{in:o,timeout:500,classNames:"settings-node",id:gr()(),unmountOnExit:!0},a.createElement("div",null,(0,w.__)("Updated Successfully","wp-migrate-db"),a.createElement("div",{className:"blue-check"},a.createElement(ct.en,null))))},$i="SET_UPDATE_ERROR",es="SET_UPDATING",ts="SET_UPDATE_SUCCESS",ns="SET_WILL_ENABLE_BETA_UPDATES",rs="SET_PLUGIN_CHECK_AGAIN_TEXT",as=function(e,t){switch(t.type){case $i:return(0,x.Z)((0,x.Z)({},e),{},{updateError:t.payload});case es:return(0,x.Z)((0,x.Z)({},e),{},{updating:t.payload});case ts:return(0,x.Z)((0,x.Z)({},e),{},{updateSuccess:t.payload});case ns:return(0,x.Z)((0,x.Z)({},e),{},{willEnableBetaUpdates:t.payload});case rs:return(0,x.Z)((0,x.Z)({},e),{},{checkAgainText:t.payload});default:return e}},is=(0,i.$j)((function(e){return{remote_site:e.migrations.remote_site,intent:e.migrations.current_migration.intent,theme_plugin_files:e.theme_plugin_files}}))((function(e){var t=(0,a.useReducer)(as,{updateError:"",checkAgainText:"",updating:!1,updateSuccess:!1,willEnableBetaUpdates:!1}),n=(0,k.Z)(t,2),r=n[0],s=n[1],o=r.updateError,l=r.updating,c=r.updateSuccess,u=r.checkAgainText,p=r.willEnableBetaUpdates,d=e.intent,m=e.remote_site,f=e.shortName,h=e.pluginSlug,v=e.remoteUpgradable,y=e.theme_plugin_files,x=null;y&&(x=y.remotePluginUpdated);var Z=m.beta_optin,T=(0,i.I0)(),S=h?(0,w.__)("<b>Could not update addon on remote site &mdash;</b> %s","wp-migrate-db"):(0,w.__)("<b>Could not update plugin on remote site &mdash;</b> %s","wp-migrate-db");(0,a.useEffect)((function(){s((0,G.m)($i,e.message)),!qi()(m)&&e.version.includes("b")&&!Z&&v&&(s((0,G.m)(ns,!0)),s((0,G.m)(rs,(0,w.gB)((0,w.__)("Enable beta updates on remote and upgrade to version %s","wp-migrate-db"),e.version))))}),[e]);var N=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(){var t,n;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return s((0,G.m)(es,!0)),e.next=3,T((0,Vi.updatePluginOnRemote)(h));case 3:if(t=e.sent,s((0,G.m)(es,!1)),t.success){e.next=11;break}return n=(0,dt.Y)(t),s((0,G.m)($i,(0,w.gB)(S,n))),s((0,G.m)(rs,(0,w.__)("Try again","wp-migrate-db"))),s((0,G.m)(ts,!1)),e.abrupt("return");case 11:s((0,G.m)(ts,!0)),setTimeout((function(){T((0,Vi.setConnectionStatus)("update_plugin_on_remote",!1));var e=(0,_.OK)();if(!1!==e){var t=(0,k.Z)(e,2),n=t[0],r=t[1];T(te({path:"unsaved"===n?"unsaved":"saved",params:{id:r}}))}else T((0,Vi.connectToRemote)(d))}),0);case 13:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),P=function(e){var t=e.initialError,n=e.updateError;if(!v||c||!n)return null;var r=n!==t||p?u:(0,w.__)("Update plugin on remote","wp-migrate-db");return a.createElement("button",{onClick:N,className:"link"},r)},O=c||x&&Ji.LD===f,A=a.createElement("div",null,!O&&a.createElement(a.Fragment,null,(0,E.ZP)(o)," ",h?"":" ",a.createElement(P,{updateError:o,initialError:e.message,remoteUpgradable:v}),l&&a.createElement(ct.Q,{className:"remote-update-spinner"})),a.createElement(Qi,{shortName:f,success:c}));return qi()(h)&&v?a.createElement(z.Z,{type:"danger"},A):a.createElement("div",{className:"full flex-container version-mismatch-error"},A)})),ss=function(e){var t=e.message,n=e.intent,r=(0,a.useState)(t),s=(0,k.Z)(r,2),o=s[0],l=s[1],c=(0,a.useState)(!1),u=(0,k.Z)(c,2),p=u[0],d=u[1],m=(0,i.I0)(),f=function(){var e=(0,b.Z)((0,g.Z)().mark((function e(){var t,r;return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return d(!0),e.next=3,m((0,Vi.copyLicenseToRemote)());case 3:if((t=e.sent).success){e.next=9;break}return r=(0,dt.Y)(t),l("".concat((0,w.__)("Could not copy license to remote site &mdash;","wp-migrate-db")," ").concat(r)),d(!1),e.abrupt("return",null);case 9:d(!1),m((0,Vi.setConnectionStatus)("copy_to_remote",!1)),m((0,Vi.connectToRemote)(n));case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),_=function(e){var t=e.initialError,n=e.copyError,r=n!==t&&n?(0,w.__)("Try again.","wp-migrate-db"):(0,w.__)("Copy license to remote.","wp-migrate-db");return a.createElement("button",{onClick:f,className:"link"},r)};return a.createElement(a.Fragment,null,a.createElement(z.Z,{type:"danger"},(0,E.ZP)(o||t),"\xa0",a.createElement(_,{initialError:t,copyError:o}),p&&a.createElement("div",{className:"relative"},a.createElement(ct.Q,{className:"svg-spinner"}))))},os=function(e){var t=e.connection_info.status,n=t.auth_form,r=t.show_auth_form,i=t.error,s=t.error_msg,l=t.ssl_notice,c=t.update_plugin_on_remote,u=t.copy_to_remote,p=t.connecting,d=e.connection_info.connection_state,m=e.current_migration,f=m.connected,_=m.intent,g=[];return l&&(f||i)&&g.push(a.createElement(Yi,null)),r&&g.push(a.createElement(Wi,{authState:n,connectionState:d,updateConnectionState:e.updateConnectionState})),p||(u&&g.push(a.createElement(ss,{message:s,intent:_})),!i||f||u||(c?g.push(a.createElement(is,(0,o.Z)({message:s},e,{intent:_,remoteUpgradable:"true"}))):""!==s&&g.push(a.createElement(z.Z,{type:"danger"},(0,E.ZP)(s))))),g};var ls,cs=function(){var e=(0,i.I0)(),t=(0,i.v9)((function(e){return e})),n=e((0,Ca.O)("wpmdb_post_connection_errors",{warning:[],info:[],danger:[]},t));return a.createElement(a.Fragment,null,Object.keys(n).filter((function(e){return n[e].length>0})).map((function(e,t){return a.createElement(z.Z,{key:t,type:e},a.createElement("ul",null,n[e].map((function(e){return a.createElement("li",{key:t},(0,E.ZP)(e))}))))})))},us=(0,v.ZP)(It.Z)(ls||(ls=(0,h.Z)(["\n  h2 {\n    margin: 15px 0;\n  }\n\n  textarea {\n    margin-bottom: 10px;\n  }\n\n  button.btn-sm {\n    margin-top: 10px;\n  }\n\n  &.disabled {\n    opacity: 0.6;\n  }\n"])));var ps=function(e){var t=e.panelsOpen,n=e.connected,r=e.connection_info.connection_state.url;return t.includes(e.panelName)?null:a.createElement("div",{className:"panel-summary"},n?r:"")},ds=(0,i.$j)((function(e){var t=(0,J.NR)("current_migration",e),n=e.panels.panelsOpen,r=(0,J.NR)("local_site",e),a=(0,J.NR)("connection_info",e);return{current_migration:t,status:a.status,panels:e.panels,connection_info:a,panelsOpen:n,local_site:r}}))((function(e){var t=(0,i.I0)(),n=(0,_.ME)(),r=!1,s=["connect-panel"];e.panels.panelsOpen.includes("connect")||(r=!0,s.push("has-divider"));var l,c,u=(l=J.NR,c="current_migration",(0,i.v9)((function(e){return l(c,e)}))),p=u.intent,d=u.connected;(0,a.useEffect)((function(){d||t((0,Ca.ku)("wpmdb_post_connection_errors"))}),[d]);var m=e.connection_info.connection_state,f=e.connection_info.status,g=f.connecting,b=f.button_status,h=(0,a.useRef)(),v=(0,i.v9)((function(e){return e.api.abort_controller}));(0,a.useEffect)((function(){return function(){v.hasOwnProperty("connect_to_remote")&&v.connect_to_remote.abort()}}),[]);var E=os(e),y=function(e){var t="",n=e.connection_info.status,r=n.connecting,a=n.button_status;return(r||"disabled"===a)&&(t=" btn-disabled"),t}(e);return a.createElement(a.Fragment,null,a.createElement(us,(0,o.Z)({title:e.panels.panelTitles.connect,panelName:"connect",panelSummary:a.createElement(ps,(0,o.Z)({panelName:"connect"},e,{connected:d})),bodyClass:"connect-panel",className:s.join(" "),forceDivider:r},e),a.createElement("div",{className:"connect-to-remote"},a.createElement("textarea",{placeholder:(0,w.__)("Connection Info - Site URL & Secret Key","wp-migrate-db"),onChange:function(e){return function(e){if(g)return!1;t((0,Vi.changeConnection)(e.target.value))}(e)},onPaste:function(e){t((0,Vi.setConnectionStatus)("pasted",!0))},onFocus:function(e){return function(){if(!d)return null;var e=void 0;if(d&&"hidden"===b&&(e=window.confirm((0,w.__)("If you change the connection details, you will lose any replaces and table selections you have made below. Do you wish to continue?","wp-migrate-db"))),e)return t((0,Vi.setConnectionStatusBatch)({button_status:"",show_auth_form:!1,error_msg:""})),t((0,Vi.resetMigration)(["connect"])),!1;!1===e&&h.current.blur()}()},value:m.value||"",className:g?"disabled consolas":"consolas",ref:h}),E.map((function(e,t){return a.createElement(a.Fragment,{key:t},e)})),g&&a.createElement("p",{className:"full connecting-message"},a.createElement("span",null,"Establishing connection to remote server, please wait..."),a.createElement(ct.Q,null))),"hidden"!==b&&!n&&a.createElement("div",null,a.createElement("button",{onClick:function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(g||""!==b||n)return!1;t((0,Vi.connectToRemote)(p,e))}()},className:"btn btn-sm".concat(y)},(0,w.__)("Connect","wp-migrate-db")))),a.createElement(cs,null))})),ms=n(20188),fs=n(82886),_s=(n(74352),n(52089)),gs={files:[],status:{backups_loading:!0,backup_deleting:!1,backup_downloading:!1,backups_error:""},ui:{backups_location:window.wpmdb_data.this_upload_dir_long}},bs=(0,_s.Lq)(gs,{BACKUPS_LOADING:function(e,t){return e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backups_loading:!0,backup_deleting:!1,backup_downloading:!1,backups_error:""}),e},GET_BACKUPS:function(e,t){return e.files=t.payload,e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backups_loading:!1,backups_error:!1}),e},BACKUP_DELETING:function(e,t){return e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backup_deleting:!0,backups_error:""}),e},DELETE_BACKUP:function(e,t){return e.files.splice(t.payload,1),e.status.backup_deleting=!1,e},BACKUP_DOWNLOADING:function(e,t){return e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backup_downloading:!0,backups_error:""}),e},DOWNLOAD_BACKUP:function(e,t){return e.status.backup_downloading=!1,e},BACKUPS_ERROR:function(e,t){return e.status=(0,x.Z)((0,x.Z)({},e.status),{},{backups_loading:!1,backup_deleting:!1,backup_downloading:!1,backups_error:t.payload}),e}}),hs=n(31998),vs=n(666),Es=n(9106),ws=n(19826),ys=n(26429),xs={},ks=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:xs;return(0,ys.ZP)(e,(function(t){return e}))},Zs=(0,_s.Lq)({abort_controller:{},AJAX_RUNNING:!1,AJAX_SUCCESS:!1,AJAX_ERROR:!1},{SET_ABORT_CONTROLLER:function(e,t){return e.abort_controller[t.payload.key]=t.payload.controller,e}}),Ts=n(59299),Ss=n(18066),Ns={mdb_rest_active:!0},Ps=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ns,t=arguments.length>1?arguments[1]:void 0;return(0,ys.ZP)(e,(function(n){return"MDB_REST_NOT_ACTIVE"===t.type?{mdb_rest_active:!1}:e}))},Os=n(15265),As={backups:bs,profiles:V.ZP,migrations:hs.ZP,panels:kn.ZP,notifications:vs.ZP,addons:Es.Z,settings:ws.ZP,global_status:ks,api:Zs,dbi_api_data:Ts.ZP,mdb_filters:Ss.ZP,mdb_rest:Ps,dry_run:Os.xN};function Cs(e,t){if("RESET_APP"===e.type){var n=t;t={profiles:n.profiles,settings:n.settings,global_status:n.global_status,notifications:n.notifications,dbi_api_data:n.dbi_api_data,mdb_filters:n.mdb_filters,mdb_rest:n.mdb_rest}}return t}function Rs(e){return(0,ms.UY)((0,x.Z)((0,x.Z)({},As),e))}var Is=Rs(void 0),Ds=function(e,t){return e=Cs(t,e),Is(e,t)},Ms=[fs.Z];var Fs=("object"===typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__,ms.qC)(ms.md.apply(void 0,Ms)),Ls=(0,ms.MT)(Ds,{},Fs);window.WPMDBStore=Ls,Ls.asyncReducers={};var Bs=Ls,Us=n(64533),js=n(51645),zs=n(65730);function Gs(e,t,n){e.asyncReducers[t]=n;var r=Rs(e.asyncReducers);e.replaceReducer((function(e,t){return e=Cs(t,e),r(e,t)}))}var Vs={local_site:window.wpmdb_data,remote_site:{}},Hs=(0,ms.UY)({local_site:function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:Vs.local_site},remote_site:{},current_migration:H.S_,migration_progress:_e.D$,search_replace:Ai.jC,dry_run:Os.xN}),Ws={profiles:V.ZP,migrations:Hs,panels:kn.ZP,notifications:vs.ZP,settings:ws.ZP,global_status:ks,mdb_filters:Ss.ZP,mdb_rest:Ps,dry_run:Os.xN};var Ys,Ks=(0,ms.UY)((0,x.Z)((0,x.Z)({},Ws),Ys)),qs=function(e,t){return e=function(e,t){if("RESET_APP"===e.type){var n=t;t={profiles:n.profiles,settings:n.settings,global_status:n.global_status,notifications:n.notifications,mdb_filters:n.mdb_filters,mdb_rest:n.mdb_rest,dry_run:n.dry_run}}return t}(t,e),Ks(e,t)},Js=[fs.Z];var Xs=("object"===typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__,ms.qC)(ms.md.apply(void 0,Js)),Qs=(0,ms.MT)(qs,{},Xs);window.WPMDBStore=Qs,Qs.asyncReducers={};var $s=Qs,eo=function(){var e,t,r,i=(0,_.Yu)()?Bs:$s;return e=a.lazy((function(){return Promise.all([n.e(532),n.e(358),n.e(135),n.e(406)]).then(n.bind(n,36890))})),Gs(i,"media_files",Us.ZP),t=a.lazy((function(){return Promise.all([n.e(532),n.e(135),n.e(537)]).then(n.bind(n,2537))})),Gs(i,"theme_plugin_files",js.ZP),r=a.lazy((function(){return Promise.all([n.e(532),n.e(605)]).then(n.bind(n,79605))})),Gs(i,"multisite_tools",zs.ZP),{MediaFiles:e,ThemePluginFiles:t,MultisiteTools:r}}(),to=eo.MediaFiles,no=eo.ThemePluginFiles,ro=eo.MultisiteTools,ao=function(e){var t=(0,i.I0)(),n=(0,i.v9)((function(e){return e.panels})).panelsToDisplay,r=(0,i.v9)((function(e){return e})),s=r.media_files,o=r.theme_plugin_files,l=r.multisite_tools,c=(0,i.v9)((function(e){return e.migrations.current_migration})).intent,u=t((0,Va.C)()),p=function(e,t){return!(!e||!t)&&!(["push","pull"].includes(c)&&!t.is_licensed)};return a.createElement(a.Fragment,null,n.map((function(t,n){switch(t){case"connect":return a.createElement(ds,{key:n});case"multisite_tools":return ro&&"backup_local"!==c&&(!["push","pull"].includes(c)||u)&&l.available&&l.is_licensed?a.createElement(a.Suspense,{fallback:a.createElement("div",null,"Loading..."),key:n},a.createElement(ro,e)):null;case"database":return a.createElement(Pi,{key:n});case"import":return a.createElement(Gi,{key:n,baseTitle:(0,w.__)("SQL File","wp-migrate-db")});case"media_files":return p(to,s)?a.createElement(a.Suspense,{fallback:a.createElement("div",null,"Loading..."),key:n},a.createElement(to,e)):null;case"theme_plugin_files":return p(no,o)?a.createElement(a.Suspense,{fallback:a.createElement("div",null,"Loading..."),key:n},a.createElement(no,e)):null;default:return null}})))},io=function(e){var t=(0,_.Nh)(f),r={backgroundImage:"url(".concat(t,")")},s=(0,i.I0)();return(0,a.useEffect)((function(){s(function(){var e=(0,b.Z)((0,g.Z)().mark((function e(t,r){return(0,g.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:window.hasOwnProperty("wpmdbmf")&&Promise.resolve().then(n.bind(n,33686)).then((function(e){t(e.default())})),window.hasOwnProperty("wpmdbtp")&&Promise.resolve().then(n.bind(n,18213)).then((function(e){t(e.default())})),window.hasOwnProperty("wpmdbmst")&&Promise.resolve().then(n.bind(n,85562)).then((function(e){t(e.default())}));case 3:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}())}),[]),(0,a.useEffect)((function(){!function(){var e=document.getElementsByClassName("update-nag");0!==e.length&&Array.from(e).forEach((function(e){var t=e.innerHTML,n=document.getElementById("nag-container"),r=document.createElement("div");r.innerHTML=t,n.append(r),r.classList.add("notification","bg-white","column","warning-notice")}))}()}),[]),oa(),a.createElement(l.UT,{hashType:"noslash"},a.createElement(fr,{className:"wpmdb free"},a.createElement("div",{className:"header-wrap"},a.createElement("div",{className:"wrapper"},a.createElement("h2",{style:{display:"none"}}),a.createElement("div",{id:"nag-container"}),a.createElement("div",{className:"header mdb-header bg-brand-light flex-container",style:r},a.createElement(m,{className:"medallion"}),a.createElement("h1",null,"WP Migrate"),a.createElement(Jr,null)))),a.createElement(mr,{isPro:!1},a.createElement(c.AW,{path:"/:tab(wpbody-content)?",exact:!0,render:function(e){return a.createElement(se,e)}}),a.createElement(c.AW,{path:["/migrate/:id","/migrate","/unsaved/:id"],render:function(e){return a.createElement(Jn,(0,o.Z)({},e,{ChildPanels:a.createElement(ao,e)}))}}),a.createElement(c.AW,{path:"/settings",render:function(e){return a.createElement(Zr,e)}}),a.createElement(c.AW,{path:"/addons",component:qr}),a.createElement(c.AW,{path:"/help",component:ea}),a.createElement(c.AW,{path:"/whats-new",component:ia}))))};window.wpmdb_abort_controller=new AbortController;var so=document.getElementById("root");(0,s.s)(so).render(a.createElement(i.zt,{store:$s},a.createElement(io,{isPro:!1})))},33686:function(e,t,n){"use strict";n.r(t);var r=n(27166),a=n(33032),i=n(62295),s=n(22497),o=n(78579),l=n(4516),c=n(66055),u=n(64533);t.default=function(){return function(e,t){e((function(e,t){e((0,s.KG)("afterFinalizeMigration",(function(){e((0,c.m)(u.F1,Date.now()))})))})),e((function(e,t){(0,i.dC)((function(){e((0,s.KJ)("wpmdbPreMigrationCheck",(function(t){return e((0,o.xO)(t))}))),e((0,s.KJ)("addMigrationStages",(function(t){return e((0,o.h2)(t))}))),e((0,s.KJ)("mdbAddonActions",function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a){return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!t().media_files.enabled){n.next=5;break}return n.next=4,e((0,o.B0)());case 4:return n.abrupt("return",n.sent);case 5:return n.abrupt("return",a);case 6:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}())),e((0,s.KJ)("wpmdbFinalizeMigration",(function(e){if(!t().media_files.enabled)return e;var n=t().profiles.current_profile,r=(0,l.r5)("profile_type",t());return e.profileID=n,e.profileType=r,e})))}))}))}}},78579:function(e,t,n){"use strict";n.d(t,{$0:function(){return h},AN:function(){return w},B0:function(){return Z},K4:function(){return x},Lm:function(){return v},Um:function(){return E},h2:function(){return k},nv:function(){return b},rM:function(){return y},xO:function(){return P}});var r=n(31125),a=n(27166),i=n(33032),s=n(42233),o=n(64533),l=n(66055),c=n(4516),u=n(3460),p=n(66866),d=n(42714),m=n(10304),f=n(29816),_=n(98135),g=n(73264),b=((0,s.__)("Media Files addon","wp-migrate-db"),function(){return(0,s.__)("<b>Addon Missing</b> - The Media Files addon is inactive on the <strong>remote site</strong>. Please install and activate it to enable media file migration.","wp-migrate-db")}),h=function(){return function(e,t){return e((0,l.m)(o.h6))}},v=function(e){return(0,l.m)(o.U4,e)},E=function(e,t){return function(n){n({type:o.vL,payload:{available:e,message:t}})}},w=function(e){return function(t){t({type:o.F_,payload:e})}},y=function(e,t){return function(t){return t((0,l.m)(o.bv,e))}},x=function(e){return function(t){return t((0,l.m)(o.Xx,e))}};function k(e){return function(t,n){return n().media_files.enabled&&e.push("media_files"),e}}var Z=function(){return function(){var e=(0,i.Z)((0,a.Z)().mark((function e(t,n){var r,i;return(0,a.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n().media_files,i=(0,c.r5)("intent",n()),["push","pull","savefile"].includes(i)&&r.enabled){e.next=4;break}return e.abrupt("return",!1);case 4:return e.next=6,t((0,u.Z6)("ADDONS_STAGE",[{fn:S,args:["media_files"]}]));case 6:return e.abrupt("return",e.sent);case 7:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},T=function(){return function(e,t){var n=(0,c.r5)("intent",t()),r=(0,c._P)("this_wp_upload_dir",t()),a=(0,c.FY)("wp_upload_dir",t());return["push","savefile"].includes(n)?r:a}},S=function e(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(){var r=(0,i.Z)((0,a.Z)().mark((function r(i,s){var o,l,p,d,f,_,b,h,v,E;return(0,a.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n||i((0,g.F)(t)),o=(0,c.r5)("migration_id",s()),l=(0,c.r5)("stages",s()),p=s().media_files,d=i(T()),f=p.excludes,_=p.option,b=p.date,h=p.last_migration,v={stage:"media_files",stages:l,folder:d,migration_state_id:o,excludes:JSON.stringify(f),is_cli_migration:0},"new"===_&&(v.date=b,v.timezone=Intl.DateTimeFormat().resolvedOptions().timeZone),"new_subsequent"===_&&""!==h&&(v.date=new Date(h).toISOString(),v.timezone=Intl.DateTimeFormat().resolvedOptions().timeZone),r.next=11,i((0,m.A)("/mf-initiate-file-migration",v));case 11:if(E=r.sent){r.next=14;break}return r.abrupt("return",!1);case 14:if(!E.data.recursive_queue){r.next=18;break}return r.next=17,i((0,g.T)(t,E,e));case 17:return r.abrupt("return",r.sent);case 18:return r.next=20,i((0,u.Z6)("ADDONS_STAGE",[{fn:N,args:[t,E]}]));case 20:case"end":return r.stop()}}),r)})));return function(e,t){return r.apply(this,arguments)}}()},N=function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return function(){var n=(0,i.Z)((0,a.Z)().mark((function n(i,o){var g,b,h,v,E;return(0,a.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return i((0,l.m)(p.V$,(0,s.__)("Loading queue...","wp-migrate-db"))),g=(0,c.r5)("migration_id",o()),b={stage:"media_files",offset:r,migration_state_id:g},n.next=5,i((0,m.A)("/mf-get-queue-items",b));case 5:if(h=n.sent){n.next=8;break}return n.abrupt("return");case 8:if("complete"!==(v=h.data).status){n.next=14;break}return E={currentStage:t,stage:"media_files",migration_state_id:g,folder:i(T())},n.next=13,i((0,u.Z6)("ADDONS_STAGE",[{fn:f.e5,args:[t,"/mf-transfer-files",E]}]));case 13:return n.abrupt("return");case 14:if(v.hasOwnProperty("queue_status")){n.next=17;break}return i((0,_.Qc)()),n.abrupt("return",!1);case 17:return i((0,d.I6)(v.queue_status.size/1e3)),n.next=20,i((0,u.Z6)("ADDONS_STAGE",[{fn:e,args:[t,h]}]));case 20:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()};function P(e){return function(t,n){var a=n().media_files,i=a.date,s=a.enabled,o=(0,c.r5)("intent",n());if(!s||!["push","pull"].includes(o))return e;var l=(0,r.Z)(e),u=new Date(Date.now());if(new Date(i)>u){l.push({name:"MF_INVALID_DATE",panel:"media_files"})}return"undefined"!==typeof a.option&&null!==a.option||l.push({name:"MF_OPTION_NULL",panel:"media_files"}),l}}},64533:function(e,t,n){"use strict";n.d(t,{F1:function(){return f},F_:function(){return p},U4:function(){return c},Xx:function(){return m},bv:function(){return d},h6:function(){return l},vL:function(){return u}});var r=n(18489),a=n(26429),i=n(29950),s=n(9106),o=n(31998),l="TOGGLE_MEDIA_FILES",c="SET_MEDIA_FILES_OPTION",u="SET_MF_AVAILABLE",p="SET_MF_LICENSED",d="UPDATE_MF_EXCLUDES",m="UPDATE_MF_DATE",f="UPDATE_MF_LAST_MIGRATION",_={enabled:!1,option:"all",available:!1,is_licensed:!1,message:"",excludes:".DS_Store\n*.log\n*backup*/\n*cache*/",last_migration:"",date:new Date(Date.now()).toISOString()};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return n.date=new Date(Date.now()).toISOString(),n;case s.$:return(0,r.Z)((0,r.Z)({},n),{},{available:1===window.wpmdb_data.mf_available,message:""});case l:return n.enabled=!n.enabled,n;case p:return n.is_licensed=t.payload,n;case c:return n.option=t.payload,n;case i.Ld:var a=t.payload.profile.value;return a.hasOwnProperty("media_files")&&(n.enabled=a.media_files.enabled,n.option=a.media_files.option,n.date=a.media_files.date,n.excludes=a.media_files.excludes,n.last_migration=a.media_files.last_migration),a.current_migration.hasOwnProperty("media_migration_option")&&!0===t.payload.profile.imported&&(n.option=null,n.enabled=!0),n;case u:var _=t.payload.available;return n.available=_,t.payload.hasOwnProperty("message")&&(n.message=t.payload.message),_||(n.enabled=!1),n;case d:return(0,r.Z)((0,r.Z)({},n),{},{excludes:t.payload});case m:return(0,r.Z)((0,r.Z)({},n),{},{date:t.payload});case f:return(0,r.Z)((0,r.Z)({},n),{},{last_migration:t.payload});case o.dN:return(0,r.Z)((0,r.Z)({},n),{},{available:"1"===t.payload.mf_available});default:return e}}))}},85562:function(e,t,n){"use strict";n.r(t);var r=n(62295),a=n(22497),i=n(47895),s=n(22633),o=n(27056);t.default=function(){return function(e,t){e((function(e,t){(0,r.dC)((function(){e((0,a.KG)("addonActions",(function(){}))),e((0,a.KG)("postConnectionPanels",(function(){e((0,s.qb)("multisite_tools"))})))}))})),e((function(e,t){(0,r.dC)((function(){e((0,a.KJ)("wpmdbBackupTables",(function(e,n){if("backup_selected"!==n)return e;var r=t(),a=r.multisite_tools,i=r.migrations,s=a.selected_subsite,l=i.current_migration,c=i.local_site,u=i.remote_site,p="push"===l.intent?u.site_details.prefix:c.this_prefix;if(0===s)return e;var d=t().migrations;return(0,o.yR)(s,p,d,"backup")}))),e((0,a.KJ)("addonPanels",(function(t,n){return e((0,i.C)(n))&&["savefile","find_replace","backup_local"].includes(n)&&t.push("multisite_tools"),t}))),e((0,a.KJ)("addonPanelsOpen",(function(e,t){return["savefile","find_replace","backup_local"].includes(t)&&e.push("multisite_tools"),e}))),e((0,a.KJ)("intiateMigrationPostData",(function(e){var n=t().multisite_tools;return n.enabled&&(e.mst_select_subsite="1"),n.selected_subsite>0&&(e.mst_selected_subsite=Number(n.selected_subsite),e.new_prefix=n.new_prefix),n.destination_subsite>0&&(e.mst_destination_subsite=Number(n.destination_subsite)),e}))),e((0,a.KJ)("wpmdb_standard_replace_values",(function(n){if(!t().multisite_tools.enabled)return n;var r=e((0,o.xU)());return n.domain.search=r.search,n.domain.replace=r.replace,n}))),e((0,a.KJ)("wpmdbPreMigrationCheck",(function(t){return e((0,o.Cj)(t))})))}))}))}}},76687:function(e,t,n){"use strict";function r(e,t,n){return"pull"===e&&t||"push"===e&&n}function a(e,t){return!!Object.keys(t).includes(e)&&{subsiteName:t[e],subsites:t}}function i(e,t){return Object.keys(t.subsites).includes(e)?t.subsites[e]:t.url}function s(e){var t=e.local_site,n=e.remote_site,r=e.current_migration.localSource;return{sourceSite:r?t:n,destinationSite:r?n:t}}n.d(t,{G2:function(){return s},sA:function(){return r},ty:function(){return i},wn:function(){return a}})},27056:function(e,t,n){"use strict";n.d(t,{Cj:function(){return x},Ox:function(){return g},Si:function(){return b},o:function(){return _},wg:function(){return w},xL:function(){return E},xU:function(){return h},yR:function(){return y}});var r=n(62295),a=n(42233),i=n(4669),s=n(65730),o=n(76687),l=n(47895),c=n(14251),u=n(29950),p=n(29942),d=n(66441),m=n(4516),f=n(66055),_=((0,a.__)("Multisite Tools addon","wp-migrate-db"),function(){return(0,a.__)("<b>Addon Missing</b> - The Multisite Tools addon is inactive on the <strong>remote site</strong>. Please install and activate it to enable Multisite Tools migration.","wp-migrate-db")}),g=function(e){return(0,f.m)(s.hA,{available:e})},b=function(e){return(0,f.m)(s.xv,e)},h=function(){return function(e,t){var n=t(),r=n.migrations,a=n.multisite_tools,i=a.selected_subsite,s=a.destination_subsite,o=a.enabled,l=r.local_site,c=r.remote_site,u=r.current_migration,d=u.twoMultisites,m=u.localSource,f=m?l:c,_=m?c:l,g="true"===f.site_details.is_multisite,b=g&&0!==i?"//"+f.subsites[i]:f.url,h=g?_.url:"//"+_.subsites[i];return d&&(b=0===i?f.url:"//"+f.subsites[i],h=0===s?_.url:"//"+_.subsites[s]),o||(b=f.url,h=_.url),{search:(0,p.Ph)(b),replace:(0,p.Ph)(h)}}},v=function(e,t){return function(n,a){if(0===e)return!1;var i=a().migrations,s=y(e,t,i,"migration");(0,r.dC)((function(){n((0,c.E2)(s)),n({type:u.mu,payload:"selected"})}))}},E=function(){return function(e,t){var n=t(),a=n.migrations,o=n.multisite_tools,l=(0,m.r5)("intent",t()),c=o.enabled,u=a.local_site,p=a.current_migration;(0,r.dC)((function(){e({type:s.pT,payload:!c,data:u,intent:l});var t=e(h());if(e((0,i.Kf)(t)),!c){var n=o.selected_subsite,r=["find_replace","savefile"].includes(l)?u.this_prefix:p.source_prefix;e(v(n,r))}}))}},w=function(e){e.selectedSubsite;var t=e.selectedSubsiteID,n=e.type;return function(e,r){var a=r().migrations,l=a.current_migration,c=a.local_site,u=l.intent;if(["push","pull"].includes(u)){var p=e(h());e((0,i.Kf)(p))}if(["savefile"].includes(u)&&"savefile"===u&&e({type:d.Dh,payload:!0}),e((function(e,t){var n=t(),r=n.multisite_tools,a=n.migrations,i=a.local_site,l=a.remote_site,c=a.current_migration,u=c.intent,p=c.twoMultisites?r.destination_subsite:r.selected_subsite,d=(0,o.sA)(u,"true"===i.is_multisite,"site_details"in l?"true"===l.site_details.is_multisite:void 0),m="push"===u?l.prefix:i.this_prefix;return p&&p>1&&(d||["find_replace","savefile"].includes(u))&&(m=m+p+"_"),e({type:s.GH,payload:m}),m})),"source"===n){var m=["find_replace","savefile"].includes(u)?c.this_prefix:l.source_prefix;e(v(t,m))}}},y=function(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"migration",a=n.local_site,i=n.remote_site,s=n.current_migration.intent,o=a.this_prefixed_tables,c=a.this_table_sizes_hr,u=i.prefixed_tables,p=i.table_sizes_hr,d="true"===a.is_multisite,m="site_details"in i&&"true"===i.site_details.is_multisite,f=(0,l.Lk)(s,{tables:o,tableSizes:c},{tables:u,tableSizes:p},r).tables,_=["".concat(t,"users"),"".concat(t,"usermeta")],g=["".concat(t,"blog_versions"),"".concat(t,"blogs"),"".concat(t,"blogmeta"),"".concat(t,"registration_log"),"".concat(t,"signups"),"".concat(t,"site"),"".concat(t,"sitemeta")],b=[];return f.forEach((function(n,a){if(_.includes(n))b.push(n);else if(!g.includes(n)){if("migration"===r){if("pull"===s&&!m||"push"===s&&!d)return void b.push(n)}else if("pull"===s&&!d||"push"===s&&!m)return void b.push(n);var i=1!==parseInt(e)?"^".concat(t+e,"_"):"^".concat(t,"(?![0-9]+_)");RegExp(i,"i").exec(n)&&b.push(n)}})),b},x=function(e){return function(t,n){var r=(0,m.r5)("intent",n()),a=(0,m.r5)("twoMultisites",n()),i=n().multisite_tools,s=i.enabled,o=i.selected_subsite,l=i.destination_subsite,c=i.new_prefix,u=i.is_licensed,p=i.available;if(!s||!u||!p)return e;var d=[];if(0===o){d.push({name:"MST_NO_SUBSITE",panel:"multisite_tools"})}if(a&&0===l){d.push({name:"MST_NO_DESTINATION",panel:"multisite_tools"})}if("savefile"===r){if(""===c){d.push({name:"MST_EMPTY_PREFIX",panel:"multisite_tools"})}if(!k(c)){d.push({name:"MST_INVALID_PREFIX",panel:"multisite_tools"})}}return d.length>0&&(e=e.concat(d)),e}},k=function(e){return null===new RegExp("[^a-z0-9_]","i").exec(e)}},65730:function(e,t,n){"use strict";n.d(t,{GH:function(){return p},LN:function(){return f},YW:function(){return l},hA:function(){return d},pT:function(){return u},w0:function(){return c},xv:function(){return m}});var r=n(18489),a=n(26429),i=n(29950),s=n(31998),o=n(9106),l="UPDATE_SELECTED_SUBSITE",c="UPDATE_DESTINATION_SUBSITE",u="MST_TOGGLE_ENABLED",p="MST_UPDATE_PREFIX",d="SET_MST_AVAILABLE",m="SET_MST_LICENSED",f="SET_MST_SUB_SINGLE",_={enabled:!1,available:!1,is_licensed:!1,selected_subsite:0,destination_subsite:0,new_prefix:"",message:""};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return n;case i.n_:case o.$:return n.available="1"===window.wpmdb_data.mst_available,n;case i.Ld:var a=t.payload.profile.value.multisite_tools;return n=(0,r.Z)({},a);case u:return n.enabled=t.payload,""===n.new_prefix&&(n.new_prefix=t.prefix),n;case l:return n.selected_subsite=t.payload,n;case c:return n.destination_subsite=t.payload,n;case p:return n.new_prefix=t.payload,n;case d:return n.message="",n;case m:return n.is_licensed=t.payload,n;case f:return n.enabled=!0,n;case s.dN:return(0,r.Z)((0,r.Z)({},n),{},{available:"1"===t.payload.mst_available});default:return e}}))}},63708:function(e,t,n){"use strict";n.d(t,{n:function(){return a},p:function(){return r}});var r=function(e){var t=e.theme_plugin_files;return"undefined"!==typeof t.available&&!t.available},a=function(e,t){if(!e)return"none";if(e&&t)switch(i(e,t)){case-1:return"down";case 0:return"equal";default:return"up"}return"add"},i=function(e,t){if(e===t)return 0;for(var n=e.split("."),r=t.split("."),a=n.length>=r.length?n.length:r.length,i=0;i<a;i++){var s=void 0!==n[i]?parseInt(n[i]):0,o=void 0!==r[i]?parseInt(r[i]):0;if(s>o)return 1;if(s<o)return-1}return 0}},18213:function(e,t,n){"use strict";n.r(t);var r=n(27166),a=n(33032),i=n(22497),s=n(53273);t.default=function(){return function(e,t){e((function(e,t){})),e((function(e,t){e((0,i.KJ)("wpmdbPreMigrationCheck",(function(t){return e((0,s.DP)(t))}))),e((0,i.KJ)("addMigrationStages",(function(t){return e((0,s.N9)(t))}))),e((0,i.KJ)("mdbAddonActions",function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a){var i,o,l,c,u,p,d,m,f,_,g,b,h,v,E,w,y,x,k,Z;return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(i=t(),o=i.theme_plugin_files,l=i.migrations,c=o.theme_files,u=o.plugin_files,p=o.muplugin_files,d=o.other_files,m=o.core_files,f=c?c.enabled:null,_=u?u.enabled:null,g=p?p.enabled:null,b=d?d.enabled:null,h=m?m.enabled:null,v="pull"===l.current_migration.intent?l.remote_site.site_details:l.local_site.site_details,E=Object.keys(v.themes).length>0,w=Object.keys(v.plugins).length>0,y=Object.keys(v.muplugins).length>0,x=Object.keys(v.others).length>0,k=Object.keys(v.core).length>0,Z=window.hasOwnProperty("wpmdbmf")&&t().media_files.enabled,!(f&&E||_&&w||g&&y||b&&x||h&&k)){n.next=19;break}if(window.hasOwnProperty("wpmdbmf")&&(!window.hasOwnProperty("wpmdbmf")||Z)){n.next=19;break}return n.next=18,e((0,s.G_)());case 18:return n.abrupt("return",n.sent);case 19:return n.abrupt("return",a);case 20:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()))}))}}},53273:function(e,t,n){"use strict";n.d(t,{BI:function(){return y},DP:function(){return P},G_:function(){return A},KX:function(){return S},LD:function(){return h},N9:function(){return O},OL:function(){return w},RX:function(){return T},Vj:function(){return E},WZ:function(){return k},pM:function(){return Z},q2:function(){return R},zk:function(){return x}});var r=n(27166),a=n(33032),i=n(31125),s=n(42233),o=n(51645),l=n(66055),c=n(3460),u=n(29816),p=n(4516),d=n(66866),m=n(42714),f=n(10304),_=n(98135),g=n(73264),b=n(29942),h=((0,s.__)("Theme & Plugin Files addon","wp-migrate-db"),"TPF"),v={theme_files:"themes",plugin_files:"plugins",muplugin_files:"muplugins",other_files:"others",core_files:"core"},E=function(){return(0,s.__)("<b>Addon Missing</b> - The Theme & Plugin Files addon is inactive on the <strong>remote site</strong>. Please install and activate it to enable Theme & Plugin Files migration.","wp-migrate-db")},w=function(e){return function(t){t({type:o._5,payload:e})}},y=function(e,t){return function(n){n({type:o.bY,payload:{available:e,message:t}})}},x=function(e){return function(t){t({type:o.lJ,payload:e})}},k=function(e,t){var n={themes:o.fe,plugins:o.Xr,muplugins:o.sW,others:o.Md,core:o.TO};return(0,l.m)(n[t],e)},Z=function(e,t){var n={themes:o.rR,plugins:o.P,muplugins:o.EJ,others:o.ND,core:o.xE};return(0,l.m)(n[t],e)},T=function(e,t){var n={themes:o.HA,plugins:o.CM};return(0,l.m)(n[t],e)},S=function(e,t){var n={themes:o.Ni,plugins:o.W3,muplugins:o.vs,others:o.qR,core:o.u8};return(0,l.m)(n[t],e)},N=function(e,t){var n=(0,i.Z)(t),r={name:{themes:"SELECTED_THEMES_EMPTY",plugins:"SELECTED_PLUGINS_EMPTY",muplugins:"SELECTED_MUPLUGINS_EMPTY",others:"SELECTED_OTHERS_EMPTY",core:"SELECTED_CORE_EMPTY"}[e],panel:{themes:"theme_files",plugins:"plugin_files",muplugins:"muplugin_files",others:"other_files",core:"core_files"}[e]};return n.push(r),n};function P(e){return function(t,n){var r=n().theme_plugin_files,a=r.theme_files,i=r.themes_selected,s=r.themes_excluded,o=r.themes_option,l=r.plugin_files,c=r.plugins_selected,u=r.plugins_excluded,p=r.plugins_option,d=r.muplugin_files,m=r.muplugins_selected,f=r.muplugins_option,_=r.other_files,g=r.others_selected,b=r.others_option,h=r.core_files,v=r.core_selected,E=r.core_option;return a.enabled&&"selected"===o&&0===i.length&&(e=N("themes",e)),a.enabled&&"except"===o&&0===s.length&&(e=N("themes",e)),l.enabled&&"selected"===p&&0===c.length&&(e=N("plugins",e)),l.enabled&&"except"===p&&0===u.length&&(e=N("plugins",e)),d&&d.enabled&&"selected"===f&&0===m.length&&(e=N("muplugins",e)),_&&_.enabled&&"selected"===b&&0===g.length&&(e=N("others",e)),h&&h.enabled&&"selected"===E&&0===v.length&&(e=N("core",e)),e}}function O(e){return function(t,n){var r=n(),a=r.theme_plugin_files,i=r.migrations,s=i.local_site,o=i.remote_site,l=i.current_migration,c=l.intent,u=a.theme_files,p=a.plugin_files,d=a.muplugin_files,m=a.other_files,f=a.core_files,_="pull"===c?o.site_details:s.site_details,g="savefile"===c;return u&&u.enabled&&Object.keys(_.themes).length>0&&(!0===g||"true"===(0,b.NR)("theme_files",l,s,o))&&e.push("theme_files"),p&&p.enabled&&Object.keys(_.plugins).length>0&&(!0===g||"true"===(0,b.NR)("plugin_files",l,s,o))&&e.push("plugin_files"),d&&d.enabled&&Object.keys(_.muplugins).length>0&&(!0===g||"true"===(0,b.NR)("muplugin_files",l,s,o))&&e.push("muplugin_files"),m&&m.enabled&&Object.keys(_.others).length>0&&(!0===g||"true"===(0,b.NR)("other_files",l,s,o))&&e.push("other_files"),f&&f.enabled&&e.push("core_files"),e}}var A=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,i,s;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=t(D()),i=n().theme_plugin_files,s=(0,p.r5)("intent",n()),["push","pull","savefile"].includes(s)){e.next=7;break}return e.abrupt("return",!1);case 7:if(i.theme_files.enabled||i.plugin_files.enabled||i.muplugin_files.enabled||i.other_files.enabled||i.core_files.enabled){e.next=9;break}return e.abrupt("return",!1);case 9:return e.next=11,t((0,c.Z6)("ADDONS_STAGE",[{fn:R,args:[a]}]));case 11:return e.abrupt("return",!0);case 12:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},C=function(e,t){var n=e.theme_plugin_files,r=(0,p.r5)("intent",e),a="push"===r||"savefile"===r?e.migrations.local_site.site_details[t]:e.migrations.remote_site.site_details[t],i=[];if("all"===n["".concat(t,"_option")])return Object.values(a).forEach((function(e){"savefile"!==r&&e[0].path.includes("wp-migrate-db")||i.push(e[0].path)})),i;if("active"===n["".concat(t,"_option")])return Object.values(a).forEach((function(e){e[0].active&&i.push(e[0].path)})),i;if("selected"===n["".concat(t,"_option")])return n["".concat(t,"_selected")];if("except"===n["".concat(t,"_option")]){var s=n["".concat(t,"_excluded")];return Object.values(a).forEach((function(e){s.includes(e[0].path)||i.push(e[0].path)})),i}},R=function e(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(){var i=(0,a.Z)((0,r.Z)().mark((function a(i,o){var l,u,d,m,_,b,h,E,w,y,x,k,Z,T,S,N,P,O,A,R,D,M,F;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return n||i((0,g.F)(t)),l=(0,p.r5)("migration_id",o()),u=(0,p.r5)("stages",o()),d=o().theme_plugin_files,m=C(o(),"plugins"),_=C(o(),"muplugins"),b=C(o(),"themes"),h=C(o(),"others"),E=C(o(),"core"),w=C(o(),v[t]),y=d.excludes,x=d.themes_option,k=d.plugins_option,Z=d.muplugins_option,T=d.others_option,S=d.core_option,N=d.plugins_excludes,P=d.muplugins_excludes,O=d.themes_excludes,A=d.others_excludes,R=d.core_excludes,D={stage:v[t],stages:u,theme_folders:JSON.stringify(b),plugin_folders:JSON.stringify(m),muplugin_folders:JSON.stringify(_),other_folders:JSON.stringify(h),core_folders:JSON.stringify(E),themes_option:x,plugins_option:k,muplugins_option:Z,others_option:T,core_option:S,folders:JSON.stringify(w),migration_state_id:l,excludes:JSON.stringify(y),plugins_excludes:JSON.stringify(N),muplugins_excludes:JSON.stringify(P),themes_excludes:JSON.stringify(O),others_excludes:JSON.stringify(A),core_excludes:JSON.stringify(R),is_cli_migration:0},r.next=14,i((0,f.A)("/tpf-initiate-file-migration",D));case 14:if(M=r.sent){r.next=17;break}return r.abrupt("return");case 17:if(F={theme_files:(0,s.__)("themes","wp-migrate-db"),plugin_files:(0,s.__)("plugins","wp-migrate-db"),muplugin_files:(0,s.__)("must-use plugins","wp-migrate-db"),other_files:(0,s.__)("others","wp-migrate-db"),core_files:(0,s.__)("core","wp-migrate-db")},!M.data.recursive_queue){r.next=22;break}return r.next=21,i((0,g.T)(t,M,e,F[t]));case 21:return r.abrupt("return",r.sent);case 22:return r.next=24,i((0,c.Z6)("ADDONS_STAGE",[{fn:I,args:[t,M]}]));case 24:case"end":return r.stop()}}),a)})));return function(e,t){return i.apply(this,arguments)}}()},I=function e(t,n){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return function(){var n=(0,a.Z)((0,r.Z)().mark((function n(a,s){var o,g,b,h,E,w,y,x,k,Z,T;return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return a((0,l.m)(d.V$,window.wpmdbtp_settings.strings.loading_transfer_queue)),o=(0,p.r5)("migration_id",s()),g={stage:v[t],offset:i,migration_state_id:o},n.next=5,a((0,f.A)("/tpf-get-queue-items",g));case 5:if(b=n.sent){n.next=8;break}return n.abrupt("return");case 8:if("complete"!==(h=b.data).status){n.next=20;break}return E=C(s(),"themes"),w=C(s(),"plugins"),y=C(s(),"muplugins"),x=C(s(),"others"),k=C(s(),"core"),Z=C(s(),v[t]),T={currentStage:t,stage:v[t],migration_state_id:o,theme_folders:JSON.stringify(E),plugin_folders:JSON.stringify(w),muplugin_folders:JSON.stringify(y),other_folders:JSON.stringify(x),core_folders:JSON.stringify(k),folders:JSON.stringify(Z)},n.next=19,a((0,c.Z6)("ADDONS_STAGE",[{fn:u.e5,args:[t,"/tpf-transfer-files",T]}]));case 19:return n.abrupt("return");case 20:if(h.hasOwnProperty("queue_status")){n.next=23;break}return a((0,_.Qc)()),n.abrupt("return",!1);case 23:return a((0,m.I6)(h.queue_status.size/1e3)),n.next=26,a((0,c.Z6)("ADDONS_STAGE",[{fn:e,args:[t,b]}]));case 26:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()},D=function(){return function(e,t){var n=(0,p.r5)("stages",t()),r=["theme_files","plugin_files","muplugin_files","other_files","core_files"];return n.find((function(e){return r.includes(e)}))}}},51645:function(e,t,n){"use strict";n.d(t,{CM:function(){return y},EJ:function(){return h},HA:function(){return w},Md:function(){return f},ND:function(){return v},Ni:function(){return x},P:function(){return b},TO:function(){return _},W3:function(){return k},Xr:function(){return d},_5:function(){return l},bY:function(){return c},fe:function(){return p},lJ:function(){return u},qR:function(){return T},rR:function(){return g},sW:function(){return m},u8:function(){return S},vs:function(){return Z},xE:function(){return E}});var r=n(18489),a=n(26429),i=n(29950),s=n(9106),o=n(31998),l="TOGGLE_THEME_PLUGIN_FILES",c="SET_TPF_AVAILABLE",u="SET_TPF_LICENSED",p="UPDATE_THEMES_OPTION",d="UPDATE_PLUGINS_OPTION",m="UPDATE_MUPLUGINS_OPTION",f="UPDATE_OTHERS_OPTION",_="UPDATE_CORE_OPTION",g="UPDATE_SELECTED_THEMES",b="UPDATE_SELECTED_PLUGINS",h="UPDATE_SELECTED_MUPLUGINS",v="UPDATE_SELECTED_OTHERS",E="UPDATE_SELECTED_CORE",w="UPDATE_EXCLUDED_THEMES",y="UPDATE_EXCLUDED_PLUGINS",x="UPDATE_THEMES_EXCLUDES",k="UPDATE_PLUGINS_EXCLUDES",Z="UPDATE_MUPLUGINS_EXCLUDES",T="UPDATE_OTHERS_EXCLUDES",S="UPDATE_CORE_EXCLUDES",N={available:!1,is_licensed:!1,message:"",theme_files:{enabled:!1},themes_option:"all",themes_selected:[],themes_excluded:[],themes_excludes:".DS_Store\n.git\nnode_modules",plugin_files:{enabled:!1},plugins_option:"all",plugins_selected:[],plugins_excluded:[],plugins_excludes:".DS_Store\n.git\nnode_modules",muplugin_files:{enabled:!1},muplugins_option:"selected",muplugins_selected:[],muplugins_excludes:".DS_Store\n.git\nnode_modules",other_files:{enabled:!1},others_option:"selected",others_selected:[],others_excludes:".DS_Store\n.git\nnode_modules",core_files:{enabled:!1},core_option:"all",core_selected:[],core_excludes:".DS_Store\n.git\nnode_modules",state:{status:""}};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:N,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return n;case s.$:return(0,r.Z)((0,r.Z)({},n),{},{available:1===window.wpmdb_data.tpf_available,message:""});case o.Pi:return(0,r.Z)((0,r.Z)({},n),{},{state:(0,r.Z)((0,r.Z)({},n.state),{},{status:o.Pi})});case o.wd:return(0,r.Z)((0,r.Z)({},n),{},{state:(0,r.Z)((0,r.Z)({},n.state),{},{status:o.wd})});case l:return n[t.payload].enabled=!n[t.payload].enabled,n;case i.Ld:return t.payload.profile.value.theme_plugin_files;case u:return n.is_licensed=t.payload,n;case c:var a=t.payload.available;return n.available=a,t.payload.hasOwnProperty("message")&&(n.message=t.payload.message),a||(n.theme_files.enabled=!1,n.plugin_files.enabled=!1,n.muplugin_files.enabled=!1,n.other_files.enabled=!1,n.core_files.enabled=!1),n;case g:return n.themes_selected=t.payload,n;case b:return n.plugins_selected=t.payload,n;case h:return n.muplugins_selected=t.payload,n;case v:return n.others_selected=t.payload,n;case E:return n.core_selected=t.payload,n;case w:return n.themes_excluded=t.payload,n;case y:return n.plugins_excluded=t.payload,n;case p:return n.themes_option=t.payload,n;case d:return n.plugins_option=t.payload,n;case m:return n.muplugins_option=t.payload,n;case f:return n.others_option=t.payload,n;case _:return n.core_option=t.payload,n;case k:return(0,r.Z)((0,r.Z)({},n),{},{plugins_excludes:t.payload});case Z:return(0,r.Z)((0,r.Z)({},n),{},{muplugins_excludes:t.payload});case x:return(0,r.Z)((0,r.Z)({},n),{},{themes_excludes:t.payload});case T:return(0,r.Z)((0,r.Z)({},n),{},{others_excludes:t.payload});case S:return(0,r.Z)((0,r.Z)({},n),{},{core_excludes:t.payload});case o.dN:return(0,r.Z)((0,r.Z)({},n),{},{available:"1"===t.payload.tpf_available});default:return e}}))}},73264:function(e,t,n){"use strict";n.d(t,{F:function(){return d},T:function(){return m}});var r=n(27166),a=n(33032),i=n(42233),s=n(66055),o=n(29950),l=n(66866),c=n(82174),u=n(42714),p=n(3460),d=function(e){return function(t){t((0,s.m)(o.f7,e)),t((0,s.m)(l.V$,(0,i.gB)((0,i.__)("Starting file transfer of %s files...","wp-migrate-db"),c.r[e]))),t((0,u.v_)())}},m=function(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:(0,i.__)("media library","wp-migrate-db");return function(){var c=(0,a.Z)((0,r.Z)().mark((function a(c){return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return c((0,s.m)(l.V$,(0,i.gB)((0,i.__)("Scanning %s - %s files found...","wp-migrate-db"),o,parseInt(t.data.items_count||0).toLocaleString()))),r.next=3,c((0,p.Z6)("ADDONS_STAGE",[{fn:n,args:[e,!0]}]));case 3:return r.abrupt("return",r.sent);case 4:case"end":return r.stop()}}),a)})));return function(e){return c.apply(this,arguments)}}()}},98135:function(e,t,n){"use strict";n.d(t,{Qc:function(){return o},SF:function(){return s},g6:function(){return l}});var r=n(42233),a=(n(98487),n(4516),n(42714),n(62457)),i=n(66866),s=function(e,t,n,r,a,i,s,o){var l=e.target.tagName.toLowerCase();return!a&&("input"!==l&&"label"!==l&&(n?s(t):r?i(t):(i(t),o()),!1))},o=function(){return function(e){e((0,a.m)({error_type:i.gF,error_message:(0,r.__)("Queue status not returned from remote.","wp-migrate-db")}))}},l=function(e,t,n,a,i){return e?arguments.length>5&&void 0!==arguments[5]&&arguments[5]?(0,r.__)("stepping up failed","wp-migrate-db"):null===t||n||a?null!==t&&n&&!a?(0,r.__)("stepping down","wp-migrate-db"):null!==t&&!n&&a?(0,r.__)("maintaining","wp-migrate-db"):null!==t&&i?(0,r.__)("max reached","wp-migrate-db"):(0,r.__)("initializing","wp-migrate-db"):(0,r.__)("stepping up","wp-migrate-db"):(0,r.__)("static","wp-migrate-db")}},29816:function(e,t,n){"use strict";n.d(t,{AG:function(){return h},e5:function(){return v}});var r=n(27166),a=n(18489),i=n(33032),s=n(42233),o=n(4516),l=n(10304),c=n(3460),u=n(53273),p=n(66055),d=n(66866),m=n(29950),f=n(42714),_=n(82174),g=n(29942),b=n(98135),h=["media_files","theme_files","plugin_files","muplugin_files","other_files","core_files"];function v(e,t,n){var u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,h=arguments.length>4&&void 0!==arguments[4]&&arguments[4],w=arguments.length>5&&void 0!==arguments[5]&&arguments[5],y=arguments.length>6&&void 0!==arguments[6]?arguments[6]:0,x=arguments.length>7&&void 0!==arguments[7]&&arguments[7];return function(){var k=(0,i.Z)((0,r.Z)().mark((function i(k,Z){var T,S,N,P,O,A,C,R,I,D,M,F,L,B,U;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return T=!0,null!==u||x||(u=(0,o.r5)("currentMaxPayloadSize",Z())),!1===x&&null!==u&&y>=5&&(u=null,x=!0,w=!1,h=!1),k((0,p.m)(d.V$,(0,s.gB)((0,s.__)("Migrating %s files..."),_.r[e]))),k((0,p.m)(m.b4)),S=T&&y<=5,r.next=8,k((0,l.A)(t,(0,a.Z)((0,a.Z)({},n),{},{payloadSize:u,stabilizePayloadSize:h,stepDownSize:w,retries:y,forceHighPerformanceTransfers:!0}),S));case 8:if(N=r.sent,P=!1,N){r.next=16;break}if(!S){r.next=15;break}P=!0,r.next=16;break;case 15:case 20:return r.abrupt("return");case 16:if(O=N.data,!N||!N.data||"complete"!==N.data.status){r.next=21;break}return r.next=20,k((0,c.Z6)("ADDONS_STAGE",[{fn:E,args:[e,N.data.status]}]));case 21:if(R=u,P||(!0!==N.success&&T?P=!0:(I=Object.keys(O.status),D=O.status.error,M=O.status[I[0]].total_transferred,F=O.status,L=F.current_payload_size,B=F.reached_max_payload_size,U=F.fallback_payload_size,R=L,A=w||B||h,C=M/1e3,!M||D?P=!0:(k((0,p.m)(m.sr,M)),k((0,p.m)(m.IJ,M)),k((0,p.m)(m.X4,null!==L&&void 0!==L?L:U))),setTimeout((function(){var t=(0,o.xg)("timer",Z());k((0,p.m)(m.NL,{currentStage:e,status:(0,b.g6)(T,u,w,h,B,P),maxPayloadSize:(0,g.lL)(T?null!==L&&void 0!==L?L:0:U),currRequestSize:(0,g.lL)(null!==M&&void 0!==M?M:0),time:Math.floor(t.time/1e3)}))}),0))),!0!==P||!T){r.next=27;break}return r.next=26,k((0,c.Z6)("ADDONS_STAGE",[{fn:v,args:[e,t,n,R,!1,!0,y+1,x]}]));case 26:case 30:return r.abrupt("return",r.sent);case 27:return C&&k((0,f.gF)(C)),r.next=30,k((0,c.Z6)("ADDONS_STAGE",[{fn:v,args:[e,t,n,R,A,!1,0,x]}]));case 31:case"end":return r.stop()}}),i)})));return function(e,t){return k.apply(this,arguments)}}()}function E(e,t){return function(){var n=(0,i.Z)((0,r.Z)().mark((function n(a,i){var s,l,d,f;return(0,r.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(s=(0,o.r5)("stages",i()),a((0,p.m)(m.sP,e)),!h.includes(e)){n.next=12;break}if(l=h.indexOf(e),d=h.slice(l+1),!(f=d.find((function(e){return s.includes(e)})))){n.next=10;break}return n.next=9,a((0,c.Z6)("ADDONS_STAGE",[{fn:u.q2,args:[f]}]));case 9:return n.abrupt("return",n.sent);case 10:if("complete"!==t){n.next=12;break}return n.abrupt("return",a((0,c.Z6)(c.ly)));case 12:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}},10304:function(e,t,n){"use strict";n.d(t,{A:function(){return l}});var r=n(27166),a=n(33032),i=n(29942),s=n(62457),o=n(66866),l=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function(){var l=(0,a.Z)((0,r.Z)().mark((function a(l){var c;return(0,r.Z)().wrap((function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.op)(e,t);case 3:c=r.sent,r.next=10;break;case 6:return r.prev=6,r.t0=r.catch(0),n||(l((0,s.m)({error_type:o.gF,error_message:r.t0})),(0,i.Tc)(r.t0)),r.abrupt("return",!1);case 10:if(c.success){r.next=13;break}return n||(l((0,s.m)({error_type:o.gF,error_message:c.data})),l((0,s.U)())),r.abrupt("return",!1);case 13:return r.abrupt("return",c);case 14:case"end":return r.stop()}}),a,null,[[0,6]])})));return function(e){return l.apply(this,arguments)}}()}},82174:function(e,t,n){"use strict";n.d(t,{r:function(){return a}});var r=n(42233),a={theme_files:(0,r.__)("theme","wp-migrate-db"),plugin_files:(0,r.__)("plugin","wp-migrate-db"),muplugin_files:(0,r.__)("must-use plugin","wp-migrate-db"),media_files:(0,r.__)("media","wp-migrate-db"),other_files:(0,r.__)("other","wp-migrate-db"),core_files:(0,r.__)("WordPress core","wp-migrate-db")}},95402:function(e,t,n){"use strict";n.d(t,{F:function(){return a}});var r=n(29942),a=function(e){var t,n=e.remote_site,a=e.local_site;switch(e.intent){case"pull":t={domain:{search:(0,r.Ph)(n.url),replace:(0,r.Ph)(a.this_url)},path:{search:n.path,replace:a.this_path}};break;case"push":case"savefile":t={domain:{search:(0,r.Ph)(a.this_url),replace:(0,r.Ph)(n.url)},path:{search:a.this_path,replace:n.path}};break;case"import":t={domain:{search:(0,r.Ph)(n.URL),replace:(0,r.Ph)(a.this_url)},path:{search:n.path,replace:a.this_path}}}return t.domain.enabled=!0,t.path.enabled=!0,t}},34653:function(e,t,n){"use strict";n.r(t),n.d(t,{addonsLoaded:function(){return w},runAddonsStage:function(){return y}});var r=n(27166),a=n(33032),i=n(62295),s=n(65730),o=n(9106),l=n(27325),c=n(38906),u=n(66055),p=n(53273),d=n(78579),m=n(27056),f=n(29942),_=n(22497),g=function(e,t,n){return function(r){r(e(n,t))}},b=function(e,t){return function(t){var n=e.media_files,r=e.dbi_api_data;n&&(t(g(d.Um,(0,d.nv)(),"1"===e.migrations.local_site.mf_available)),(0,f.Yu)()&&!1===n.is_licensed&&t((0,d.AN)(E("wp-migrate-db-pro-media-files",r))))}},h=function(e,t){return function(t){var n=e.theme_plugin_files,r=e.dbi_api_data;n&&(t(g(p.BI,(0,p.Vj)(),"1"===e.migrations.local_site.tpf_available)),(0,f.Yu)()&&!1===n.is_licensed&&t((0,p.zk)(E("wp-migrate-db-pro-theme-plugin-files",r))))}},v=function(e){return function(t,n){var r=n(),a=r.profiles,i=r.migrations,o=r.multisite_tools,l=r.dbi_api_data,c=i.current_migration,p=i.local_site,d=c.intent;if(o){t((0,_.KJ)("wpmdb_post_connection_errors",(function(e){return(0,f.sk)(d,n())&&e.warning.push(p["mst_required_message_".concat(d)]),e}))),t(g(m.Ox,(0,m.o)(),e.mst_available)),(0,f.Yu)()&&!1===o.is_licensed&&t((0,m.Si)(E("wp-migrate-db-pro-multisite-tools",l)));var b=!c.twoMultisites&&("true"===p.is_multisite||e.site_details&&"true"===e.site_details.is_multisite);["push","pull"].includes(d)&&b&&null===a.current_profile&&t((0,u.m)(s.LN))}}},E=function(e,t){return!(!t.api_data||!t.api_data.addons_available_list)&&e in t.api_data.addons_available_list};function w(){return function(e,t){e({type:o.$});var n=t(),r=n.migrations,a=r.local_site,s=r.remote_site;(0,i.dC)((function(){e(b(n,s,a)),e(h(n,s,a)),e(v(s,a))}))}}function y(e,t){return function(){var t=(0,a.Z)((0,r.Z)().mark((function t(n,a){var i,s,o,u;return(0,r.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(i=(0,l.u)("delay_between_requests",a()),e.length&&(s=e[0].fn,o=e[0].args),!(i>0)){t.next=8;break}return t.next=5,(0,c.QK)((function(){return n((0,c.Am)(s,o))}),1e3*i);case 5:u=t.sent,t.next=11;break;case 8:return t.next=10,n((0,c.Am)(s,o));case 10:u=t.sent;case 11:return t.abrupt("return",u);case 12:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}},40882:function(e,t,n){"use strict";n.r(t),n.d(t,{betaOptionToggle:function(){return a}});var r=n(27325),a=function(){return function(e,t){(0,r.u)("beta_optin",t())||window.wpmdb_data.is_beta_plugins_installed&&window.confirm(window.wpmdb_strings.rollback_beta_to_stable)&&(window.location=window.wpmdb_data.rollback_to_stable_url)}}},34363:function(e,t,n){"use strict";n.r(t),n.d(t,{changeConnection:function(){return z},connectToRemote:function(){return j},copyLicenseToRemote:function(){return C},handleTableSelects:function(){return B},resetMigration:function(){return P},retryOverHTTP:function(){return I},setConnectedProfileName:function(){return M},setConnectionStatus:function(){return S},setConnectionStatusBatch:function(){return N},setError:function(){return A},shouldShowSSLNotice:function(){return D},updateConnectionState:function(){return O},updatePluginOnRemote:function(){return R}});var r=n(88368),a=n(18489),i=n(27166),s=n(33032),o=n(12544),l=n.n(o),c=n(62295),u=n(42233),p=n(29942),d=function(e){var t=0,n=window.WPMDBStore.getState();if(0===e.length)return window.wpmdb_strings.connection_info_missing;var a=function(e){var t=e.split("\n"),n=e;if(1===t.length){var r=e.trim().split(" ");2===r.length&&(!1===window.wpmdb_data.openssl_available&&(r[0]=r[0].replace("https://","http://")),n=r[0]+"\n"+r[1])}return n}(e),i=a.split("\n"),s=(0,r.Z)(i,2),o=s[0],l=s[1];return(0,p.mQ)(o)?("undefined"!==typeof l&&(t=l.length),32!==t&&40!==t?window.wpmdb_strings.connection_info_key_invalid:o===window.wpmdb_data.connection_info[0]?window.wpmdb_strings.connection_info_local_url:l===n.settings.key?window.wpmdb_strings.connection_info_local_key:{url:o,key:l,str:a}):window.wpmdb_strings.connection_info_url_invalid},m=n(66055),f=n(61987),_=n(31998),g=n(58696),b=n(4516),h=n(14251),v=n(27114),E=n(29950),w=n(22633),y=n(34653),x=n(22497),k=n(47585),Z=n(39881);var T=function(e){e.connectionState;var t=e.result,n=void 0===t?null:t,r=e.intent,o=void 0===r?null:r;return function(e,t){(0,c.dC)((function(){var r=D(t()),l=(0,a.Z)((0,a.Z)({},k.A),{},{button_status:"hidden"});r&&(l.ssl_notice=!0),e(N(l)),e(B((0,b._P)("this_tables",t()),n.data.tables,(0,b.r5)(["intent","tables_selected","backup_option","backup_tables_selected"],t()))),e((0,h.I8)(!0)),!1!==e((0,Z.G9)(n,!0,o))&&(e(M()),e((function(e,t){var n=["database","save_profile","theme_plugin_files"];null===(0,b.r5)("selected_existing_profile",t())&&n.push("custom_fields","standard_fields","multisite_tools"),(0,g.C)("status",t()).ssl_notice&&n.push("connect"),e((0,x.O)("postConnectionPanelsOpen",n)),(0,c.dC)((function(){e((0,w.G7)(n)),e((0,x.Kw)("postConnectionPanels")),e((0,w.qb)("database","connect"))}))})),e(function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t){var n;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n=[],window.hasOwnProperty("wpmdbmf")&&n.push("media_files"),window.hasOwnProperty("wpmdbtp")&&n.push("theme_plugin_files"),n.length>0&&(t((0,w.nW)(n)),t((0,y.addonsLoaded)()));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()))}))}},S=function(e,t){return(0,m.m)(f.i6,{key:e,statusVal:t})},N=function(e){return function(t){t((0,m.m)(f.N_,e))}},P=function(e){return(0,m.m)(_.rK,e)},O=function(e){return(0,m.m)(f.iv,e)},A=function(e){return(0,m.m)(f.i6,{key:"error",statusVal:e})},C=function(){return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,a;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=n(),a=r.migrations.connection_info.connection_state,e.next=4,(0,p.op)("/copy-license-to-remote",{url:a.url,key:a.key});case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},R=function(e){return function(){var t=(0,s.Z)((0,i.Z)().mark((function t(n,r){var a,s,o;return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=r(),s=a.migrations.connection_info.connection_state,t.prev=2,t.next=5,(0,p.op)("/update-plugin-on-remote",{url:s.url,key:s.key,slug:e});case 5:o=t.sent,t.next=15;break;case 8:if(t.prev=8,t.t0=t.catch(2),"AbortError"!==t.t0.name){t.next=13;break}return console.log("fetch aborted",t.t0),t.abrupt("return");case 13:return n(U(t.t0.message)),t.abrupt("return",!1);case 15:return t.abrupt("return",o);case 16:case"end":return t.stop()}}),t,null,[[2,8]])})));return function(e,n){return t.apply(this,arguments)}}()},I=function(){return function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){var r,s,o;return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r=n(),s=(0,a.Z)({},r.migrations.connection_info.connection_state),o=r.migrations.current_migration.intent,s.url=s.url.replace("https","http"),s.value=s.value.replace("https","http"),t(S("retry_over_http",!0)),t(O(s)),t(j(o));case 8:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()},D=function(e){return(0,g.C)("status",e).retry_over_http},M=function(){return function(e,t){var n=(0,b.r5)("intent",t()),r=(0,b._P)("url",t());l()(["push","pull"],n)&&(r=(0,b.FY)("url",t()));var a=r.replace(/(^\w+:|^)\/\//,""),i="push"===n?(0,u.gB)((0,u.__)("Push to %s","wp-migrate-db"),a):(0,u.gB)((0,u.__)("Pull from %s","wp-migrate-db"),a);e((0,h.fJ)(i))}},F=function(e){return function(t){var n=new AbortController;return t((0,m.m)("SET_ABORT_CONTROLLER",{key:e,controller:n})),n}},L=function(e){return function(t){(0,c.dC)((function(){t(N({connecting:!0,button_status:"disabled"})),t(O((0,a.Z)({},e)))}))}},B=function(e,t,n){var a=(0,r.Z)(n,4),i=a[0],s=a[1],o=a[2],l=a[3];return function(n,r){var a=e,c=t;"pull"===i&&(a=t,c=e);var u=a.filter((function(e){return s.includes(e)}));if(u.length!==s.length&&n({type:E.KU,payload:u}),"backup_manual_select"===o){var p=c.filter((function(e){return l.includes(e)}));p.length!==l.length&&n({type:E.O$,payload:p})}}},U=function(e){return function(t,n){var r,a=(0,v.Y)(e);t((r=a,function(){var e=(0,s.Z)((0,i.Z)().mark((function e(t,n){return(0,i.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(D(n())&&t(S("ssl_notice",!0)),!r.includes("#197")){e.next=5;break}return t(I()),e.abrupt("return");case 5:r.includes("401 Unauthorized")&&t(N({show_auth_form:!0})),r.includes("#195")&&t(S("copy_to_remote",!0)),r.includes("#196")&&t(S("update_plugin_on_remote",!0)),t(S("error_msg",r));case 9:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}())),(0,g.C)("status",n()).retry_over_http?t(N({retry_over_http:!1,ssl_notice:!0})):t((function(e,t){e((0,m.m)(_.rK,["connect"])),e(N({error:!0,button_status:"",connecting:!1}))}))}},j=function(e){return function(){var t=(0,s.Z)((0,i.Z)().mark((function t(n,r){var a,s,o,l;return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return a=(0,b.NR)("connection_info",r()),s=a.connection_state,o=n(F("connect_to_remote")),n(L(s)),t.prev=3,t.next=6,(0,p.op)("/verify-connection",{url:s.url,key:s.key,intent:e},o);case 6:l=t.sent,t.next=16;break;case 9:if(t.prev=9,t.t0=t.catch(3),"AbortError"!==t.t0.name){t.next=14;break}return console.log("fetch aborted",t.t0),t.abrupt("return");case 14:return n(U(t.t0.message)),t.abrupt("return",!1);case 16:if(l.success){t.next=19;break}return n(U(l)),t.abrupt("return");case 19:return n(T({connectionState:s,result:l,intent:e})),t.abrupt("return",l);case 21:case"end":return t.stop()}}),t,null,[[3,9]])})));return function(e,n){return t.apply(this,arguments)}}()},z=function(e){return function(){var t=(0,s.Z)((0,i.Z)().mark((function t(n,r){var s,o,l,u,p,m,f;return(0,i.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(s=r(),o=s.migrations.current_migration.intent,l=s.migrations.connection_info.status.pasted,n(N({error:!1,button_status:""})),u=e.connection_state,p=d(e),m=(0,a.Z)((0,a.Z)({},u),{},{value:p.str,url:p.url,key:p.key}),"string"!==typeof p){t.next=11;break}return f=(0,a.Z)((0,a.Z)({},u),{},{value:e}),(0,c.dC)((function(){n(O(f)),n(N({error:!0,button_status:"disabled",error_msg:p}))})),t.abrupt("return",p);case 11:n(O(m)),l&&(0,c.dC)((function(){n(j(o,!0)),n(S("pasted",!1))}));case 13:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}()}},39881:function(e,t,n){"use strict";n.d(t,{G9:function(){return u}});var r=n(95402),a=n(22497),i=n(4669),s=n(14251),o=n(66055),l=n(29942),c=n(29950);function u(e,t,n){return function(u,p){var d=p();u({type:"UPDATE_REMOTE_SITE",payload:e.data});var m=(0,r.F)({intent:n,remote_site:e.data,local_site:d.migrations.local_site});m=u((0,a.O)("wpmdb_standard_replace_values",m)),u((0,i.mX)(m)),u((0,s.uI)("push"===n));var f="pull"===n?e.data.prefix:d.migrations.local_site.this_prefix,_="pull"===n?d.migrations.local_site.this_prefix:e.data.prefix;return u((0,s.OZ)(f,_)),["push","pull"].includes(n)&&(u((0,s.nX)("true"===e.data.site_details.is_multisite&&"true"===d.migrations.local_site.is_multisite)),u((0,o.m)(c.W$,{local_site_mode:void 0!==d.settings.high_performance_transfers?d.settings.high_performance_transfers:d.migrations.local_site.site_details.high_performance_transfers,remote_site_mode:e.data.site_details.high_performance_transfers}))),!!t&&(u(function(e){return function(t){t({type:c.tX,connected:e})}}(t)),u(function(e,t){return function(n,r){r().migrations.local_site.this_prefix!==e.data.prefix&&n((0,o.m)("SET_CONNECTION_STATUS",{key:"prefix_mismatch",statusVal:!0})),(0,l.SC)(t,r())&&n((0,s.ED)())}}(e,n)))}}},44789:function(e,t,n){"use strict";n.r(t),n.d(t,{TrackMigrationComplete:function(){return l},TrackMigrationStart:function(){return o}});var r=n(27166),a=n(33032),i=n(29942),s=n(4516),o=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,o,l,c,u;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=n().settings,o=a.isPro,l=a.allow_tracking,c=l&&o,u=(0,s.r5)("migration_id",n()),c){e.next=5;break}return e.abrupt("return",!1);case 5:return e.prev=5,e.next=8,(0,i.op)("/log-migration",{migration_id:u,complete:!1});case 8:e.next=13;break;case 10:e.prev=10,e.t0=e.catch(5),console.error(e.t0.message);case 13:case"end":return e.stop()}}),e,null,[[5,10]])})));return function(t,n){return e.apply(this,arguments)}}()},l=function(){return function(){var e=(0,a.Z)((0,r.Z)().mark((function e(t,n){var a,i,o,l,c,u;return(0,r.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=n().settings,i=a.isPro,o=a.allow_tracking,o&&i){e.next=4;break}return e.abrupt("return",!1);case 4:if(l=(0,s.r5)("migration_id",n()),c=(0,s.r5)("intent",n()),(u=new FormData).append("action","wpmdb_track_migration_complete"),u.append("nonce",window.wpmdb_data.nonces.flush),u.append("migration_id",l),u.append("intent",c),!["push","pull"].includes(c)){e.next=13;break}return e.abrupt("return");case 13:return e.prev=13,e.next=16,fetch(window.ajaxurl,{method:"POST",body:u});case 16:e.next=21;break;case 18:e.prev=18,e.t0=e.catch(13),console.error(e.t0.message);case 21:case"end":return e.stop()}}),e,null,[[13,18]])})));return function(t,n){return e.apply(this,arguments)}}()}},9106:function(e,t,n){"use strict";n.d(t,{$:function(){return i}});var r=n(26429),a={media_files:window.hasOwnProperty("wpmdbmf"),theme_plugin_files:window.hasOwnProperty("wpmdbtp"),multisite_tools:window.hasOwnProperty("wpmdbmst")},i="ADDONS_LOADED";t.Z=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a,t=arguments.length>1?arguments[1]:void 0;return(0,r.ZP)(e,(function(n){return"RESET_APP"===t.type?n:e}))}},61987:function(e,t,n){"use strict";n.d(t,{MS:function(){return u},N_:function(){return l},i6:function(){return o},iv:function(){return s}});var r=n(18489),a=n(52089),i=n(47585),s="UPDATE_CONNECTION_INFO",o="SET_CONNECTION_STATUS",l="SET_CONNECTION_STATUS_BATCH",c={connection_state:[],status:i.A},u=(0,a.Lq)(c,{RESET_MIGRATION:function(e,t){return(0,r.Z)((0,r.Z)({},c),{},{connection_state:e.connection_state,status:e.status})},SET_CONNECTION_STATUS:function(e,t){var n=t.payload,r=n.statusVal,a=n.key;return e.status[a]=r,e},SET_CONNECTION_STATUS_BATCH:function(e,t){return e.status=(0,r.Z)((0,r.Z)({},e.status),t.payload),e},SET_MIGRATION_CONNECTED:function(e,t){return e.status.connecting=!1,e},UPDATE_CONNECTION_INFO:function(e,t){return e.connection_state=t.payload,e},LOAD_PROFILE:function(e,t){var n=t.payload.profile.value.connection_info;return(0,r.Z)((0,r.Z)({},n),{},{status:(0,r.Z)({},i.A)})}})},59299:function(e,t,n){"use strict";n.d(t,{I:function(){return p},Ih:function(){return o},_K:function(){return i},aJ:function(){return u},eD:function(){return s},qi:function(){return l},x_:function(){return c},zB:function(){return d}});var r=n(18489),a=n(26429),i="SET_API_DATA",s="UPDATE_LICENSE_ERRORS",o="SET_LICENSE_STATUS",l="SET_LICENSE_SAVED",c="SET_LICENCE_UI_STATUS",u="SET_LICENSE_CHECK_CONTEXT",p="SET_API_TIME",d="SET_DBI_DOWN_STATUS",m={display_name:"",user_email:"",license_name:"",licence:{licence_status:window.wpmdb_data.licence_status,licence_ui_status:"",check_again:!1,error_message:"",status:""},license_saved:!1,license_check_context:"ui",license_errors:window.wpmdb_data.license_errors,license_status:{},license_messages:{},license_constant:!1,api_time:0,api_data:window.wpmdb_data.api_data,dbi_down_status:!1};t.ZP=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){var a=t.payload;switch(t.type){case i:return(0,r.Z)((0,r.Z)({},n),{},{api_data:a});case c:return(0,r.Z)((0,r.Z)({},n),{},{licence:(0,r.Z)((0,r.Z)({},n.licence),{},{licence_ui_status:a.value})});case"LICENSE_REMOVED":return(0,r.Z)((0,r.Z)({},n),{},{licence:(0,r.Z)((0,r.Z)({},n.licence),{},{licence_status:null})});case s:return(0,r.Z)((0,r.Z)({},n),{},{license_errors:a});case l:return(0,r.Z)((0,r.Z)({},n),{},{license_saved:a});case"CLEAR_LICENSE_ERRORS":return(0,r.Z)((0,r.Z)({},n),{},{license_errors:[],license_status:{}});case o:return(0,r.Z)((0,r.Z)({},n),{},{licence:(0,r.Z)((0,r.Z)({},n.licence),{},{licence_status:t.payload})});case u:return(0,r.Z)((0,r.Z)({},n),{},{license_check_context:t.payload});case p:return(0,r.Z)((0,r.Z)({},n),{},{api_time:t.payload});case"SET_LICENSE_MESSAGES":return(0,r.Z)((0,r.Z)({},n),{},{license_messages:(0,r.Z)((0,r.Z)({},n.license_messages),t.payload),api_data:(0,r.Z)((0,r.Z)({},n.api_data),{},{errors:t.payload})});case"OVERWRITE_LICENCE_STATUS":return(0,r.Z)((0,r.Z)({},n),{},{licence:(0,r.Z)((0,r.Z)({},n.licence),a)});case d:return(0,r.Z)((0,r.Z)({},n),{},{dbi_down_status:a});default:return e}}))}},26547:function(e,t,n){"use strict";n.d(t,{Dx:function(){return l},EL:function(){return p},Ej:function(){return s},Qy:function(){return m},UL:function(){return f},VG:function(){return c},WH:function(){return d},ci:function(){return o},yQ:function(){return u}});var r=n(18489),a=n(26429),i={status:"",upload_file:"",error:{},file_uploaded:!1,file_size:0,table_sizes:{},table_rows:{},tables:[],file:void 0},s="UPDATE_IMPORT_STATUS",o="SET_IMPORT_DATA",l="SET_UPLOAD_FILE",c="SET_IMPORT_TABLE_DATA",u="RESET_IMPORT_DATA",p="IMPORT_ERROR",d="SET_IMPORT_TABLES",m="SET_IMPORT_FILE",f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i,t=arguments.length>1?arguments[1]:void 0;return(0,a.ZP)(e,(function(n){switch(t.type){case"RESET_APP":return i;case s:return(0,r.Z)((0,r.Z)({},n),{},{status:t.payload});case l:return(0,r.Z)((0,r.Z)({},n),{},{upload_file:t.payload});case p:return(0,r.Z)((0,r.Z)({},n),{},{error:t.payload});case o:var a=t.payload,f=a.file_uploaded,_=a.upload_file,g=a.file_size;return(0,r.Z)((0,r.Z)({},n),{},{upload_file:_,file_uploaded:f,file_size:g});case c:var b=t.payload,h=b.table_sizes,v=b.table_rows,E=b.tables;return(0,r.Z)((0,r.Z)({},n),{},{table_sizes:h,table_rows:v,tables:E});case d:return(0,r.Z)((0,r.Z)({},n),{},{tables:t.payload});case m:return(0,r.Z)((0,r.Z)({},n),{},{file:t.payload});case u:return i;default:return e}}))}},31998:function(e,t,n){"use strict";n.d(t,{Pi:function(){return p},dN:function(){return m},rK:function(){return u},wd:function(){return d}});var r=n(18489),a=n(20188),i=n(29950),s=n(66441),o=n(61987),l=n(66866),c=n(26547),u="RESET_MIGRATION",p="MIGRATION_STARTED",d="WPMDB_PRE_MIGRATION",m="UPDATE_LOCAL_SITE",f={local_site:window.wpmdb_data,remote_site:{}};t.ZP=(0,a.UY)({local_site:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.local_site,t=arguments.length>1?arguments[1]:void 0,n=(0,r.Z)({},e);if(t.type===m){return["wpmdbmf","wpmdbmst","wpmdbtp"].forEach((function(e){t.payload[e]&&"1"===t.payload[e]?window[e]={enabled:"1"}:window[e]=void 0})),Object.keys(t.payload).forEach((function(e){window.wpmdb_data[e]=t.payload[e]})),(0,r.Z)((0,r.Z)({},e),t.payload)}return n},remote_site:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.remote_site,t=arguments.length>1?arguments[1]:void 0;(0,r.Z)({},e);switch(t.type){case u:return{};case"UPDATE_REMOTE_SITE":return(0,r.Z)({},e),t.payload;default:return e}},connection_info:o.MS,current_migration:i.S_,migration_progress:l.D$,search_replace:s.jC,import_data:c.UL})},67821:function(e,t,n){"use strict";n.r(t),n.d(t,{selectFromImportData:function(){return i}});var r=n(4516),a=function(e){return e.migrations.import_data};function i(e,t){return(0,r.fX)(a,"import_data",e,t)}},90534:function(e){e.exports="data:image/png;base64,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"},50247:function(){}},function(e){e.O(0,[532,288],(function(){return t=86177,e(e.s=t);var t}));e.O()}]);