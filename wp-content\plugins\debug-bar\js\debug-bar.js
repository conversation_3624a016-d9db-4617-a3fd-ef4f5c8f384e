var wpDebugBar;!function(e){var i;wpDebugBar=i={body:void 0,init:function(){i.body=e(document.body),i.toggle.init(),i.tabs(),i.actions.init()},isVisible:function(){return i.body.hasClass("debug-bar-visible")},toggle:{init:function(){e("#wp-admin-bar-debug-bar").click(function(t){if(t.preventDefault(),t.target.hash){var s=e(t.target.rel);if(i.isVisible()){if(s.hasClass("current"))return s.removeClass("current"),void i.toggle.visibility()}else i.toggle.visibility();e(".debug-menu-target").hide().trigger("debug-bar-hide"),e(".debug-menu-link").removeClass("current"),s.addClass("current"),e(t.target.hash).show().trigger("debug-bar-show")}else i.toggle.visibility()})},visibility:function(t){t=void 0===t?!i.isVisible():t,i.body.toggleClass("debug-bar-visible",t),e(this).toggleClass("active",t)}},tabs:function(){var i=e(".debug-menu-link"),t=e(".debug-menu-target");i.click(function(s){var r=e(this);s.preventDefault(),r.hasClass("current")||(t.hide().trigger("debug-bar-hide"),i.removeClass("current"),r.addClass("current"),e("#"+this.href.substr(this.href.indexOf("#")+1)).show().trigger("debug-bar-show"))})},actions:{init:function(){var t=e("#debug-bar-actions");e(document).keydown(function(e){var t=e.key||e.which||e.keyCode;27===t&&i.isVisible()&&(e.preventDefault(),i.actions.close())}),e(".maximize",t).click(i.actions.maximize),e(".restore",t).click(i.actions.restore),e(".close",t).click(i.actions.close)},maximize:function(){i.body.removeClass("debug-bar-partial"),i.body.addClass("debug-bar-maximized")},restore:function(){i.body.removeClass("debug-bar-maximized"),i.body.addClass("debug-bar-partial")},close:function(){i.toggle.visibility(!1)}}},e(wpDebugBar.init)}(jQuery);