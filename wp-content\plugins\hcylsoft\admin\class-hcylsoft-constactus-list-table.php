<?php
if (!class_exists('WP_List_Table')) {
    require_once ABSPATH . 'wp-admin/includes/class-wp-list-table.php';
}
class  Hcyl_Constact_List_Table extends WP_List_Table {

    function __construct() {
        parent::__construct(array(
            'singular' => 'item', // 唯一标识符，单数
            'plural'   => 'items', // 唯一标识符，复数
            'ajax'     => false // 是否启用AJAX
        ));


    }

    public static function sendMsg()
    {
        global $wpdb;
        $sql = "select * from {$wpdb->prefix}customer_info where is_send_qiwei = %d";

        $result =  $wpdb->get_results($wpdb ->prepare($sql,0),ARRAY_A);

        if(count($result) > 0){

            foreach ($result as $res) {
                try {
                    $staff_phone = get_option('site_support_staff','');
                    if($staff_phone == ''){
                        continue;
                    }
                    self::qiweiRobbot($res);
                }catch (Exception $e){
                    error_log('发送企微消息出错: ' . $e->getMessage());
                }

            }


        }


    }

    public static function qiweiRobbot($res)
    {
        $company_name = $res['company_name'];
        $phone = $res['phone'];
        $user_name = $res['user_name'];
        $staff_phone = get_option('site_support_staff','');

        $botUrl =  get_option('site_support_staff_bot','');
        if($botUrl == ''){
            error_log('企微机器人配置不存在');
            return;
        }

        $content = "#### 官网来访客户提醒\n
            >公司名称: <font color=\"warning\">{$company_name}</font>
             >客户姓名: <font color=\"warning\">{$user_name}</font>
             >客户手机号: <font color=\"warning\">{$phone}</font>";

        // 定义要发送的消息数据
        $data = array(
            'msgtype' => 'markdown',
            'markdown' => array(
                'content' => $content,
            ),
        );

        $data2 = array(
            'msgtype' => 'text',
            'text' => array(
                'content' => "请相关同事尽快跟进回访。",
                "mentioned_mobile_list" => array($staff_phone)
            ),
        );

        // 企业微信群聊机器人的 Webhook 地址
        $webhook_url = $botUrl;

        // 发送 HTTP POST 请求
        $response = wp_remote_post($webhook_url, array(
            'headers'   => array(
                'Content-Type' => 'application/json',
            ),
            'body'      => json_encode($data),
        ));

        wp_remote_post($webhook_url, array(
            'headers'   => array(
                'Content-Type' => 'application/json',
            ),
            'body'      => json_encode($data2),
        ));

        // 检查是否成功发送消息
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log("发送消息失败：$error_message");
        } else {
            $response_code = wp_remote_retrieve_response_code($response);
            $result = json_decode($response['body']);
            if ($response_code === 200) {
                if($result->errcode == 0){
                    global $wpdb;
                   $num = $wpdb->update("{$wpdb->prefix}customer_info", array(
                            "is_send_qiwei"=>1,
                            "updated" => date("Y-m-d H:i:s",time())
                    ),array(
                            'id'=>$res['id']
                    ));
                    error_log("消息发送成功！".$response);
                    error_log("更新成功发送状态！".$res['id']);
                }else {
                    error_log("消息发送成功！".$response);
                }
            } else {
                // 未能成功发送消息
                error_log("消息发送失败，响应码：$response_code");
            }
        }

    }

    /**
	 * @global string $status
	 * @return array
	 */
	public function get_columns() {
        return array(
            'cb'        => '<input type="checkbox" />',
            'company_name'  => '公司名称',
            'phone' => '联系电话',
            'user_name' => '客户姓名',
            'is_contact' => '是否回访',
            'is_send_qiwei' => '是否发送企微消息',
            'created' => '创建时间',
            'updated' => '更新时间'
        );
	}

    public function get_bulk_actions() {
        $actions = array(
            'delete' => '删除',
            'updateContact' => '更新回访状态'
        );
        return $actions;
    }

    public function process_bulk_action() {
        // 检查当前的操作是否是删除操作
        if ( 'delete' === $this->current_action() ) {
            // 获取选中的项目
            $post_ids = isset( $_POST['post'] ) ? $_POST['post'] : array();
            $tableName = 'customer_info';
            // 遍历并删除选中的项目
            foreach ( $post_ids as $post_id ) {
                global $wpdb;
                $result = $wpdb->delete( "{$wpdb->prefix}$tableName", array( 'id' => $post_id ) );
            }
        }else if( 'updateContact' === $this->current_action()) {
            $post_ids = isset( $_POST['post'] ) ? $_POST['post'] : array();
            $tableName = 'customer_info';
            // 遍历并删除选中的项目
            foreach ( $post_ids as $post_id ) {
                global $wpdb;
                $result = $wpdb->update( "{$wpdb->prefix}$tableName", array(
                        'is_contact' => 1
                ) ,array( 'id' => $post_id ) );
                if ( ! $result ) {
                    return false;
                }else {
                    return true;
                }

            }

        }
    }

    // 添加自定义过滤器表单
    function extra_tablenav( $which ) {
        if ( $which == "top" ) {
            // 定义过滤选项
            ?>
            <div class="alignleft actions">
                    <input type="text" name="user_name" placeholder="客户名称" value="<?php
                    if(isset($_POST['user_name']) && $_POST['user_name'] != '')
                            echo  $_POST['user_name'];
                    ?>">
                    <input type="text" name="company_name" placeholder="公司名称" value="<?php
                    if(isset($_POST['company_name']) && $_POST['company_name'] != '')
                        echo  $_POST['company_name'];
                    ?>">
                    <input type="submit" class="button button-primary" value="查询">
                    <input type="reset" class="button" value="重置">
            </div>
<?php
        }
    }

    // 数据查询
    function prepare_items() {
        global $wpdb;

        $tableName = 'customer_info';

        $columns  = $this->get_columns();
        $hidden   = array();
        $sortable = $this->get_sortable_columns();

        $this->_column_headers = array($columns, $hidden, $sortable);


        $per_page     = 20;
        $current_page = $this->get_pagenum();

        $params = array();

        $sql = "SELECT * FROM {$wpdb->prefix}{$tableName} where 1 = 1";
        $sqlCount = "SELECT count(*) FROM {$wpdb->prefix}{$tableName} where 1 = 1";
        if(isset($_POST['user_name']) && $_POST['user_name'] != '' ){
            $sql .= " and user_name like  %s ";
            $sqlCount .= " and user_name like  %s ";
            $params[] = '%'.esc_sql($_POST['user_name']).'%';
        }
        if(isset($_POST['company_name']) && $_POST['company_name'] != ''){
           $sql .= " and company_name like  %s ";
           $sqlCount .= " and user_name like  %s ";
           $params[] = '%'.esc_sql($_POST['company_name']).'%';
        }
        if(count($params) == 0) {
            $total_items  = $wpdb->get_var($sqlCount);
        }else {
            $total_items  = $wpdb->get_var($wpdb -> prepare($sqlCount,$params));
        }
//
        $this->set_pagination_args(array(
            'total_items' => $total_items,
            'per_page'    => $per_page
        ));
        $params[] = $per_page;
        $params[] = ($current_page - 1) * $per_page;
        $this->items = $wpdb->get_results($wpdb->prepare(
            $sql." ORDER BY created DESC LIMIT %d OFFSET %d",
            $params
        ), ARRAY_A);
    }

    function display_rows() {
        foreach ($this->items as $item) {
            echo '<tr>';
            foreach ($this->get_columns() as $column_name => $column_display_name) {
               if($column_name == 'cb'){
                   echo '<th scope="row" class="check-column"><input type="checkbox"  name="post[]" value="' . esc_attr( $item['id'] ) . '" /></th>';
               }else if($column_name == 'is_contact'){
                   echo '<td>' . ($item[$column_name] == 0? "待回访" : "已回访" ) . '</td>';
               }else if($column_name == 'is_send_qiwei') {
                   echo '<td>' . ($item[$column_name] == 0 ? "待发送" : "已发送")  . '</td>';
               }else {
                   echo '<td>'.$item[$column_name].'</td>';
               }
            }
            echo '<tr/>';
        }
    }

    // 获取可排序的列
    function get_sortable_columns() {
        $sortable_columns = array(
            'created'      => array( 'date', false )
        );
        return $sortable_columns;
    }




    public function no_items() {
        echo '无数据';
    }


    public static function add()
    {
        header('Content-Type: application/json; charset=utf-8');
        $result = [];
        global $wpdb;
        if(!isset($_POST['cpname'])){
            $result['code'] = 500;
            $result['msg'] = "公司名称不能为空!";
            echo json_encode($result);
            wp_die();
        }
        if(!isset($_POST['name'])){
            $result['code'] = 500;
            $result['msg'] = "姓名不能为空!";
            echo  json_encode($result) ;
            wp_die();
        }
        if(!isset($_POST['phone'])) {
            $result['code'] = 500;
            $result['msg'] = "手机号不能为空!";
            echo json_encode($result);
            wp_die();
        }else {
            $pattern = '/^(?:(?:\+|00)86)?1[3-9]\d{9}$/';
            if (!preg_match($pattern, $_POST['phone'])){
                $result['code'] = 500;
                $result['msg'] = "手机号不合法!";
                echo json_encode($result);
                wp_die();
            }
        }
        $tableName = 'customer_info';
        $flag  = $wpdb->insert("{$wpdb->prefix}$tableName",array(
            'company_name'  => esc_sql($_POST['cpname']),
            'phone' => esc_sql($_POST['phone']),
            'is_contact' => 0,
            'is_send_qiwei' => 0,
            'user_name' => esc_sql($_POST['name']),
            'created' => date("Y-m-d H:i:s",time())
        ) );
        if($flag != false){ //插入成功
            $result = array(
                'code' => 200,
                'msg' => '操作成功'
            );
            echo json_encode($result);
        }else{
            $result = array(
                    'code' => 500,
                    'msg' => '操作失败'
            );
            echo json_encode($result);
        }
        wp_die();
    }



}



