<?php

/**
 * Fired during plugin activation
 *
 * @link       http://example.com
 * @since      1.0.0
 *
 * @package    Hcylsoft
 * @subpackage Hcylsoft/includes
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.0.0
 * @package    Hcylsoft
 * @subpackage Hcylsoft/includes
 * <AUTHOR> Name <<EMAIL>>
 */
class Hcylsoft_Activator {

	/**
	 * Short Description. (use period)
	 *
	 * Long Description.
	 *
	 * @since    1.0.0
	 */
	public static function activate() {

        if (!wp_next_scheduled('my_plugin_cron_job')) {
            wp_schedule_event(time(), 'weekly', 'my_plugin_cron_job',array(),true);
        }

    }

   public static function  my_custom_cron_schedules( $schedules ) {
        $schedules['every_ten_minutes'] = array(
            'interval' => 60, // 每10分钟（以秒为单位）
            'display'  => '每分钟'
        );
        return $schedules;
    }

    public static function my_plugin_cron_function()
    {

        require_once plugin_dir_path(plugin_dir_path( __FILE__ ))  . 'admin/class-hcylsoft-constactus-list-table.php';

        Hcyl_Constact_List_Table::sendMsg();
    }






}
