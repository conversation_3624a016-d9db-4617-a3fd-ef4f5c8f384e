<?php

namespace DeliciousBrains\WPMDB\Container\Invoker;

use DeliciousBrains\WPMDB\Container\Invoker\Exception\InvocationException;
use DeliciousBrains\WPMDB\Container\Invoker\Exception\NotCallableException;
use DeliciousBrains\WPMDB\Container\Invoker\Exception\NotEnoughParametersException;
/**
 * Invoke a callable.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface InvokerInterface
{
    /**
     * Call the given function using the given parameters.
     *
     * @param callable $callable   Function to call.
     * @param array    $parameters Parameters to use.
     *
     * @return mixed Result of the function.
     *
     * @throws InvocationException Base exception class for all the sub-exceptions below.
     * @throws NotCallableException
     * @throws NotEnoughParametersException
     */
    public function call($callable, array $parameters = array());
}
