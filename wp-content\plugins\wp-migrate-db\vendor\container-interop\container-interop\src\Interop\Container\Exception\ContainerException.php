<?php

/**
 * @license http://www.opensource.org/licenses/mit-license.php MIT (see the LICENSE file)
 */
namespace DeliciousBrains\WPMDB\Container\Interop\Container\Exception;

use DeliciousBrains\WPMDB\Container\Psr\Container\ContainerExceptionInterface as PsrContainerException;
/**
 * Base interface representing a generic exception in a container.
 */
interface ContainerException extends PsrContainerException
{
}
