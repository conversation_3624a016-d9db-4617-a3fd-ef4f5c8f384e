"use strict";(self.webpackJSONPwpmdb=self.webpackJSONPwpmdb||[]).push([[513],{76513:function(e,t,n){n.r(t),n.d(t,{default:function(){return L}});var c=n(88368),r=n(4665),a=n(7354),i=n(62295),s=n(42233),u=n(19085),o=n(27325),l=n(27166),f=n(33032),p=n(66055),m=(n(19826),n(59299)),d=n(68424),_=n(29942),v=n(87326),b=n(27114),x=n(4516),k=function(e){return e.dbi_api_data};function g(e,t){return(0,x.fX)(k,"settings",e,t)}var h=n(31998);function w(e){return function(){var t=(0,f.Z)((0,l.Z)().mark((function t(n){return(0,l.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,n((0,_.wM)({preRequest:(0,i.dC)((function(){n((0,v.c9)("licence_action",!0)),n((0,v.rT)("licence"))})),asyncFn:e,requestFailed:function(e){var t,c;n((t=e,c="licence_action",function(e){return e((0,v.nE)("licence",(0,s.__)("API error: ")+(0,b.Y)(t))),e((0,v.c9)(c,!1)),!1}))},requestSuccess:function(e){n((0,v.c9)("licence_action",!1))}}));case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}function Z(e,t){return function(n){var r=e.data,a=r.errors;if(a){if(n((0,v.c9)(t,!1)),Object.keys(a).length>0){var i=Object.keys(a),s=(0,c.Z)(i,1)[0];r.hasOwnProperty("licence_status")&&(s=r.licence_status),n(N(s))}n((0,p.m)(m.eD,a))}else n(N("active_licence"))}}function E(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:w,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ui";return function(){var c=(0,f.Z)((0,l.Z)().mark((function c(r,a){var s,u,f,v,b,x;return(0,l.Z)().wrap((function(c){for(;;)switch(c.prev=c.next){case 0:if(s=(0,o.u)("licence",a()),u=g("api_time",a()),f=Date.now()-u,v=g("license_check_context",a()),36e5,t||!(f<36e5)||n!==v){c.next=7;break}return c.abrupt("return");case 7:return c.next=9,r(e((0,_.op)("/check-license",{licence:s,context:"all",message_context:n},!1,r)));case 9:if(b=c.sent){c.next=12;break}return c.abrupt("return",null);case 12:return x=b.data,r(Z(b,"check_licence")),(0,i.dC)((function(){r((0,p.m)(m._K,b.data)),r((0,p.m)(m.I,Date.now())),r((0,p.m)(m.aJ,n)),r((function(e){(0,_.op)("/local-site-details").then((function(t){t.success&&e((0,p.m)(h.dN,t.data))}))})),t&&r((0,d.Ax)())})),c.abrupt("return",x);case 16:case"end":return c.stop()}}),c)})));return function(e,t){return c.apply(this,arguments)}}()}function y(e,t){if(!t)return null;var n=t.data,c=n.errors,r=n.error_type;return e(Z(t,"licence_action")),"undefined"!==typeof t.data.dbrains_api_down?(e((0,p.m)(m.zB,!0)),e((0,d.Ax)()),e(E())):e((0,p.m)(m.zB,!1)),!(c&&!Object.keys(c).includes("subscription_expired"))&&(1===Number(t.data.is_first_activation)&&"subscription_expired"!==r&&e((0,p.m)(m.x_,"first_activation")),"subscription_expired"!==r&&e((0,p.m)(m.qi,!0)),t.success&&t.data&&(0,i.dC)((0,f.Z)((0,l.Z)().mark((function n(){return(0,l.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:"subscription_expired"!==r&&e(N("active_licence")),e((0,d.Ax)()),e((0,v.m7)("masked_licence",t.data.masked_licence)),e((0,p.m)(m._K,t.data)),"subscription_expired"!==r&&e((0,p.m)(m.eD,[])),e(E());case 6:case"end":return n.stop()}}),n)})))),setTimeout((function(){e((0,p.m)(m.qi,!1))}),2500),t)}function N(e){return function(){var t=(0,f.Z)((0,l.Z)().mark((function t(n){return(0,l.Z)().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",n((0,p.m)(m.Ih,e)));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}var S=(0,i.$j)((function(e){return{settingsStatus:(0,o.u)("status",e)}}),{checkLicenceAgain:function(e,t){return function(){var n=(0,f.Z)((0,l.Z)().mark((function n(c,r){var a,i;return(0,l.Z)().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return c((0,v.c9)("check_again",!0)),a=w,e&&!t&&(a=function(){return t=e,function(){var e=(0,f.Z)((0,l.Z)().mark((function e(n){var c;return(0,l.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n(w((0,_.op)("/activate-license",{licence_key:t,context:"all",message_context:"settings"},!1,n)));case 2:return c=e.sent,e.abrupt("return",y(n,c));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();var t}),n.next=5,c(E(a,!0));case 5:return i=n.sent,c((0,v.c9)("check_again","success")),n.abrupt("return",i);case 8:case"end":return n.stop()}}),n)})));return function(e,t){return n.apply(this,arguments)}}()}})((function(e){var t,n,c=!1,a=e.settingsStatus,i=e.settings;return i&&(t=i.licence,n=i.masked_licence),a.check_again&&(c=!0===a.check_again),r.createElement(r.Fragment,null,r.createElement("div",{className:"flex-container licence-action"},r.createElement("a",{onClick:function(){e.checkLicenceAgain(t,n)}},(0,s.__)("Check my license again","wp-migrate-db")),r.createElement("div",{className:"relative"},c&&r.createElement(u.Q,{className:"license-notification-spinner"}))))})),C=n(46415),O=function(e){var t=e.settings,n=t.status,c=t.errors,a=n.disable_ssl;return r.createElement(r.Fragment,null,r.createElement("div",{className:"flex-container licence-action"},r.createElement("button",{className:"btn-tooltip-stroke",onClick:function(){return e.disableSSL()}},(0,s.gB)((0,s.__)("Temporarily disable SSL for connections to %s","wp-migrate-db"),"api.deliciousbrains.com")),r.createElement(C.OP,{position:!1,condition:a,errorMsg:c.disable_ssl,spinnerCond:a&&!0===a})))},A=(0,i.$j)((function(e){return{settingsStatus:(0,o.u)("status",e)}}),{reactivateLicense:function(){return function(){var e=(0,f.Z)((0,l.Z)().mark((function e(t){var n,c;return(0,l.Z)().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t((0,v.c9)("reactivate_license",!0)),e.next=3,t(w((0,_.op)("/reactivate-license",{context:"all",message_context:"settings"},!1,t)));case 3:return n=e.sent,c=y(t,n),t((0,v.c9)("reactivate_license","success")),t(E(w,!0)),e.abrupt("return",c);case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()}})((function(e){var t=!1,n=e.settingsStatus,c=e.className,a=e.btnText;return n.reactivate_license&&(t=!0===n.reactivate_license),r.createElement(r.Fragment,null,r.createElement("div",{className:"flex-container licence-action"},r.createElement("a",{onClick:function(){e.reactivateLicense()},className:c||""},a||(0,s.__)("Reactivate license","wp-migrate-db")),r.createElement("div",{className:"relative"},t&&r.createElement(u.Q,{className:"license-notification-spinner"}))))})),L=function(e){var t=(0,i.v9)((function(e){return e.dbi_api_data.licence})),n=t.license_ui_status,s=t.licence_status,u=(0,r.useState)(null),o=(0,c.Z)(u,2),l=o[0],f=o[1],p=["subscription_expired","licence_not_found","no_activations_left"];return(0,r.useEffect)((function(){(0,a.includes)(p,s)?f(r.createElement(S,e)):"activation_deactivated"===s&&f(r.createElement(A,e))}),[]),l||(""===n?null:"check_again"===n?r.createElement(S,e):"connection_failed"===n?r.createElement(O,e):null)}}}]);