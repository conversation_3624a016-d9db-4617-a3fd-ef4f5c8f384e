<?php
return ['x-generator'=>'GlotPress/4.0.0-beta.2','translation-revision-date'=>'2023-10-27 11:30:15+0000','plural-forms'=>'nplurals=1; plural=0;','project-id-version'=>'Plugins - W3 Total Cache - Stable (latest release)','language'=>'zh_CN','messages'=>['Varnish Cache'=>'Varnish 缓存','Behavior'=>'行为','Application'=>'应用','Rest API'=>'静态应用程序接口','Empty CloudFlare Cache'=>'清空 CloudFlare 缓存','Empty Varnish Cache'=>'清空 Varnish 缓存','Empty CDN Cache'=>'清空 CDN 缓存','Empty Database Cache'=>'清空数据库缓存','Empty Browser Cache'=>'清空浏览器缓存','Advanced Settings'=>'高级设置','Basic Settings'=>'基本设置','User Experience (UX) is a setting that focuses on enhancing the overall browsing experience for visitors
		of your website. By enabling this feature, you can optimize your website\'s performance by minimizing
		load times, reducing server requests, and delivering content more efficiently. This ultimately leads
		to faster page loading, improved user satisfaction, and increased engagement, resulting in a speedier
		and more enjoyable WordPress website.'=>'用户体验（UX）是一种设置，其重点是提升网站访问者的整体浏览体验。
		的整体浏览体验。通过启用此功能，您可以优化网站性能，最大限度地缩短
		加载时间、减少服务器请求并更高效地交付内容，从而优化网站性能。这最终会
		加快页面加载速度、提高用户满意度并增加参与度，从而创建一个更快
		更愉快的 WordPress 网站。','Cache Usage Statistics is a feature that provides valuable insights into the performance of your website\'s
		caching system. It displays detailed data regarding the utilization of your cache and overall performance
		metrics. By monitoring these statistics, website owners can identify areas of improvement, optimize their
		caching settings, and ultimately enhance the speed and responsiveness of their WordPress site, resulting
		in a smoother user experience for visitors.'=>'缓存使用情况统计是一项功能，可提供有关网站缓存系统性能的宝贵信息。
		缓存系统的性能。它显示有关缓存利用率和整体性能指标的详细数据。
		指标。通过监控这些统计数据，网站所有者可以确定需要改进的地方，优化他们的
		缓存设置，并最终提高 WordPress 网站的速度和响应能力，从而为访问者带来更流畅的用户体验。
		为访问者带来更流畅的用户体验。','Opcode cache is a powerful feature that enhances the performance of a WordPress website by caching 
			compiled PHP code. By storing pre-compiled code in memory, opcode cache eliminates the need for 
			repetitive interpretation and compilation of PHP files, resulting in significantly faster execution 
			times. Opcode cache reduces server load and improves response times, ultimately enhancing the 
			overall speed and responsiveness of your WordPress site. If opcode cache is available on the 
			hosting server it will automatically be selected in the dropdown and enabled.'=>'Opcode 缓存是一项强大的功能，可通过缓存
			编译的 PHP 代码，从而提高 WordPress 网站的性能。通过在内存中存储预编译代码，操作码缓存消除了对
			重复解释和编译 PHP 文件，从而大大加快了执行速度。
			时间。操作码缓存可减少服务器负载并改善响应时间，最终提高
			WordPress网站的整体速度和响应速度。如果
			托管服务器上可用，它将自动在下拉菜单中被选中并启用。','Google PageSpeed Insights can be used to analyze your homepage and provide an explanation of metrics and recommendations for improvements using W3 Total Cache features/extensions.  This tool is enabled by default but will not function until authorization is granted, which can be done on the %1$sGeneral Settings%2$s page.'=>'Google PageSpeed Insights 可用于分析您的主页，并提供指标解释和使用 W3 Total Cache 功能/扩展的改进建议。  该工具默认为启用状态，但在获得授权之前不会运行，可在 %1$sGeneral Settings%2$s 页面上进行授权。','Google PageSpeed Tool'=>'谷歌网页速度工具','%1$sImage Optimization%2$s enabled? %1$s%3$s%2$s'=>'已启用 %1$s 图像优化 %2$s ？%1$s%3$s%2$s','%1$sObject Cache%2$s is %1$sdisabled via filter%2$s'=>'通过过滤器 %2$s 启用了 %1$s 对象缓存 %2$s','Enable Image Service'=>'启用图像服务','Image Optimization'=>'图片优化','Minify Cache'=>'最小化缓存',' via filter'=>' 通过过滤器','This tool allows users to easily transfer their W3 Total Cache plugin settings between different 
					WordPress installations by exporting the current configuration as a file and importing it on 
					another site, ensuring consistent caching and performance optimizations across multiple websites.'=>'该工具允许用户在不同的 WordPress 安装之间轻松转移 W3 Total Cache 插件设置。
					WordPress 安装之间传输其 W3 Total Cache 插件设置。
					另一个网站，确保多个网站的缓存和性能优化保持一致。','PageSpeed Tool'=>'页面速度工具','The PageSpeed Tool is a powerful feature that can be used to help optimize and enhance the performance 
					of your WordPress website. By leveraging the insights and recommendations provided by Google\'s 
					PageSpeed Insights API, this tool analyzes your website\'s speed and suggests improvements to boost 
					its performance. By implementing the recommended optimizations, such as minimizing CSS and JavaScript, 
					optimizing images, and enabling browser caching, you can significantly accelerate your WordPress site, 
					resulting in faster loading times and an improved user experience.'=>'PageSpeed 工具是一项强大的功能，可用于帮助优化和增强 WordPress 网站的性能。
					您的 WordPress 网站的性能。通过利用 Google 的
					PageSpeed Insights API 提供的洞察和建议，该工具可分析网站速度并提出改进建议，以提高网站性能。
					性能。通过实施建议的优化措施，如最小化 CSS 和 JavaScript、
					优化图像并启用浏览器缓存，就能大大加快 WordPress 网站的速度、
					从而加快加载速度并改善用户体验。','Miscellaneous settings provide additional options and configurations to optimize and speed up 
					your WordPress website.'=>'杂项设置提供额外的选项和配置，以优化和加快
					您的 WordPress 网站。','The plugin license is a key that unlocks advanced features and support for the W3 Total Cache 
						WordPress plugin. By activating the license, users gain access to enhanced caching mechanisms, 
						optimization tools, enabling them to significantly speed up their 
						WordPress websites and improve overall performance.'=>'插件许可证是一个密钥，用于解锁高级功能和支持 W3 Total Cache
						WordPress 插件的高级功能和支持。激活许可证后，用户可以使用增强的缓存机制、
						优化工具，使他们能够显著加快其
						WordPress 网站并提高整体性能。','A reverse proxy is a server that sits between a client and a web server, acting as an intermediary 
					for requests. It retrieves resources on behalf of the client from the server, and then returns 
					the response to the client. By utilizing a reverse proxy, such as Varnish or Nginx, in conjunction 
					with W3 Total Cache, you can significantly enhance the performance of your WordPress website by 
					caching and serving static content directly from the reverse proxy server, reducing the load on 
					your WordPress server and improving response times for visitors.'=>'反向代理是位于客户端和网络服务器之间的服务器，作为中间人
					请求的中介。它代表客户端从服务器检索资源，然后将响应返回给客户端。
					响应给客户端。通过使用反向代理（如 Varnish 或 Nginx）与
					与 W3 Total Cache 结合使用，您可以通过以下方式显著提高 WordPress 网站的性能
					直接从反向代理服务器缓存和提供静态内容，从而大大提高 WordPress 网站的性能，减少 WordPress 服务器的负载，提高响应速度。
					您的 WordPress 服务器的负载，改善访问者的响应时间。','Enabling browser caching will instruct visitors\' web browsers to store static files from your WordPress 
					website, such as images, CSS, and JavaScript files, locally on their devices. By doing so, subsequent 
					visits to your site will retrieve these cached files from the browser\'s storage, reducing the need 
					for repeated downloads. This results in faster page loading times and a smoother browsing experience 
					for your visitors, ultimately improving the overall speed and performance of your WordPress website.'=>'启用浏览器缓存会指示访问者的网络浏览器将 WordPress 网站中的静态文件（如图片、CSS 和 JavaScript 文件）存储到他们的设备上。
					网站上的静态文件，如图片、CSS 和 JavaScript 文件，并将其本地存储在他们的设备上。这样，以后访问您网站时
					访问您的网站时将从浏览器的存储中检索这些缓存文件，从而减少重复下载的需要。
					重复下载的需要。这将加快页面加载时间，为访问者带来更流畅的浏览体验
					最终提高 WordPress 网站的整体速度和性能。','Object Cache is disabled via filter.'=>'通过过滤器禁用对象缓存。','Enable this option to utilize an object cache mechanism, which significantly enhances the performance 
					of your WordPress website. Object caching stores frequently accessed database queries and complex data 
					structures in memory, reducing the need to retrieve them from the database repeatedly. By doing so, it 
					minimizes the processing time required to generate dynamic content, resulting in faster page loading times 
					and improved overall site speed. Enabling object cache is particularly beneficial for websites with heavy 
					database usage or high traffic volumes, as it helps alleviate the strain on the server by efficiently 
					serving cached data.'=>'启用此选项可使用对象缓存机制，该机制可大大提高 WordPress 网站的性能。
					的性能。对象缓存可将经常访问的数据库查询和复杂的数据
					结构存储在内存中，从而减少了从数据库中反复检索的需要。通过这样做，它可以
					最大限度地减少生成动态内容所需的处理时间，从而加快页面加载时间，提高网站整体速度。
					并提高网站的整体速度。启用对象缓存对数据库使用量大或流量高的网站尤为有利。
					对数据库使用量大或流量高的网站尤其有利，因为它能通过有效地
					提供缓存数据，从而减轻服务器的压力。','Enable this setting to utilize the power of caching your WordPress site\'s database queries. 
					By storing frequently accessed database queries in memory, the database cache reduces the need for 
					repetitive database interactions, resulting in faster page load times. This feature is particularly 
					beneficial for websites with heavy database usage, improving overall performance and delivering a smoother 
					user experience.'=>'启用此设置可利用缓存 WordPress 网站数据库查询的功能。
					通过将经常访问的数据库查询存储在内存中，数据库缓存减少了对重复数据库交互的需求。
					重复数据库交互的需要，从而加快页面加载时间。这一功能尤其
					对数据库使用量大的网站尤其有利，它能提高网站的整体性能，提供更流畅的
					用户体验。','Minification is a technique used to reduce the file size of HTML, CSS, and JavaScript 
					files by removing unnecessary characters such as whitespace, comments, and line breaks. 
					This process can significantly improve the load times of web pages by reducing the amount 
					of data that needs to be downloaded by the user\'s browser.'=>'最小化是一种用于减小 HTML、CSS 和 JavaScript 文件大小的技术。
					文件的大小。
					这一过程可减少用户浏览器需要下载的数据量，从而大大提高网页的加载时间。
					用户浏览器需要下载的数据量，从而大大缩短网页的加载时间。','Page cache is a technique used to speed up the performance of a website by storing a copy of the 
					generated HTML page in the server\'s memory or disk, and then serving that copy to subsequent 
					visitors instead of generating the page from scratch each time. This can result in significant 
					speed improvements for websites with high traffic or dynamic content.'=>'页面缓存是一种用于加快网站性能的技术，它可以在服务器内存或磁盘中存储一个
					生成 HTML 页面的副本，然后将该副本提供给后续访问者，而不是每次都从头开始生成页面。
					而不是每次都从头开始生成页面。这可以大大提高
					对于流量大或有动态内容的网站，这可以大大提高速度。','Flush caches with '=>'使用刷新缓存 ','Info'=>'信息','W3 Edge'=>'W3 边缘','Follow Us'=>'关注我们','Learn more about Pro!'=>'了解更多专业信息！','WPML Extension'=>'WPML 扩展','Pro Features'=>'专业版功能','Premium Support Services'=>'高级支持服务','GitHub'=>'GitHub','Support Center'=>'支持中心','Documentation'=>'说明文档','Learn More'=>'了解更多','Investigate Compatibility Issue'=>'调查兼容性问题','Eliminate render-blocking Javascripts'=>'消除阻塞渲染的 Javascript','Hosting Environment Troubleshooting'=>'托管环境故障排除','CDN Configuration: Full-Site Delivery'=>'CDN 配置：全站交付','Plugin Configuration'=>'插件配置','Performance Audit & Consultation'=>'绩效审计与咨询','Sales Questions'=>'销售问题','Billing Support'=>'账单支持','Plugin configuration successfully updated and all caches successfully emptied.'=>'插件配置已成功更新，所有缓存已成功清空。','Purge All Caches Except CloudFlare'=>'清除除 CloudFlare 以外的所有缓存','Unlock Feature'=>'解锁功能','"New Relic" is a powerful performance monitoring and analysis tool that enhance the speed and efficiency 
			of a WordPress website. By utilizing New Relic, website owners can gain valuable insights into their 
			site\'s performance, identifying bottlenecks, slow queries, and other performance issues. With this 
			information, users can optimize their website\'s configuration, improve caching strategies, and make 
			informed decisions to deliver a faster and more responsive browsing experience for their visitors.'=>'"New Relic "是一款功能强大的性能监控和分析工具，可提高 WordPress 网站的速度和效率。
			WordPress 网站的速度和效率。通过使用 New Relic，网站所有者可以深入了解其网站的性能，发现瓶颈、缓慢查询和其他性能问题。
			网站的性能，找出瓶颈、查询速度慢和其他性能问题。有了这些
			信息，用户可以优化网站配置，改进缓存策略，并做出明智的决策，以提供更快、更优质的服务。
			做出明智的决定，为访问者提供更快、反应更灵敏的浏览体验。','Valid W3 Total Cache Pro license'=>'有效的 W3 Total Cache Pro 许可证','Fragment caching is a powerful feature that helps improve the speed and performance of your 
			website. It allows you to cache specific sections or fragments of your web pages instead 
			of caching the entire page. By selectively caching these fragments, such as sidebar widgets 
			or dynamic content, you can reduce the processing time required to generate the page, 
			resulting in faster load times and improved overall site performance.'=>'片段缓存是一项强大的功能，有助于提高网站的速度和性能。
			速度和性能。它允许你缓存网页的特定部分或片段，而不是缓存整个页面。
			而不是缓存整个页面。通过有选择性地缓存这些片段（如侧边栏小部件
			或动态内容，就能减少生成页面所需的处理时间、
			从而加快加载速度，提高网站整体性能。','CloudFlare is a powerful content delivery network (CDN) and security service that can greatly enhance 
			the performance and security of your WordPress website. By integrating CloudFlare with W3 Total Cache, 
			you can take advantage of its global network of servers to deliver your website\'s content faster to 
			visitors from around the world, resulting in reduced loading times and improved user experience. 
			Additionally, CloudFlare offers various optimization features like minification, caching, and image 
			optimization, further accelerating your website\'s loading speed and overall performance.'=>'CloudFlare 是一个功能强大的内容交付网络（CDN）和安全服务，可以大大提高
			的性能和安全性。通过将 CloudFlare 与 W3 Total Cache 集成、
			通过将 CloudFlare 与 W3 Total Cache 集成，您就可以利用其全球服务器网络，将网站内容更快地传输给来自世界各地的
			来自世界各地的访问者，从而缩短加载时间并改善用户体验。
			此外，CloudFlare 还提供各种优化功能，如最小化、缓存和图像
			优化等各种优化功能，进一步加快网站的加载速度和整体性能。','CDN Cache'=>'CDN 缓存','Content Delivery Network (CDN) is a powerful feature that can significantly enhance the performance of 
			your WordPress website. By leveraging a distributed network of servers located worldwide, a CDN helps 
			deliver your website\'s static files, such as images, CSS, and JavaScript, to visitors more efficiently. 
			This reduces the latency and improves the loading speed of your website, resulting in a faster and 
			smoother browsing experience for your users. With W3 Total Cache\'s CDN integration, you can easily 
			configure and connect your website to a CDN service of your choice, unleashing the full potential of 
			your WordPress site\'s speed optimization.'=>'内容分发网络（CDN）是一项强大的功能，可以显著提高 WordPress 网站的性能。
			WordPress 网站的性能。通过利用分布在世界各地的服务器网络，CDN 可以帮助
			将您网站的静态文件（如图片、CSS 和 JavaScript）更高效地传送给访问者。
			这就减少了延迟，提高了网站的加载速度，从而为用户带来更快、更流畅的浏览体验。
			流畅的浏览体验。通过 W3 Total Cache 的 CDN 集成，您可以轻松地
			配置并将您的网站连接到您选择的 CDN 服务，充分释放
			您的 WordPress 网站的速度优化潜力。','Browser Cache Content-Security-Policy-Report-Only Settings'=>'浏览器缓存内容-安全-策略-仅报告设置','The Content Security Policy - Report Only requires the "report-uri" and/or "report-to" directives. Please define one or both of these directives %1$shere%2$s.'=>'内容安全策略 - 仅报告 "要求使用 "report-uri "和/或 "report-to "指令。请定义其中一个或两个指令 %1$shere%2$s。','The Content Security Policy Report Only (%1$sCSPRO%2$s) header allows web developers to experiment with policies by monitoring (but not enforcing) their effects. These violation reports consist of JSON documents sent via an HTTP POST request to the specified URI. This header is applied separately from the Content-Security-Policy and is useful for testing alternative configurations.'=>'仅内容安全策略报告 (%1$sCSPRO%2$s) 标头允许网络开发人员通过监控（但不执行）策略的效果来试验策略。这些违规报告由通过 HTTP POST 请求发送到指定 URI 的 JSON 文档组成。该标头与 Content-Security-Policy 分开应用，可用于测试其他配置。','Specifies valid sources for Worker, SharedWorker, or ServiceWorker scripts.'=>'指定 Worker、SharedWorker 或 ServiceWorker 脚本的有效来源。','Specifies valid sources for inline styles applied to individual DOM elements.'=>'指定应用于单个 DOM 元素的内联样式的有效来源。','Specifies valid sources for stylesheet <style> elements and <link> elements with rel="stylesheet".'=>'指定样式表 <style> 元素和带有 rel="stylesheet" 的 <link> 元素的有效来源。','Specifies valid sources for JavaScript inline event handlers.'=>'指定 JavaScript 内联事件处理程序的有效来源。','Specifies valid sources for JavaScript <script> elements.'=>'指定 JavaScript <script> 元素的有效来源。','Specifies which manifest can be applied to the resource.'=>'指定可应用于资源的清单。','Defines the valid sources for web workers and nested browsing contexts loaded using elements such as <frame> and <iframe>. For workers, non-compliant requests are treated as fatal network errors by the user agent.'=>'定义了使用 <frame> 和 <iframe> 等元素加载的网络工作者和嵌套浏览上下文的有效来源。对于 Worker，不符合要求的请求会被用户代理视为致命的网络错误。','The referenced "group" should be defined in either the Report-To or Reporting-Endpoints HTTP headers. These will need to be manually defined either via htaccess or another method of modifying HTTP headers.'=>'引用的 "组 "应在 Report-To 或 Reporting-Endpoints HTTP 标头中定义。这些都需要通过 htaccess 或其他修改 HTTP 标头的方法手动定义。','Defines a reporting endpoint "group" to which violation reports should to be sent.'=>'定义应向其发送违规报告的报告端点 "组"。','Instructs the user agent to report attempts to violate the Content Security Policy. These violation reports consist of JSON documents sent via an HTTP POST request to the specified URI.'=>'指示用户代理报告违反内容安全策略的尝试。这些违规报告由通过 HTTP POST 请求发送到指定 URI 的 JSON 文档组成。','Content Security Policy Report Only'=>'仅限内容安全政策报告','worker-src:'=>'worker-src：','style-src-attr:'=>'style-src-attr：','style-src-elem:'=>'style-src-elem：','script-src-attr:'=>'script-src-attr：','script-src-elem:'=>'script-src-elem：','manifest-src:'=>'manifest-src：','child-src:'=>'child-src：','report-to:'=>'report-to：','report-uri:'=>'report-uri：','Passed Audits'=>'通过审计','Diagnostics'=>'诊断','Opportunities'=>'机会','Tips'=>'提示','No identified items were provided by Google PageSpeed Insights API for this metric'=>'Google PageSpeed Insights API 没有为这一指标提供识别项目','Value'=>'值','Element'=>'元素','Statistic'=>'统计','Cache Hit Probability'=>'缓存命中概率','Cache Lifetime Miliseconds'=>'缓存寿命 毫秒','Total'=>'总计','Execution Time'=>'执行时间','Parse/Compile Time'=>'解析/编译时间','Duration'=>'时效','Start Time'=>'开始时间','Transfer Size'=>'传输大小','Group'=>'组','Type'=>'类型','Wasted Miliseconds'=>'浪费的毫秒','Wasted Percentage'=>'浪费百分比','Wasted Bytes'=>'浪费的字节','Total Bytes'=>'总字节数','Copy Full URL'=>'复制完整 URL','Other Screenshot'=>'其他截图','Desktop: '=>'桌面： ','Mobile: '=>'手机： ','View All Results'=>'查看所有结果','CLS'=>'CLS','Cumulative Layout Shift'=>'渐进式布局切换','LCP'=>'LCP','Largest Contentful Paint'=>'最大内容绘制','TBT'=>'TBT','Total Blocking Time'=>'总阻塞时间','FCP'=>'FCP','First Contentful Paint'=>'首次内容绘制','ALL'=>'所有','Audit Results'=>'审计结果','Final Screenshot'=>'最终截图','Pageload Thumbnails'=>'分页缩略图','Screenshots'=>'屏幕截图','Core Metrics'=>'核心指标','90–100'=>'90-100','50–89'=>'50-89','0–49'=>'0-49','%1$sValues are estimated and may vary. The %2$sperformance score is calculated%3$s directly from these metrics. %4$sSee calculator.%5$s%6$s'=>'%1$s 价值是估计的，可能会有所不同。%2$s 性能分数是直接根据这些指标计算得出的 %3$s。%4$s 请参阅计算器。%5$s%6$s','Desktop'=>'桌面端','Mobile'=>'移动端','An error has occured!'=>'发生错误！','Running Analysis. This may take up to 2 minutes.'=>'运行分析。这可能需要 2 分钟。','Analysis last run '=>'分析最后一次运行 ','This tool will analyze your website\'s homepage using the Google PageSpeed Insights API to gather desktop/mobile performance metrics. Additionally for each metric W3 Total Cache will include an explaination of the metric and our recommendation for achieving improvments via W3 Total Cache features/extensions if available. Results will be cached for %1$s but will forcibly refresh via the "Refresh Analysis" button.'=>'该工具将使用 Google PageSpeed Insights API 分析您的网站主页，以收集桌面/移动性能指标。此外，对于每项指标，W3 Total Cache 还将提供指标解释，以及通过 W3 Total Cache 功能/扩展（如果可用）实现改进的建议。结果将缓存 %1$s，但会通过 "刷新分析 "按钮强制刷新。','Desktop response Message : '=>'桌面响应信息 ： ','Desktop response Code : '=>'桌面响应代码 ： ','Mobile response Message : '=>'手机回复信息 ： ','Mobile response Code : '=>'手机回复代码 ： ','Analyze URL : '=>'分析 URL ： ','API request failed!'=>'API 请求失败！','Before you can get started using the Google PageSpeed tool, you’ll first need to authorize access. Please click %1$s.'=>'在开始使用 Google PageSpeed 工具之前，您首先需要授权访问。请单击 %1$s。','An unknown error occured attempting to filter audit results!'=>'尝试过滤审计结果时出现未知错误！','Error : '=>'错误 ： ','Reduce the time spent parsing CSS by minifying, or deferring non-critical CSS, or removing unused CSS. (Use %1$s %2$sMinify for CSS%3$s and compression.) Use %4$sCDN%5$s and HTTP2 Push if available on server.'=>'通过缩小、推迟非关键CSS或删除未使用的CSS来减少分析CSS所花费的时间。（对CSS%3$s和压缩使用%1$s%2$s Minify。）如果服务器上有可用的话，请使用%4$sCDN%5$s和HTTP2 Push。','Reduce your JavaScript payload by implementing code splitting, minifying and compressing your JavaScript code, removing unused code, and following the PRPL pattern. (Use %1$s %2$sMinify for JS%3$s and compression.) Use %4$sCDN%5$s and HTTP2 Push if available on server.'=>'通过代码分割、最小化和压缩 JavaScript 代码、删除未使用的代码以及遵循 PRPL 模式来减少 JavaScript 的有效载荷。(使用 %1$s %2$sMinify 进行 JS%3$s 和压缩）。如果服务器上有，则使用 %4$sCDN%5$s 和 HTTP2 Push。','Review your website\'s third-party code and remove the ones that aren\'t adding any value to your website.'=>'检查网站的第三方代码，删除不能为网站带来任何价值的代码。','Without completely redesigning your web page from scratch, typically you cannot resolve this warning.  Understand that this warning is significant and if you get it for more than one or two pages in your site, you should consider:'=>'如果不从头开始重新设计网页，通常无法解决此警告。  要知道，这个警告是很重要的，如果你的网站中有超过一到两个页面收到这个警告，你就应该考虑一下：','Features that will help performace of the above:'=>'有助于实现上述功能的功能：','Use another rendering strategy.'=>'使用另一种渲染策略。','Minimize critical JavaScript.'=>'最小化关键 JavaScript。','If the cause is client-side rendering:'=>'如果原因是客户端渲染：','Cache assets using a service worker.'=>'使用服务工作者缓存资产。','Deliver different assets based on the network connection (adaptive serving).'=>'根据网络连接提供不同的资产（自适应服务）。','Compress text files.'=>'压缩文本文件。','Preload important resources.'=>'预载重要资源。','Optimize and compress images.'=>'优化和压缩图像。','If the cause is resource load times:'=>'如果原因是资源加载时间：','Minimize unused polyfills.'=>'尽量减少未使用的多填充。','Defer unused JavaScript.'=>'推迟未使用的 JavaScript。','Minify and compress JavaScript files.'=>'最小化和压缩 JavaScript 文件。','Inline critical CSS.'=>'内联关键 CSS。','Defer non-critical CSS.'=>'推迟非关键 CSS。','Minify CSS.'=>'最小化 CSS。','If the cause is render-blocking JavaScript and CSS:'=>'如果原因是 JavaScript 和 CSS 的渲染阻塞：','Establish third-party connections early.'=>'尽早建立第三方联系。','Serve HTML pages cache-first.'=>'优先缓存 HTML 网页。','Cache assets.'=>'缓存资产。','Route users to a nearby CDN.'=>'将用户路由到附近的 CDN。','Optimize your server.'=>'优化服务器。','If the cause is slow server response time:'=>'如果原因是服务器响应时间过慢：','How To Fix Poor LCP'=>'如何修复低劣的 LCP','Compress your text resources.'=>'压缩文本资源。','Remove unnecessary third-party scripts.'=>'删除不必要的第三方脚本。','Combine images using CSS sprites.'=>'使用 CSS 精灵组合图像。','Avoid multiple page redirects.'=>'避免多页面重定向。','Eliminate Render Blocking CSS and apply asynchronous loading where applicable. Additionally, image optimization by way of resizing, lazy loaidng, and webp conversion can impact this metric as well.'=>'消除渲染阻塞 CSS 并在适用情况下应用异步加载。此外，通过调整大小、懒加载和 webp 转换等方式优化图片也会影响这一指标。','Use default values for best results'=>'使用默认值以获得最佳效果','Use %1$sBrowser Caching%2$s in %3$s and set the Expires header and cache control header for static files and HTML.'=>'在 %3$s 中使用 %1$s 浏览器缓存 %2$s 并为静态文件和 HTML 设置 Expires 标头和缓存控制标头。','The fastest way to bypass the delay of external resources is to use in-line styles for above-the-fold content.'=>'绕过外部资源延迟的最快方法是在折叠内容上方使用内联样式。','In order for content to be displayed to the user, the browser must first download, parse, and process all external stylesheets it encounters before it can display or render any content to a user\'s screen.'=>'为了向用户显示内容，浏览器必须先下载、解析和处理遇到的所有外部样式表，然后才能在用户屏幕上显示或呈现任何内容。','How it\'s measured: Lighthouse\'s Speed Index measurement comes from a node module called Speedline.'=>'测量方法Lighthouse（兆光科技）的速度指数测量来自一个名为Speedline的节点模块。','What it measures: The Speed Index is the average time at which visible parts of the page are displayed.'=>'衡量指标速度指数是显示页面可见部分的平均时间。','Lighthouse Performance score weighting: 10%'=>'灯塔绩效评分权重：10%','What it represents: How much is visible at a time during load.'=>'它代表什么：在加载过程中，一次能看到多少。','Enable Page Cache using the fastest engine.'=>'使用最快的引擎启用页面缓存。','Use font-display atribute: The font-display attribute determines how the font is displayed during your page load, based on whether it has been downloaded and is ready for use.'=>'使用 font-display 属性：font-display 属性根据字体是否已下载并准备就绪，决定字体在页面加载过程中的显示方式。','Preload fonts with a plugin or manually:'=>'使用插件或手动预载字体：','It\'s advisable to host the fonts on the server instead of using Google CDN'=>'建议在服务器上托管字体，而不是使用 Google CDN','More details %1$s.'=>'更多详细信息 %1$s。','Use the "viewport" <meta> tag to control the viewport\'s size and shape form mobile friendly website:'=>'使用 "viewport" <meta> 标签来控制视口的大小和形状，形成移动友好型网站：','Note that modern browsers automatically calculate the aspect ratio for an image/video based on the declared width and height attributes.'=>'请注意，现代浏览器会根据声明的宽度和高度属性自动计算图像/视频的宽高比。','Where width and height have been declared as 640 and 360 pixels respectively.'=>'其中宽度和高度分别声明为 640 和 360 像素。','To fix this audit, simply specify, both, the width and height for your webpage\'s image and video elements. This ensures that the correct spacing is used for images and videos.'=>'要解决这一问题，只需指定网页图片和视频元素的宽度和高度即可。这样就能确保图片和视频使用正确的间距。','Only using compositor properties:'=>'仅使用合成器属性：','Using %1$s JS Minify and deferring or using async may also help.'=>'使用 %1$s JS Minify 和延迟或使用 async 也可能有帮助。','Avoiding the use of document.write() should ideally be built into your development workflow so that your production website is optimized for web performance from the beginning.'=>'避免使用 document.write()，最好是将其纳入开发工作流程，以便从一开始就优化生产网站的网络性能。','You can fix this audit by preferably eliminating document.write() altogether, wherever possible.'=>'您可以尽可能取消 document.write()，从而解决这一审计问题。','If you\'re supporting older browsers that don\'t support passive event listeners, you\'ll need to use feature detection or a polyfill. See the Feature Detection section of the WICG Passive event listeners explainer document for more information.'=>'如果要支持不支持被动事件侦听器的旧版浏览器，则需要使用特征检测或多填充。有关详细信息，请参阅 WICG 被动事件侦听器说明文档中的 "特征检测 "部分。','For example:'=>'例如：','If you\'re only supporting browsers that have passive event listener support, just add the flag.'=>'如果只支持支持被动事件监听器的浏览器，只需添加标记即可。','Add a passive flag to every event listener that Lighthouse identified.'=>'为灯塔识别的每个事件监听器添加被动标记。','Exclude the image from being lazy-loaded if the %1$s Lazy load is enabled in Performance &raquo; User Experience &raquo; Exclude words.'=>'如果在性能&raquo; 用户体验&raquo; 排除字样中启用了 %1$s 懒加载，则将图像排除在懒加载之外。','	element.'=>'	元素。','Don\'t lazy load images that appear "above the fold" just use a standard '=>'不要偷懒加载出现在 "折页上方 "的图片，只需使用标准的 ','Preload - Lazyload embeded videos.'=>'预加载 - 懒加载嵌入的视频。','Use Cloudflare Workers'=>'使用 Cloudflare Workers','Use A Lightweight Social Sharing Plugin'=>'使用轻量级社交分享插件','Replace Embeds With Screenshots'=>'用截图替换嵌入','Don\'t Overtrack In Google Tag Manager'=>'不要在 Google 标签管理器中过度跟踪','Avoid Google AdSense And Maps'=>'避免使用 Google AdSense 和地图','Prefetch Or Preconnect Third-Party Scripts'=>'预取或预连接第三方脚本','Defer Parsing Of JavaScript'=>'延迟解析 JavaScript','Delay Third-Party JavaScript'=>'延迟第三方 JavaScript','Host Gravatars Locally'=>'本地寄存 Gravatars','Host Facebook Pixel Locally'=>'在本地托管 Facebook 像素','Host Google Analytics Locally'=>'在本地托管谷歌分析','Host Google Fonts Locally'=>'在本地托管 Google 字体','Lazy Load YouTube Videos'=>'懒人加载 YouTube 视频','Find Slow Third-Party-Code'=>'查找慢速第三方代码','Stick to using compositor properties to keep events away from the main-thread. Compositor properties are run on a separate compositor thread, freeing the main-thread for longer and improving your page load performance.'=>'坚持使用合成器属性，让事件远离主线程。合成器属性在单独的合成器线程上运行，这样就能更长时间地释放主线程，提高页面加载性能。','Only using compositor properties'=>'仅使用合成器属性','Reduce the time spent parsing CSS by minifying, or deferring non-critical CSS, or removing unused CSS. (Use %1$s Minify for %2$sCSS%3$s and compression.) Use %4$sCDN%5$s and HTTP2 Push if available on server.'=>'通过最小化、延迟非关键 CSS 或删除未使用的 CSS 来减少解析 CSS 所花费的时间。(使用 %1$s Minify 进行 %2$sCSS%3$s 和压缩。）如果服务器上有，则使用 %4$sCDN%5$s 和 HTTP2 Push。','Reducing CSS parsing time'=>'缩短 CSS 解析时间','Reduce your JavaScript payload by implementing code splitting, minifying and compressing your JavaScript code, removing unused code, and following the PRPL pattern. (Use %1$s Minify for %2$sJS%3$s and compression.) Use %4$sCDN%5$s and HTTP2 Push if available on server.'=>'通过实现代码拆分、缩小和压缩JavaScript代码、删除未使用的代码以及遵循PRPL模式来减少JavaScript负载。（对%2$s JS%3$s和压缩使用%1$s Minify。）如果服务器上有可用的话，请使用%4$sCDN%5$s和HTTP2 Push。','Reducing JavaScript execution time'=>'缩短 JavaScript 的执行时间','Delay 3rd-party JS'=>'延迟第三方 JS','Debouncing your input handlers helps solve both of the above problems.'=>'对输入处理程序进行去抖有助于解决上述两个问题。','Avoid using long-running input handlers (which may block scrolling) and do not make style changes in input handlers (which is likely to cause repainting of pixels).'=>'避免使用长时间运行的输入处理程序（可能会阻止滚动），也不要在输入处理程序中更改样式（可能会导致像素重绘）。','Debouncing your input handlers'=>'消除输入处理程序的弹跳','Review your website\'s third-party code and remove the ones	that aren\'t adding any value to your website.'=>'检查网站的第三方代码，删除不能为网站带来任何价值的代码。','Optimizing third-party JavaScript'=>'优化第三方 JavaScript','Chrome DevTools Timeline Recordings'=>'Chrome 浏览器开发工具时间轴录音','You can access those data from JavaScript using the API or by viewing them on your %1$s.'=>'您可以使用应用程序接口从 JavaScript 访问这些数据，也可以在您的 %1$s 上查看这些数据。','You do that by inserting API calls in your JavaScript and then extracting detailed timing data that you can use to optimize your code.'=>'具体做法是在 JavaScript 中插入 API 调用，然后提取详细的定时数据，用于优化代码。','User Timing API'=>'用户定时 API','The %1$s gives you a way to measure your app\'s JavaScript performance.'=>'%1$s 为您提供了一种衡量应用程序 JavaScript 性能的方法。','Using a different slider'=>'使用不同的滑块','Using a different theme'=>'使用不同的主题','Using a simpler web page builder as many page builders add a lot of code bloat'=>'使用更简单的网页制作工具，因为许多网页制作工具会添加大量臃肿的代码','Reducing the amount of widgets / sections within your web pages or page layouts'=>'减少网页或页面布局中的小部件/版块数量','Without completely redesigning your web page from scratch, typically you cannot resolve this warning. Understand that this warning is significant and if you get it for more than one or two pages in your site, you should consider:'=>'如果不从头开始重新设计网页，通常无法解决此警告。  要知道，这个警告是很重要的，如果你的网站中有超过一到两个页面收到这个警告，你就应该考虑一下：','Use %1$sBrowser Caching%2$s for static files and HTML  - 1 year for static files 1 hor for html'=>'对静态文件和 HTML 使用 %1$sBrowser Caching%2$s - 静态文件 1 年，HTML 1 小时','Optimize your image delivery by sizing them properly and compressing them for smaller sizes. Use Webp conversion via the %1$s %2$sImage Service%3$s extension.'=>'通过适当调整图片大小和压缩图片以缩小尺寸，优化图片传输。通过 %1$s %2$sImage Service%3$s 扩展使用 Webp 转换。','Compress your HTML, CSS, and JavaScript files and minify your CSS and JavaScript to ensure your text-based resources are as small as they can be. Use the %1$s Minify %2$sJS%3$s and %4$sCSS%5$s features to accomplish this.'=>'压缩 HTML、CSS 和 JavaScript 文件，并对 CSS 和 JavaScript 进行最小化，以确保基于文本的资源尽可能小。使用 %1$s Minify %2$sJS%3$s 和 %4$sCSS%5$s 功能来实现这一目标。','Deffer or async the JS (Select  Non blocking using Defer or  Non blocking using async Embed method in %1$s %2$sMinify%3$s options before head and after body)'=>'延迟或异步 JS（在 %1$s %2$sMinify%3$s 选项中选择使用延迟的非阻塞或使用异步嵌入方法的非阻塞，在头部之前和正文之后）。','Learn more about implementing this technique %1$s.'=>'了解有关实施此技术 %1$s 的更多信息。',' you can use the translated ES5 code instead. In this manner, you are always serving modern code to modern browsers.'=>' 您可以使用翻译过的 ES5 代码。这样，您就能始终向现代浏览器提供现代代码。','For browsers that don\'t support '=>'对于不支持以下功能的浏览器 ',' also supports most of the ES6 features. This lets you load regular JavaScript files with ES6 features, knowing that the browser can handle it.'=>' 也支持大部分 ES6 功能。这样，您就可以加载具有 ES6 功能的普通 JavaScript 文件，因为您知道浏览器可以处理这些文件。','Every browser that supports '=>'所有支持 ','Implement modern feature-detection using '=>'使用 ','One way to deal with this issue is to load polyfills, only when needed, which can provide feature-detection support at JavaScript runtime. However, it is often very difficult to implement in practice.'=>'解决这个问题的一种方法是只在需要时加载 polyfills，它可以在 JavaScript 运行时提供功能检测支持。不过，在实际应用中往往很难实现。','webpack-stats-duplicates'=>'webpack-stats-duplicates','To fix this audit, use a tool like %1$s to identify duplicate modules'=>'要修复此审计，请使用 %1$s 等工具来识别重复模块','Incorporate good site building practices into your development workflow to ensure you avoid duplication of JavaScript modules in the first place.'=>'将良好的网站建设实践纳入开发工作流程，确保首先避免重复使用 JavaScript 模块。','Preload fonts hosted on the server: '=>'预载服务器上托管的字体： ','JS and CSS - Use HTTP2/Push for %1$s Minified files'=>'JS 和 CSS - 使用 HTTP2/Push 获取 %1$s 简化文件','Remember that combining multiple redirects into a single redirect is the most effective way to improve web performance.'=>'请记住，将多个重定向合并为一个重定向是提高网络性能的最有效方法。','Similarly, remove temporary redirects if not needed anymore.'=>'同样，如果不再需要，也要删除临时重定向。','Always redirect to the preferred version of the URL, especially, when redirects are dynamically generated. This helps eliminate unnecessary redirects.'=>'始终重定向到首选版本的 URL，尤其是动态生成重定向时。这有助于消除不必要的重定向。','Wherever possible, avoid landing page redirects; especially, the practice of executing separate, individual redirects for reasons such as protocol change, adding www, mobile-specific page, geo-location, and subdomain.'=>'尽可能避免登陆页面重定向；尤其是避免因协议变更、添加 www、移动专用页面、地理位置和子域等原因而执行单独、个别的重定向。','Avoid client-side redirects, as much as possible, as they are slower, non-cacheable and may not be supported by browsers by default.'=>'尽可能避免客户端重定向，因为客户端重定向速度较慢、不可缓存，而且浏览器默认情况下可能不支持客户端重定向。','When dealing with server-side redirects, we recommend that they be executed via web server configuration as they are often faster than application-level configuration.'=>'在处理服务器端重定向时，我们建议通过网络服务器配置来执行，因为它们通常比应用程序级配置更快。','Use %1$s %2$sPage Caching%3$s (fastest module)'=>'使用 %1$s %2$s 页面缓存%3$s （最快模块）','Where "https://third-party-example.com"	is the domain of the respective third-party resource.'=>'其中，"https://third-party-example.com "是相应第三方资源的域名。','Add dns-prefetch for all other third-party domains. For all other third-party scripts, including non-critical ones, add the following code to the link tag:'=>'为所有其他第三方域名添加 dns-prefetch。对于所有其他第三方脚本，包括非关键脚本，在链接标记中添加以下代码：','Where "https://third-party-example.com" is the critical third-party domain your page intends to connect to.'=>'其中，"https://third-party-example.com "是您的页面打算连接的关键第三方域。','Add preconnect for critical third-party domains. Out of the list of third-party resources flagged by Google Page speed, identify the critical third-party resources and add the following code to the link tag:'=>'为重要的第三方域添加预连接。在 Google Page speed 标记的第三方资源列表中，找出关键的第三方资源并在链接标记中添加以下代码：','Look at the list of third-party resources flagged by Google Page speed and add preconnect or dns-prefetch to their link tags depending on whether the resource is critical or not.'=>'查看 Google Page speed 标记的第三方资源列表，并根据资源是否重要，在链接标记中添加 preconnect 或 dns-prefetch。','Brotli extension'=>'Brotli 扩展','Use %1$s Browser Caching - Peformance>Browser Cache - Enable Gzip compression or Brotli compression (Gzip compression is most common and for Brotli compression you need to install %2$s on your server.'=>'使用 %1$s 浏览器缓存 - 性能 > 浏览器缓存 - 启用 Gzip 压缩或 Brotli 压缩（Gzip 压缩最为常见，若要使用 Brotli 压缩，则需要在服务器上安装 %2$s。','Use %1$s %2$sImage Service%3$s to convert media library images to WebP.'=>'使用 %1$s %2$sImage Service%3$s 将媒体库图像转换为 WebP。','W3TC Extensions'=>'W3TC 扩展','Enable lazy load for images.'=>'启用图片懒加载。','Use image optimization plugin.'=>'使用图像优化插件。','A single image'=>'一张图片','For example, let\'s say you want to send a high-resolution image to only those users that have high-resolution screens, as	determined by the Device pixel ratio (DPR). The code would look like this:'=>'例如，假设您想只向那些拥有高分辨率屏幕（由设备像素比 (DPR) 决定）的用户发送高分辨率图像。代码如下：','Essentially, you create various sizes of your image, and then utilize the srcset tag to define when the images get served. This is useful for responsive design when you have multiple images to deliver across	several devices and dimensions.'=>'从本质上讲，你可以创建各种尺寸的图片，然后利用 srcset 标签来定义图片的服务时间。这对响应式设计非常有用，因为您需要在多个设备和尺寸上提供多张图片。','The srcset HTML tag provides the browser with variations of an image (including a fallback image) and instructs the browser to use specific images depending on the situation.'=>'srcset HTML 标签为浏览器提供图像的各种变化（包括后备图像），并指示浏览器根据情况使用特定图像。','Using srcset:'=>'使用 srcset：','photoshop'=>'影印','It\'s important to prepare images before uloading them to the website. This should be done before the Image is uploaded and can be done by using some image optimization tool like %1$s.'=>'在将图片上传到网站之前，准备好图片非常重要。这项工作应在上传图片前完成，可以使用一些图片优化工具，如 %1$s。','With this breakdown, we can see how many unused bytes are in our JS files, so we can manually remove them.'=>'有了这个细分，我们就能知道 JS 文件中有多少未使用的字节，从而可以手动删除它们。','Some themes and plugins are loading JS files or parts of the JS files on all pages and not only on the pages that should be loading on. For eaxmple if you are using some contact form plugin, there is a chance that the JS file of that plugin will load not only on the /contact/ page, but on all other pages as well and this is why the unused JS should be removed.'=>'有些主题和插件会在所有页面上加载 JS 文件或部分 JS 文件，而不是只在应该加载的页面上加载。例如，如果您正在使用某个联系表单插件，那么该插件的 JS 文件有可能不仅会加载到 /contact/ 页面，还会加载到所有其他页面，这就是为什么要删除未使用 JS 的原因。','On the %1$s tab all of the recommended settings are preset. Use the help button to simplify discovery of your %2$s and %3$s files and groups. Pay close attention to the method and location of your %3$s group embeddings. See the plugin\'s %4$s for more information on usage.'=>'在 %1$s 标签上，所有推荐设置都已预设。使用帮助按钮可简化对 %2$s 和 %3$s 文件和组的查找。请密切关注 %3$s 组嵌入的方法和位置。有关使用的更多信息，请参阅插件 %4$s。','With this breakdown, we can see how many unused bytes are in our CSS files, so we can manually remove them.'=>'有了这个明细表，我们就能知道 CSS 文件中有多少未使用的字节，从而可以手动删除它们。','After sometime, a table will show up in the tab with the resources it analyzed, and how much code is used in the webpage. All the files linked in the webpage (css, js) will be listed in the Coverage tab. Clicking on any resource there will open that resource in the Sources panel with a breakdown of Total Bytes and Unused Bytes.'=>'一段时间后，该选项卡中将显示一个表格，其中包含分析过的资源以及网页中使用的代码数量。网页中链接的所有文件（css、js）都会在 "覆盖范围 "选项卡中列出。点击任何资源，都会在 "来源 "面板中打开该资源，并显示 "总字节数 "和 "未使用字节数"。','If you have a webpage you want to analyze its code coverage. Load the webpage and click on the o button in the Coverage tab.'=>'如果你有一个网页，你想分析它的代码覆盖率。加载网页并点击覆盖率选项卡中的 o 按钮。','Coverage will open up. We will see buttons for start capturing coverage, to reload and start capturing coverage and to stop capturing coverage and show results.'=>'覆盖范围将打开。我们将看到 "开始捕捉覆盖范围"、"重新加载并开始捕捉覆盖范围"、"停止捕捉覆盖范围并显示结果 "等按钮。','Open your Chrome browser, go to “Developer Tools”, click on “More Tools” and then “Coverage”.'=>'打开 Chrome 浏览器，进入 "开发工具"，点击 "更多工具"，然后点击 "覆盖"。','Some themes and plugins are loading CSS files or parts of the CSS files on all pages and not only on the pages that should be loading on. For eaxmple if you are using some contact form plugin, there is a chance that the CSS file of that plugin will load not only on the /contact/ page, but on all other pages as well and this is why the unused CSS should be removed.'=>'有些主题和插件会在所有页面上加载 CSS 文件或 CSS 文件的一部分，而不是只在应该加载 CSS 文件的页面上加载。例如，如果你正在使用某个联系表单插件，那么该插件的 CSS 文件有可能不仅会加载到 /contact/ 页面，还会加载到所有其他页面，这就是为什么要删除未使用的 CSS 的原因。','Performance &raquo; Minify &raquo; CSS'=>'性能 " 简化 " CSS','Minify CSS'=>'压缩CSS','Performance &raquo; Minify &raquo; JS'=>'性能 " 简化 " JS','Minify JS'=>'最小化JS','%1$s can eliminate render blocking resources. Once Minified, you can defer JS in the %2$s. Render blocking CSS can be eliminated in %3$s using the "Eliminate Render blocking CSS by moving it to HTTP body" (PRO FEATURE).'=>'%1$s 可以消除渲染阻塞资源。最小化后，可以在 %2$s 中延迟 JS。在 %3$s 中，可以使用 "通过将 CSS 移至 HTTP body 来消除渲染阻塞 CSS"（专业功能）来消除渲染阻塞 CSS。','Matching Google access record found but the refresh token value is blank!'=>'找到匹配的 Google 访问记录，但刷新令牌值为空！','Missing/invalid Site ID.'=>'缺失/无效的站点 ID。','Missing/invalid Google access token JSON setting after authentication.'=>'验证后缺少/无效的 Google 访问令牌 JSON 设置。','Missing/invalid W3 API key.'=>'缺少/无效 W3 API 密钥。','Missing/invalid Google access authentication code.'=>'缺少/无效 Google 访问验证码。','No matching Google access record found for W3 key!'=>'未找到与 W3 密钥匹配的 Google 访问记录！','No refresh token provided for Google access record update!'=>'没有为 Google 访问记录更新提供刷新令牌！','No W3 key provided for Google access record update!'=>'未为 Google 访问记录更新提供 W3 密钥！','No site ID provided for Google access record update!'=>'未为 Google 访问记录更新提供站点 ID！','Response Message : '=>'回复信息 ： ','Response Code : '=>'响应代码 ： ','Refresh URL : '=>'刷新 URL ： ','API request error!'=>'API 请求错误！','Missing Google access token.'=>'缺少 Google 访问令牌。','Decreases performance by ~20% at scale in exchange for increasing interoperability with more hosting environments and WordPress idiosyncrasies. Enable this option if you experience issues with the Apache rules.'=>'性能降低约 20% at，以换取与更多主机环境和 WordPress 特异性的互操作性。如果您在使用 Apache 规则时遇到问题，请启用此选项。','Even if using a feed proxy service enabling this option is still recommended.'=>'即使使用饲料代理服务，仍建议启用此选项。','Allow W3 Total Cache to connect to the PageSpeed Insights API on your behalf.'=>'允许 W3 Total Cache 代表您连接到 PageSpeed Insights API。','Deauthorize'=>'取消授权','Valid'=>'有效','No W3-API matching record found during Google authorization return processing!'=>'在 Google 授权返回处理过程中未发现 W3-API 匹配记录！','No W3Key return to W3-API from Google!'=>'没有 W3Key 从 Google 返回 W3-API！','No authorize code returned to W3-API from Google!'=>'谷歌未向 W3-API 返回授权代码！','Failed to process authorize request!'=>'处理授权请求失败！','Return URL missing for authorize request!'=>'缺少授权请求的返回 URL！','Authorize URL missing for authorize request!'=>'授权请求缺少授权 URL！','Unique site ID missing for authorize request!'=>'授权请求缺少唯一站点 ID！','Google PageSpeed Insights API authorization successfully reset.'=>'Google PageSpeed Insights API 授权重置成功。','Missing refresh token.'=>'缺少刷新令牌。','Google PageSpeed Insights API authorization successfull.'=>'Google PageSpeed Insights API 授权成功。','Google PageSpeed Insights API authorization failed.'=>'Google PageSpeed Insights API 授权失败。','Response Code: %1$s<br/>Response Message: %2$s'=>'响应代码：%1$s<br/>响应信息：%2$s','Google PageSpeed'=>'Google页面速度','W3 API Key:'=>'W3 API 密钥：','Authorize :'=>'授权 ：','Adds the ability to analyze the website\'s homepage and provide a detailed breakdown of performance metrics including potential issues and proposed solutions.'=>'增加了分析网站主页的功能，并提供详细的性能指标明细，包括潜在问题和建议的解决方案。','Google Page Speed'=>'谷歌页面速度','MaxCDN has been replaced with StackPath CDN. As a result your configuration is now invalid and requires reconfiguration to a new %1$sCDN provider%2$s. You can migrate to StackPath using %3$sthis guide%2$s.'=>'MaxCDN 已被 StackPath CDN 取代。因此，您的配置现已失效，需要重新配置到新的 %1$sCDN 提供商%2$s。您可以使用 %3$s 本指南%2$s 迁移到 StackPath。','Feature-Policy / Permissions-Policy'=>'功能-政策/权限-政策','APC Usage'=>'APC 使用情况','Redis Usage'=>'Redis 使用情况','Memcached Usage'=>'Memcached 使用情况','Cache'=>'缓存','Don\'t cache'=>'不缓存','Object Cache Purge Log'=>'对象缓存清除日志','Database Cache Purge Log'=>'数据库缓存清除日志','view log'=>'查看日志','Page Cache Purge Log'=>'页面缓存清除日志','JS'=>'JS','CSS'=>'CSS','Install Free Backup Plugin'=>'安装免费备份插件','It\'s easy to set up and manage, backs up your entire WordPress site, has automated fault protection if an update fails, and provides easy site migration options.'=>'它易于设置和管理，备份您的整个 WordPress 站点，可以自动在更新失败时提供故障保护，并提供简单的站点迁移选项。','Protect your WordPress site from data loss by installing the FREE Total Upkeep plugin.'=>'安装免费的 Total Upkeep 备份插件，让您的 WordPress 站点免受数据丢失。','W3 Total Cache has detected that you do not have a Backup Plugin installed.'=>'W3 Total Cache 检测到您没有安装任何数据备份插件。','Swarmify extension is currently'=>'Swarmify 扩展当前已','Not Supported. (%1$s %2$s See %3$sNewRelic Requirements%4$s page.)'=>'不支持。（%1$s %2$s 请参阅 %3$sNewRelic 要求%4$s 页面。）','NewRelic extension is currently'=>'NewRelic 扩展当前已','Example extension is currently '=>'示例扩展当前已 ','If you\'re an existing StackPath customer, enable %1$s and:'=>'如果您是 StackPath 的现有客户，请启用 %1$s 然后：','StackPath works magically with W3 Total Cache.'=>'StackPath 可以与 W3 Total Cache 配合使用。','Dramatically increase website speeds in just a few clicks! Add the StackPath content delivery network (%1$s) service to your site.'=>'只需点击几下即可显著提高网站速度！将 StackPath 内容分发网络 (%1$s) 服务添加到您的站点。','StackPath is a service that lets you speed up your site even more with W3 Total Cache. Sign up now and save!'=>'StackPath 服务可以和 W3 Total Cache 一并使用从而加速您的站点。现在注册并加速您的站点！','If you\'re an existing StackPath customer, enable %1$s and Authorize. If you need help configuring 
							your %1$s, we also offer Premium Services to assist you.'=>'如果您是 StackPath 的现有客户，请启用 %1$s 并授权。若您在配置 %1$s 时
						需要帮助，我们我们还提供高级服务来帮助您。','StackPath works magically with W3 Total Cache to speed up your site around the world for as little as $10 per month.'=>'StackPath 可以与 W3 Total Cache 配合使用，以低至 $10/月 的价格在全球加速您的站点。','Enhance your website performance by adding StackPath\'s (%1$s) service to your site.'=>'使用 StackPath 的（%1$s）服务以提升您的网站加载性能。','StackPath is a service that lets you speed up your site even more with W3 Total Cache. Sign up now to recieve a special offer!'=>'StackPath 是一项可让您使用 W3 Total Cache 加快网站速度的服务。现在注册，即可获得特别优惠！','W3 Total Cache has detected that you do not have a %1$s configured'=>'W3 Total Cache 已检测到您没有配置 %1$s','Sign Up Now and save!'=>'立即注册，节省费用！','Host the entire website with your compatible %1$s provider to reduce page load time.'=>'将整个网站托管给兼容的 %1$s 提供商，以缩短页面加载时间。','[W3TC] Page Cache prime (every %d seconds)'=>'[W3TC]页面缓存黄金时间（每%d秒一次）','[W3TC] Page Cache file GC (every %d seconds)'=>'[W3TC] 页面缓存文件 GC（每 %d 秒一次）','[W3TC] Object Cache file GC (every %d seconds)'=>'[W3TC] 对象缓存文件 GC（每 %d 秒一次）','[W3TC] Database Cache file GC (every %d seconds)'=>'[W3TC] 数据库缓存文件 GC（每 %d 秒一次）','make it automatically.'=>'自动生成。','or use FTP form to allow '=>'或使用 FTP 表格允许 ','Files and directories could not be automatically created to complete the installation.'=>'无法自动创建文件和目录以完成安装。','W3 Total Cache Error:'=>'W3 Total Cache 错误：','%1$s could not be written, please edit config and add:%2$s before %3$s.'=>'无法写入 %1$s，请编辑配置并在 %3$s 前添加：%2$s。','%1$s could not be written, please edit config and add: %2$s before %3$s.'=>'%1$s 无法写入，请编辑配置并在 %3$s 前添加：%2$s。','Default settings could not be restored. Please run %1$s to make the configuration file write-able, then try again.'=>'无法恢复默认设置。请运行 %1$s 使配置文件可写，然后重试。','Please enter %1$s or %2$s password for the %2$s account.'=>'请输入 %2$s 账户的 %1$s 或 %2$s 密码。','Please enter %1$s or %2$s login for the server. Create a temporary one just for this support case if needed.'=>'请输入服务器的 %1$s 或 %2$s 登录名。如果需要，请为此案创建一个临时登录名。','FTP'=>'FTP','SSH'=>'SSH','Please enter %1$s or %2$s host for the site.'=>'请输入 %1$s 或 %2$s 网站主机。','URL'=>'URL','Please enter the address of the site in the site %1$s field.'=>'请在站点 %1$s 字段中输入站点地址。','Premium Support'=>'高级支持','Total query time: '=>'总查询时间： ','Cached queries: '=>'缓存查询： ','Total queries: '=>'总查询次数： ','Db cache debug info:'=>'数据库缓存调试信息：','Cant find minification base url, make sure minification folder sits inside WP_CONTENT_DIR and DOCUMENT_ROOT is set correctly'=>'无法找到最小化基础 URL，请确保最小化文件夹位于 WP_CONTENT_DIR 内，且 DOCUMENT_ROOT 设置正确','Replaced URLs for CDN:'=>'替换了 CDN 的 URL：','DONOTCDN constant is defined'=>'定义了 DONOTCDN 常量','SSL is rejected'=>'拒绝 SSL','request URI is rejected'=>'请求 URI 被拒绝','user agent is rejected'=>'用户代理被拒绝','[W3TC] CDN auto upload (every %1$d seconds)'=>'[W3TC] CDN 自动上传（每 %1$d 秒一次）','[W3TC] CDN queue process (every %1$d seconds)'=>'[W3TC]CDN队列进程（每%1$d秒一次）','Post was modified before wp_rewrite initialization. Cant flush cache.'=>'帖子在 wp_rewrite 初始化之前被修改。无法清除缓存。','Please see %2$sAmazon\'s CloudFront documentation -- Paying for file invalidation%3$s:%1$sThe first 1,000 invalidation paths that you submit per month are free; you pay for each invalidation path over 1,000 in a month.%1$sYou can disable automatic purging by enabling "Only purge CDN manually".'=>'请参阅%2$s亚马逊 CloudFront 文档 -- 为文件无效付费%3$s：%1$s您每月提交的前 1,000 个无效路径是免费的；当月超过 1,000 个无效路径时，您需要为每个无效路径付费。%1$s您可以通过启用 "仅手动清除 CDN "来禁用自动清除功能。','Please see %1$sAmazon\'s CloudFront documentation -- Paying for file invalidation%2$sThe first 1,000 invalidation paths that you submit per month are free; you pay for each invalidation path over 1,000 in a month.%3$sYou can disable automatic purging by enabling %4$sOnly purge CDN manually%5$s.'=>'请参阅%1$s亚马逊 CloudFront 文档 -- 为文件失效付费%2$s每月提交的前 1,000 个失效路径是免费的；每月超过 1,000 个失效路径时，每个失效路径都要付费。%3$s您可以通过启用%4$s仅手动清除 CDN%5$s来禁用自动清除功能。',' %1$sNot supported by "Disk: Enhanced" page cache method for Nginx%2$s'=>' %1$sNot supported by "Disk：Nginx 的 "增强型 "页面缓存方法%2$s','%1$s to make existing file modifications visible to visitors with a primed cache'=>'%1$s 使已启动缓存的访问者可以看到对现有文件的修改',' Domain:'=>' 领域：','%1$sW3 Total Cache Error:%2$s database driver doesn\'t exist: %3$s.'=>'%1$sW3 Total Cache 错误：%2$s 数据库驱动程序不存在：%3$s.','%1$sW3 Total Cache Error:%2$s some files appear to be missing or out of place. Please re-install plugin or remove %3$s%4$s%5$s. %6$s'=>'%1$sW3 Total Cache 错误：%2$s 某些文件似乎丢失或位置不对。请重新安装插件或删除 %3$s%4$s%5$s。%6$s','Attempt to create object of class %1$s has been made, but file %2$s doesnt exists'=>'已尝试创建类%1$s的对象，但文件%2$s不存在','Show More'=>'显示更多','Connection read timeout'=>'连接读取超时','Connection retry interval'=>'连接重试间隔','Connection timeout'=>'连接超时','Verify TLS Certificates'=>'验证 TLS 证书','%1$sWP Google Maps%2$s plugin'=>'%1$sWP谷歌地图%2$s插件','%1$sGoogle Maps Easy%2$s plugin'=>'%1$sGoogle Maps Easy%2$s 插件','%1$sWP Google Map Plugin%2$s plugin'=>'%1$sWP谷歌地图插件%2$s插件','Google Maps'=>'谷歌地图','inline'=>'内联','sync (to head)'=>'同步（到头）','async'=>'异步','Exclude words:'=>'排除词语：','Process %1$sbackground%2$s styles'=>'处理%1$s后台%2$s样式','Process %1$simg%2$s tags'=>'处理 %1$simg%2$s 标记','Failed to open file'=>'无法打开文件','Success'=>'成功','Invalid WordPress nonce.  Please reload the page and try again.'=>'无效的 WordPress nonce。 请重新加载页面，然后重试。','Enable it here'=>'在此处启用它','Usage Statistics is collected only when Debug Mode is enabled.'=>'只有启用调试模式时，才会收集使用统计信息。','Enable here'=>'如果这是\'Advanced Custom Fields\'字段，请在此处启用以直接将标签和选项与ACF同步','Items: '=>'项目： ','Evictions: '=>'驱逐： ','APC'=>'装甲运兵车','Hit rate'=>'命中率','Size used (MB)'=>'使用的大小（MB）','Used (%): '=>'使用: ','Evictions/sec: '=>'驱逐/秒： ','Used by '=>'已使用 ','CPU load'=>'CPU负载','CPU load: '=>'CPU 负载： ','CPU load:'=>'处理器负载：','Memory per request (MB)'=>'每次请求的内存（MB）','Memory used: '=>'已使用内存： ','PHP Memory:'=>'PHP内存使用：','Database:'=>'数据库：','Time taken for database activity'=>'数据库活动所需时间','Slowest requests (in debug mode only)'=>'最慢的请求（仅在调试模式下）','Cache flushes: '=>'刷新缓存： ','Calls/period: '=>'通话/期间： ','Fragment Cache:'=>'片段缓存：','Calls'=>'电话','Time taken for ObjectCache activity'=>'对象缓存活动所需的时间','Detailed view (in debug mode only)'=>'详细视图（仅限调试模式）','Calls/sec: '=>'呼叫次数/秒： ','Time taken: '=>'所需时间： ','Flushes/period: '=>'冲洗次数/周期： ','Sets/period: '=>'套装/周期： ','Hit rate: '=>'命中率： ','Hits/period: '=>'点击次数/周期： ','Gets/period: '=>'获取/周期： ','Responded JS compression: '=>'响应 JS 压缩： ','Responded CSS compression: '=>'响应 CSS 压缩： ','Requests/period: '=>'要求/期限： ','JS compression in cache: '=>'缓存中的 JS 压缩： ','CSS compression in cache: '=>'缓存中的 CSS 压缩： ','Files: '=>'文件： ','Used: '=>'使用: ','Time per request (ms)'=>'每个请求的时间（毫秒）','Static time to process (ms): '=>'静态处理时间（毫秒）： ','Static Requests/second: '=>'静态请求数/秒： ','Static Requests/period: '=>'静态请求/周期： ','Dynamic time to process (ms): '=>'动态处理时间（毫秒）： ','Dynamic Requests/second: '=>'动态请求数/秒： ','Dynamic Requests/period: '=>'动态请求/周期： ','Access Log:'=>'访问日志：','Requests handled by PHP'=>'由 PHP 处理的请求','Third Party'=>'第三方','Query String'=>'请求参数','Logged In'=>'登录','Cache Fill'=>'缓存填充','W3TC Configuration'=>'W3TC 配置','API call'=>'应用程序接口调用','Not cached'=>'未缓存','Requests/period'=>'请求/期间','PHP Requests:'=>'PHP 请求：','Request time'=>'请求时间','Size used: '=>'使用的尺寸： ',' ms'=>' 毫秒','Avg processing time: '=>'平均处理时间： ','Cache hit rate: '=>'缓存命中率： ','Cache hits: '=>'缓存命中： ','Requests/sec: '=>'请求/秒： ','Requests: '=>'请求： ','Entries: '=>'条目： ','Cache size: '=>'缓存大小： ','No data collected yet'=>'尚未收集任何数据','An error occurred'=>'发生错误','Page Cache Reject Requests for '=>'页面缓存拒绝以下请求 ','Object Cache Calls'=>'对象缓存调用','Reject reasons: '=>'拒绝理由： ','Period'=>'周期','Database Queries'=>'数据库查询','&lt; Back To Statistics'=>'&lt; 返回统计','error'=>'错误','%1$s%2$sW3 Total Cache Error:%3$s Files and directories could not be automatically removed to complete the deactivation. %4$sPlease execute commands manually:%5$s%6$s%7$s%8$s'=>'%1$s%2$sW3 Total Cache Error:%3$s 无法自动删除文件和目录以完成停用。%4$s请手动执行命令：%5$s%6$s%7$s%8$s','Please %1$snetwork activate%2$s W3 Total Cache when using WordPress Multisite.'=>'请在使用WordPress Multisite时%1$s网络激活%2$sW3总缓存。','Purges Log'=>'清除日志','I Understand the Risks'=>'我了解风险','Once %1$sCSS%2$s is optimized, try %3$sJS%4$s minification. If auto mode doesn\'t work for
							you, be sure to check the web browsers error console to quickly confirm that the optimization
							isn\'t working. If the JavaScript is working, you can either make additional optimizations for
							user experience like experimenting with embed locations etc or further reducing file size etc.
							However, if you\'re having errors try the "combine only" option and if that still generates
							errors, there are bugs in the code of your theme or plugins or both that prevent minification
							of %5$sJS%6$s from working automatically.'=>'优化%1$sCSS%2$s后，请尝试%3$sJS%4$s缩小。如果自动模式不适用于
							您，请务必检查Web浏览器错误控制台以快速确认优化
							不起作用。如果JavaScript正在工作，您可以对
							用户体验，如尝试嵌入位置等或进一步减小文件大小等。
							但是，如果您遇到错误，请尝试“仅合并”选项，如果仍然生成
							错误，主题或插件的代码中存在错误，或两者兼而有之，可以防止缩小
							的 %5$sJS%6$s 自动工作。','Start with minify for your %1$sCSS%2$s using auto mode first. If you have any issues at that step,
							contact your developer(s) and report a bug. They should be able to point you in the right
							direction or correct the issue in a future update.'=>'从缩小%1$sCSS%2$s开始，首先使用自动模式。如果您在该步骤中遇到任何问题，
							联系您的开发人员并报告错误。他们应该能够指出您的正确位置
							在将来的更新中指导或更正问题。','Still want to get started? Now for the Pro\' tips:'=>'还想入门吗？现在来看看专业人士的建议：','The interactions and dependencies of %1$sCSS%2$s or %3$sJS%4$s on each other can be complex.
							Themes and plugins are typically created by various developers and can be combined in
							millions of combinations. As a result, W3 Total Cache cannot take all of those nuances into
							account, it just does the operation and let\'s you tune to what degree it does it, it
							doesn\'t "validate" the result or know if it\'s good or bad; a human must do that.'=>'%1$sCSS%2$s或%3$sJS%4$s之间的相互作用和依赖关系可能很复杂。
							主题和插件通常由各种开发人员创建，可以组合在
							数以百万计的组合。因此，W3 总缓存无法将所有这些细微差别纳入
							帐户，它只是执行操作，并让您调整到它执行的程度，它
							不“验证”结果或知道它是好是坏;人类必须这样做。','Minification is a process of reducing the file size to improve user experience and it requires
					testing in order to get it right &mdash; as such it doesn\'t work for everyone.'=>'缩小是减小文件大小以改善用户体验的过程，它需要
					测试是为了让它正确 - 因此它并不适合所有人。','What is minification exactly?'=>'什么是最小化？','There are lots of reasons why minify cannot work for all sites under all circumstances and they
				have nothing to do with W3 Total Cache: Your site\'s content, your server(s), your plugins and
				your theme are all unique, that means that minify cannot automatically work for everyone.'=>'Minify不能在所有情况下适用于所有站点的原因有很多，并且它们
				与W3总缓存无关：您网站的内容，您的服务器，您的插件和
				您的主题都是独一无二的，这意味着minify无法自动为所有人工作。','an "instant on" or "set it and forget it" optimization technique.'=>'一种“即时开启”或“设置并忘记它”的优化技术。',' not '=>' 不 ','In the best case, the usage of minify optimization is a trial and error process, it\'s'=>'在最好的情况下，使用minify优化是一个试错过程，它是','Hang on!'=>'坚持住！','Our terms of use and privacy policies have been updated. Please %1$sreview%2$s and accept them.'=>'我们的使用条款和隐私政策已更新。请%1$s查看%2$s并接受它们。','The W3 Total Cache license key is not active for this site. You can switch your license to this website following %1$sthis link%2$s'=>'W3 总缓存许可证密钥对于此站点未处于活动状态。您可以在以下%1$s链接下将许可证切换到本网站%2$s','%1$sUpgrade Performance%2$s'=>'%1$s升级性能%2$s','Loading...'=>'加载中...','This tool allows you to modify the URL of Media Library attachments. Use it if the "WordPress address (%1$sURL%2$s)" value has been changed in the past.'=>'此工具允许您修改媒体库附件的URL。如果“WordPress地址（%1$sURL%2$s）”值在过去已更改，请使用它。','Create a list of redirects to %1$sCDN%2$s (hostname specified in hostname field #1.)'=>'创建重定向到 %1$sCDN%2$s的列表（在主机名字段 #1 中指定的主机名）。','The %1$sTTL%2$s of page cache files is set via the "Expires header lifetime" field in the "%3$sHTML%4$s" section on %5$sBrowser Cache%6$s Settings tab.'=>'页面缓存文件的 %1$sTTL%2$s 是通过 %5$sBrowser Cache%6$s 设置选项卡上"%3$sHTML%4$s "部分中的 "Expires header lifetime "字段设置的。','Enable %1$sHTTP%2$s compression in the "%3$sHTML%4$s" section on %5$sBrowser Cache</a> Settings tab.'=>'在%5$s浏览器缓存</a> 设置选项卡的 "%3$sHTML%4$s" 部分启用%1$sHTTP%2$s 压缩。','Return correct Content-Type header for %1$sXML%2$s files (e.g., feeds and sitemaps). Slows down cache engine.'=>'为%1$sXML%2$s文件（例如，源和站点地图）返回正确的内容类型标头。降低缓存引擎的速度。','Always cache %1$sURL%2$ss that use these query string name-value pairs. The value part is not required. But if used, separate name-value pairs with an equals sign (i.e., name=value). Each pair should be on their own line.'=>'始终缓存使用这些查询字符串名称/值对%1$s URL%2$s。值部分不是必需的。但是，如果使用，则使用等号（即 name=value）分隔名称-值对。每对应该在自己的线上。','Significantly reduce the default %1$sTTL%2$s for comment cookies to reduce the number of authenticated user traffic. Enter -1 to revert to default %3$sTTL%4$s.'=>'大幅降低注释 cookie 的默认 %1$sTTL%2$s 以减少验证用户流量。输入 -1 可恢复默认 %3$sTTL%4$s。','If disabled, HEAD requests can often be cached resulting in "empty pages" being returned for subsequent requests for a %1$sURL%2$s.'=>'如果禁用，通常可以缓存 HEAD 请求，从而导致为%1$sURL%2$s的后续请求返回“空页”。','Controls WordPress %1$sREST%2$s %3$sAPI%4$s functionality.'=>'控制WordPress%1$sREST%2$s %3$sAPI%4$s功能。','Disable %1$sREST%2$s %3$sAPI%4$s'=>'禁用 %1$sREST%2$s %3$sAPI%4$s','REpresentational State Transfer'=>'呈现状态转移','%1$sREST%2$s %3$sAPI%4$s'=>'%1$sREST%2$s %3$sAPI%4$s','A %1$scompliant%2$s sitemap can be used to specify the pages to maintain in the primed cache. Pages will be cached according to the priorities specified in the %3$sXML%4$s file.'=>'可以使用 %1$scompliant%2$s 网站地图来指定要在优先缓存中保留的页面。页面将根据 %3$sXML%4$s 文件中指定的优先级进行缓存。','Specify full home %1$sURL%2$ss of your mirrors so that plugin will flush it\'s cache when content is changed. For example:%3$s http://my-site.com%4$shttp://www.my-site.com%5$shttps://my-site.com'=>'指定镜像的完整主%1$sURL%2$s，以便插件在内容更改时刷新其缓存。例如：%3$s http://my-site.com%4$shttp://www.my-site.com%5$shttps://my-site.com','Additional home %1$sURL%2$ss:'=>'其他主页 %1$sURL%2$ss：','Secure Socket Layer'=>'缓存SSL（HTTPS）请求','Cache %1$sSSL%2$s requests (uniquely) for improved performance.'=>'缓存%1$sSSL%2$s请求（唯一）以提高性能。','Specify redis password, when %1$sSASL%2$s authentication used'=>'使用 %1$sSASL%2$s 身份验证时，指定 redis 密码','In miliseconds'=>'以毫秒为单位','In seconds'=>'在几秒钟内','Verify the server\'s certificate when connecting via TLS.'=>'通过 TLS 连接时验证服务器的证书。','Verify TLS Certificates:'=>'验证 TLS 证书：','Multiple servers may be used and seperated by a comma; e.g. 127.0.0.1:6379, domain.com:6379. To use TLS, prefix server with tls://'=>'可以使用多个服务器，并用逗号隔开；例如 127.0.0.1:6379、domain.com:6379。要使用 TLS，请在服务器前加上 tls://','Specify memcached password, when %1$sSASL%2$s authentication used'=>'指定 memcached 密码，当使用%1$sSASL%2$s身份验证','%1$sAvailable when memcached extension installed, built with %2$sSASL%3$s'=>'%1$s安装 memcached 扩展时可用，使用 %2$sSASL 构建%3$s','Simple Authentication and Security Layer'=>'简单身份验证和安全层','Specify memcached username, when %1$sSASL%2$s authentication used'=>'指定 memcached 用户名，当使用%1$sSASL%2$s身份验证','ElastiCache %1$sPHP%2$s module not found'=>'未找到 ElastiCache %1$sPHP%2$s 模块','Time to Live'=>'生活时间','The %1$sTTL%2$s of page cache files is set via the "Expires header lifetime" field in the "Cascading Style Sheets &amp; JavaScript" section on %3$sBrowser Cache%4$s Settings tab.'=>'页面缓存文件的%1$sTTL%2$s是通过“浏览器缓存”选项卡上“层叠样式表和JavaScript”部分中%3$sBrowser缓存%4$s“设置”部分中的“过期标头生存期”字段设置的。','Enable %1$sHTTP%2$s compression in the "Cascading Style Sheets &amp; JavaScript" section on %3$sBrowser Cache%4$s Settings tab.'=>'在%3$s浏览器缓存%4$s设置“选项卡上的”级联样式表和 JavaScript“部分中启用%1$sHTTP%2$s压缩。','Always ignore the specified %1$sCSS%2$s files. Use relative paths. Omit: protocol, hostname, leading forward slash and query strings.'=>'始终忽略指定的 %1$sCSS%2$s 文件。使用相对路径。省略：协议、主机名、前导斜线和查询字符串。','Always ignore the specified %1$sJS%2$s files. Use relative paths. Omit: protocol, hostname, leading forward slash and query strings.'=>'始终忽略指定的%1$sJS%2$s文件。使用相对路径。省略：协议、主机名、前导正斜杠和查询字符串。','For better performance, send files to browser before they are requested when using the %1$sHTTP%2$s/2 protocol.'=>'为了获得更好的性能，请在使用 %1$sHTTP%2$s/2 协议时，在请求文件之前将文件发送到浏览器。','No %1$sCSS%2$s files added'=>'未添加 %1$sCSS%2$s 文件','%1$sCSS%2$s file management:'=>'%1$sCSS%2$s 文件管理：','%1$sCSS%2$s minify settings:'=>'%1$sCSS%2$s缩小设置：','No %1$sJS%2$s files added'=>'未添加%1$sJS%2$s文件','%1$sJS%2$s file management:'=>'%1$sJS%2$s 文件管理：','Before %1$s&lt;/head&gt;%2$s'=>'在 %1$s&lt;/head&gt;%2$s 之前','%1$sJS%2$s minify settings:'=>'%1$sJS%2$s缩小设置：','%1$sHTML%2$s minify settings:'=>'%1$sHTML%2$s 最小化设置：','If disabled, %1$sCSS%2$s and %3$sJS%4$s embeddings will use GET variables instead of "fancy" links.'=>'如果禁用，%1$sCSS%2$s和%3$sJS%4$s嵌入将使用GET变量而不是“花哨”链接。','Best compatibility with %1$sIIS%2$s is realized via %3$sWinCache%4$s opcode cache.'=>'通过 %3$sWinCache%4$s 操作码缓存实现了与 %1$sIIS%2$s 的最佳兼容性。','Additional installation guides can be found in the %1$swiki%2$s.'=>'其他安装指南可以在%1$swiki%2$s中找到。','Alternative PHP Cache'=>'替代 PHP 缓存','Install %1$sAPC%2$s module'=>'安装 %1$sAPC%2$s 模块','Check out the %1$sFAQ%2$s for more details on %3$susage</a>.'=>'请查看 %1$sFAQ%2$s 了解有关 %3$susage</a> 的更多详情。','%1$sOptional:%2$s On the "%3$sUser Agent Groups%4$s" tab, specify any user agents, like mobile phones if a mobile theme is used.'=>'%1$s可选：%2$s 在"%3$s用户代理组%4$s"选项卡上，指定任何用户代理，如使用移动主题的手机。','%1$sRecommended:%2$s If you already have a content delivery network (%3$sCDN%4$s) provider, proceed to the "%5$sContent Delivery Network%6$s" tab and populate the fields and set your preferences. If you do not use the Media Library, you will need to import your images etc into the default locations. Use the Media Library Import Tool on the "Content Delivery Network" tab to perform this task. If you do not have a %7$sCDN%8$s provider, you can still improve your site\'s performance using the "Self-hosted" method. On your own server, create a subdomain and matching %9$sDNS%10$s Zone record; e.g. static.domain.com and configure %11$sFTP%12$s options on the "Content Delivery Network" tab accordingly. Be sure to %13$sFTP%14$s upload the appropriate files, using the available upload buttons.'=>'%1$s推荐：%2$s 如果您已经拥有内容交付网络 （%3$sCDN%4$s） 提供商，请转到“%5$s内容交付网络%6$s”选项卡并填充字段并设置首选项。如果您不使用媒体库，则需要将图像等导入到默认位置。使用“内容交付网络”选项卡上的媒体库导入工具执行此任务。如果您没有%7$sCDN%8$s提供商，您仍然可以使用“自托管”方法提高网站的性能。在您自己的服务器上，创建子域和匹配的%9$sDNS%10$s区域记录;例如，相应地 static.domain.com 并配置“内容交付网络”选项卡上的%11$sFTP%12$s选项。请务必使用可用的上传按钮%13$sFTP%14$s上传相应的文件。','%1$sRecommended:%2$s On the "%3$sBrowser Cache%4$s" tab, %5$sHTTP%6$s compression is enabled by default. Make sure to enable other options to suit your goals.'=>'%1$s推荐：%2$s在“%3$s浏览器缓存%4$s”选项卡上，默认情况下启用%5$sHTTP%6$s压缩。确保启用其他选项以适合您的目标。','%1$sRecommended:%2$s On the "%3$sMinify%4$s" tab all of the recommended settings are preset. Use the help button to simplify discovery of your %5$sCSS%6$s and %7$sJS%8$s files and groups. Pay close attention to the method and location of your %9$sJS%10$s group embeddings. See the plugin\'s %11$s%12$sFAQ%13$s%14$s for more information on usage.'=>'%1$s推荐：%2$s 在"%3$s最小化%4$s"选项卡上，所有推荐设置都已预设。使用帮助按钮可简化对 %5$sCSS%6$s 和 %7$sJS%8$s 文件和组的查找。请密切关注 %9$sJS%10$s 组嵌入的方法和位置。有关使用的更多信息，请参阅插件的 %11$s%12$sFAQ%13$s%14$s 。','If selected, detailed caching information will appear at the end of each page in a %1$sHTML%2$s comment. View a page\'s source code to review.'=>'如果选择了该选项，详细的缓存信息将出现在每个页面末尾的 %1$sHTML%2$s 注释中。查看页面源代码以进行审查。','Detailed information about each cache will be appended in (publicly available) %1$sHTML%2$s comments in the page\'s source code. Performance in this mode will not be optimal, use sparingly and disable when not in use.'=>'有关每个缓存的详细信息将附加在页面源代码的（公开可用）%1$sHTML%2$s注释中。此模式下的性能不会达到最佳状态，请谨慎使用，并在不使用时禁用。','Optimize disk enhanced page and minify disk caching for %1$sNFS%2$s'=>'为 %1$sNFS%2$s 优化磁盘增强页面并精简磁盘缓存','Network File System'=>'Network File System (网络文件系统)','Not recommended for %1$sNFS%2$s systems.'=>'不建议用于%1$sNFS%2$s系统。','Notify of server configuration errors, if this option is disabled, the server configuration for active settings can be found on the %1$sinstall%2$s tab.'=>'通知服务器配置错误，如果禁用此选项，则可以在%1$s安装%2$s选项卡上找到活动设置的服务器配置。','Specify the %1$sSNS%2$s topic.'=>'指定%1$sSNS%2$s主题。','Specify the %1$sAPI%2$s secret.'=>'指定%1$sAPI%2$s机密。','Specify the %1$sAPI%2$s Key.'=>'指定 %1$sAPI%2$s 密钥。','Simple Notification Service'=>'简单通知服务','Access Control List'=>'Access Control List (访问控制表)','Varnish Configuration Language'=>'清漆配置语言','Specify the IP addresses of your varnish instances above. The %1$sVCL%2$s\'s %3$sACL%4$s must allow this request.'=>'指定上述上光油实例的 IP 地址。%1$sVCL%2$s的%3$sACL%4$s必须允许此请求。','Enable %1$sHTTP%2$s compression and add headers to reduce server load and decrease file load time.'=>'启用%1$sHTTP%2$s压缩并添加标头以减少服务器负载并减少文件加载时间。','Object caching greatly increases performance for highly dynamic sites that use the %1$sObject Cache %2$sAPI%3$s%4$s.'=>'对于使用 %1$sObject Cache %2$sAPI%3$s%4$s 的高动态网站，对象缓存可大大提高性能。','Minification can decrease file size of %1$sHTML%2$s, %3$sCSS%4$s, %5$sJS%6$s and feeds respectively by ~10%% on average.'=>'缩小可以使%1$sHTML%2$s，%3$sCSS%4$s，%5$sJS%6$s和提要的文件大小平均减少约10%%。',' (available after upgrade)'=>' （升级后可用）','Select %1$s'=>'选择%1$s','Web Performance Optimization'=>'网页性能优化','Thanks for choosing W3TC as your Web Performance Optimization (%1$sWPO%2$s) framework!'=>'感谢您选择W3TC作为您的Web性能优化（%1$sWPO%2$s）框架！','%1$sThe plugin is currently %2$s in %3$s mode.%4$s'=>'%1$s 插件当前为 %2$s，处于 %3$s模式。%4$s','%1$sPHP%2$s Modules'=>'%1$sPHP%2$s 模块','%1$sCSS%2$s'=>'%1$sCSS%2$s','%1$sJS%2$s'=>'%1$sJS%2$s','eXtensible Markup Language'=>'可扩展标记语言','W3 Total Cache %1$sby W3 EDGE %2$s&reg;%3$s%4$s'=>'W3 Total Cache %1$sby W3 EDGE %2$s®%3$s%4$s','Theme files, media library attachments, %1$sCSS%2$s, %3$sJS%4$s files etc will appear to load instantly for site visitors.'=>'主题文件，媒体库附件，%1$sCSS%2$s，%3$sJS%4$s文件等将立即为网站访问者加载。','If you have already added a %1$sCNAME%2$s to your %3$sDNS%4$s Zone, enter it here.'=>'如果您已将%1$sCNAME%2$s添加到%3$sDNS%4$s区域，请在此处输入。','Enter the hostname provided by Rackspace Cloud Files, this value will replace your site\'s hostname in the %1$sHTML%2$s.'=>'输入 Rackspace Cloud Files 提供的主机名，该值将替换 %1$sHTML%2$s 中的网站主机名。','Enter the hostname or %1$sCNAME%2$s(s) of your %3$sFTP%4$s server configured above, these values will replace your site\'s hostname in the %5$sHTML%6$s.'=>'输入上面配置的%3$sFTP%4$s服务器的主机名或%1$sCNAME%2$s，这些值将替换%5$sHTML%6$s中的站点主机名。','%1$sSFTP%2$s private key:'=>'%1$sSFTP%2$s 私钥：','%1$sSFTP%2$s public key:'=>'%1$sSFTP%2$s 公钥：','Secure Shell'=>'安全 Shell','Use default %1$sSSH%2$s public/private key files'=>'使用默认%1$sSSH%2$s公钥/私钥文件','%1$sFTP%2$s path:'=>'%1$sFTP%2$s 路径：','%1$sFTP%2$s password:'=>'%1$sFTP%2$s密码：','%1$sFTP%2$s username:'=>'%1$sFTP%2$s 用户名：','%1$sFTP%2$s connection:'=>'%1$sFTP%2$s连接：','%1$sFTP%2$s hostname:'=>'%1$sFTP%2$s 主机名：','Use passive %1$sFTP%2$s mode'=>'使用被动 %1$sFTP%2$s 模式','Objects in an S3 bucket served from CloudFront do not need to be publicly accessible. Set this value to disabled to ensure that objects are not publicly accessible and can only be accessed via CloudFront or with a suitable IAM role.'=>'从 CloudFront 提供的 S3 存储桶中的对象不需要可公开访问。将此值设置为禁用，以确保对象不可公开访问，并且只能通过 CloudFront 或使用合适的 IAM 角色进行访问。','Disabled (don\'t apply an ACL)'=>'禁用（不应用 ACL）','Enabled (apply the \'public-read\' ACL)'=>'已启用（应用 "公开读取 "ACL）','Set objects to publicly accessible on upload:'=>'将对象设置为在上传时可公开访问：','If you have already added a %1$s%2$sCNAME%3$s%4$s to your %5$sDNS%6$s Zone, enter it here.'=>'如果您已经在 %5$sDNS%6$s 区域中添加了 %1$s%2$sCNAME%3$s%4$s ，请在此处输入。','AWS Identity and Access Management'=>'AWS Identity and Access Management','We recommend that you use %1$s%2$sIAM%3$s%4$s to create a new policy for %5$sAWS%6$s services that have limited permissions. A helpful tool: %7$s%8$sAWS%9$s Policy Generator%10$s'=>'我们建议您使用 %1$s%2$sIAM%3$s%4$s 为有权限限制的 %5$sAWS%6$s 服务创建新策略。实用工具：%7$s%8$sAWS%9$s 策略生成器%10$s','or %1$sCNAME%2$s:'=>'或%1$sCNAME%2$s：','If using Amazon Web Services or Self-Hosted %1$sCDN%2$s types, enable %3$sHTTP%4$s compression in the "Media &amp; Other Files" section on %5$sBrowser Cache%6$s Settings tab.'=>'如果使用亚马逊网络服务或自托管 %1$sCDN%2$s 类型，请在 %5$s浏览器缓存%6$s 设置选项卡的 "媒体&amp; 其他文件 "部分启用 %3$sHTTP%4$s 压缩。','If using subdomain for %1$sCDN%2$s functionality, this setting helps prevent new users from sending cookies in requests to the %3$sCDN%4$s subdomain.'=>'如果将子域用于%1$sCDN%2$s功能，则此设置有助于防止新用户在请求中向%3$sCDN%4$s子域发送 Cookie。','Set cookie domain to "%1$s"'=>'将 Cookie 域设置为“%1$s”','Specify the path of files that should not use the %1$sCDN%2$s.'=>'指定不应使用 %1$sCDN%2$s 的文件路径。','Specify user agents that should not access files hosted with the %1$sCDN%2$s.'=>'指定不应访问使用 %1$sCDN%2$s 承载的文件的用户代理。','Specify any files outside of theme or other common directories to host with the %1$sCDN%2$s.'=>'指定要使用%1$sCDN%2$s承载的主题或其他公共目录之外的任何文件。','Specify the file types in the active theme to host with the %1$sCDN%2$s.'=>'使用 %1$sCDN%2$s 指定活动主题中要托管的文件类型。','Specify the file types within the WordPress core to host with the %1$sCDN%2$s.'=>'指定 WordPress 核心中要使用%1$sCDN%2$s托管的文件类型。','If %1$sCDN%2$s is enabled (and not using the origin pull method), your minified files will be automatically uploaded.'=>'如果启用了 %1$sCDN%2$s（且未使用原点拉取方法），则会自动上传已精简的文件。','Frequently Asked Questions'=>'常见问题','Access-Control-Allow-Origin'=>'访问控制允许原点','Add %1$sCORS%2$s headers to allow cross-domain assets usage.'=>'添加 %1$sCORS%2$s 标头，允许跨域使用资产。','All Media Library content will use %1$sCDN%2$s links on administration pages.'=>'所有媒体库内容都将在管理页面上使用%1$sCDN%2$s链接。','When %1$sSSL%2$s pages are returned no %3$sCDN%4$s %5$sURL%6$ss will appear in HTML pages.'=>'当返回 %1$sSSL%2$s 页面时，HTML 页面中不会出现 %3$sCDN%4$s %5$sURL%6$s 。','Purge %1$sCDN%2$s only if explicit purge button is clicked.'=>'清除%1$sCDN%2$s仅当单击了显式清除按钮时。','Only purge %1$sCDN%2$s manually'=>'仅手动清除%1$sCDN%2$s','Adds canonical %1$sHTTP%2$s header to assets files.'=>'为资产文件添加规范 %1$sHTTP%2$s 标头。','If checked, any file names or paths specified in the "custom file list" field below will be hosted with the %1$sCDN%2$s.'=>'如果选中，在下面的“自定义文件列表”字段中指定的任何文件名或路径都将与%1$sCDN%2$s一起托管。','If checked, minified %1$sCSS%2$s and %3$sJS%4$s files will be hosted with the %5$sCDN%6$s.'=>'如果选中，经过精简的 %1$sCSS%2$s 和 %3$sJS%4$s 文件将通过 %5$sCDN%6$s 托管。','If checked, all theme file types specified in the "theme file types to upload" field below will be hosted with the %1$sCDN%2$s.'=>'如果选中，在下面的“要上传的主题文件类型”字段中指定的所有主题文件类型都将使用%1$sCDN%2$s托管。','If checked, WordPress static core file types specified in the "wp-includes file types to upload" field below will be hosted with the %1$sCDN%2$s.'=>'如果选中，在下面的“wp-include要上传的文件类型”字段中指定的WordPress静态核心文件类型将与%1$sCDN%2$s一起托管。','If checked, all attachments will be hosted with the %1$sCDN%2$s.'=>'如果选中，所有附件都将使用%1$sCDN%2$s托管。',' objects from the %1$sCDN%2$s if needed.'=>' 如果需要，可从 %1$s CDN %2$s 中获取对象。','Prepare the %1$sCDN%2$s by:'=>'通过以下方式准备%1$sCDN%2$s：','Extensible Markup Language'=>'可扩展标记语言','%1$sHTML%2$s &amp; %3$sXML%4$s'=>'%1$s HTML %2$s &amp; %3$s XML %4$s','%1$sCSS%2$s &amp; %3$sJS%4$s'=>'%1$sCSS%2$s &amp; %3$sJS%4$s','Universal Resource Indicator'=>'通用资源指标','Generate unique %1$sURI%2$s for each file protected from caching by browser.'=>'为受浏览器保护的每个文件生成唯一的%1$sURI%2$s。','Universal Resource Locator'=>'重写对象的URL结构','Rewrite %1$sURL%2$s structure of objects'=>'重写 %1$sURL%2$s 对象结构','Resources with a "?" in the %1$sURL%2$s are not cached by some proxy caching servers.'=>'某些代理缓存服务器不会缓存%1$s URL%2$s中带有“？”的资源。','Enable %1$sHTTP%2$s (brotli) compression'=>'启用 %1$sHTTP%2$s（brotli）压缩','Enable %1$sHTTP%2$s (gzip) compression'=>'启用%1$sHTTP%2$s （gzip） 压缩','Import post attachments directly into the Media Library (and %1$sCDN%2$s)'=>'将帖子附件直接导入媒体库（和%1$sCDN%2$s）','Complete header management including %1$sETags%2$s'=>'完整的标题管理，包括 %1$sETags%2$s','Minification (concatenation and white space removal) of inline, external or 3rd party JavaScript / %1$sCSS%2$s with automated updates'=>'内联、外部或第三方 JavaScript/%1$sCSS%2$s的缩小（串联和空白删除）并具有自动更新','Uniform Resource Identifier'=>'统一资源标识符','Caching of search results pages (i.e. %1$sURI%2$ss with query string variables) in memory or on disk'=>'在内存或磁盘上缓存搜索结果页（即%1$sURI%2$s具有查询字符串变量）','Caching of (minified) %1$sCSS%2$s and JavaScript in memory, on disk or on %3$sCDN%4$s'=>'在内存、磁盘或 %3$sCDN%4$s 中缓存（最小化）%1$sCSS%2$s 和 JavaScript','Transparent content delivery network (%1$sCDN%2$s) integration with Media Library, theme files and WordPress core'=>'透明内容分发网络（%1$sCDN%2$s）与媒体库，主题文件和WordPress核心集成','Bandwidth savings via Minify and %1$sHTTP%2$s compression of %3$sHTML%4$s, %5$sCSS%6$s, JavaScript and feeds'=>'通过 Minify 和 %1$sHTTP%2$s 压缩 %3$sHTML%4$s、%5$sCSS%6$s、JavaScript 和 feeds 来节省带宽','Reduced %1$sHTTP%2$s Transactions, %3$sDNS%4$s lookups and reduced document load time'=>'减少了%1$sHTTP%2$s事务、%3$sDNS%4$s查找并缩短了文档加载时间','Please review the latest %1$sterms of use and privacy policy%2$s, and accept them.'=>'请查看最新的 %1$s 使用条款和隐私政策%2$s，并接受它们。','Secure File Transfer Protocol'=>'安全文件传输协议','(required for Self-hosted (%1$sFTP%2$s) %3$sCDN%4$s %5$sSFTP%6$s support)'=>'（自托管 （%1$sFTP%2$s） %3$sCDN%4$s %5$sSFTP%6$s 支持所必需的）','(required for %1$sCDN%2$s support)'=>'（%1$sCDN%2$s支持需要）','File Transfer Protocol'=>'File Transfer Protocol (文件传输协议)','(required for Self-hosted (%1$sFTP%2$s) %3$sCDN%4$s support)'=>'(自托管（%1$sFTP%2$s）%3$sCDN%4$s 支持所需的内容）','%1$sNot installed/Error/No/False%2$s: Plugin or some functions may not work.%3$s'=>'%1$s未安装/错误/否/错误%2$s：插件或某些功能可能无法正常工作。%3$s','%1$sNot detected/Not available/Off%2$s: May be installed, but cannot be automatically confirmed. Functionality may be limited.%3$s'=>'%1$s未检测到/不可用/关闭%2$s：可能已安装，但无法自动确认。功能可能会受到限制。%3$s','%1$sInstalled/Ok/Yes/True/On%2$s: Functionality will work properly.%3$s'=>'%1$sInstalled/Ok/Yes/True/On%2$s：功能将正常运行。%3$s','Scripts that were not already detected above may require %1$sprofessional consultation%2$s to implement.'=>'上面尚未检测到的脚本可能需要%1$s专业咨询%2$s才能实现。','Uniform Resource Indicator'=>'统一资源指标','User Agent: '=>'用户代理： ','E-mail sent from IP: '=>'从 IP 发送的电子邮件： ','Request data: '=>'请求数据： ','Twitter: '=>'Twitter: ','Name: '=>'名称： ','URL: '=>'网址： ','Version: '=>'版本： ','Request professional %1$ssupport%2$s or troubleshoot issues using the common questions below:'=>'请求专业 %1$s 支持%2$s 或使用以下常见问题排除故障：','%1$sThe following memcached servers are not responding or not running:%2$sThis message will automatically disappear once the issue is resolved.%3$s'=>'%1$s以下 memcached 服务器没有响应或未运行：%2$s一旦问题得到解决，此消息将自动消失。%3$s','Creating and editing'=>'创建和编辑','The required directives for fancy permalinks could not be detected, please confirm they are available: %1$s %2$s'=>'无法检测到花式永久链接所需的指令，请确认它们可用：%1$s %2$s','Preview mode is active: Changed settings will not take effect until preview mode is %1$s or %2$s. %3$sTo preview any changed settings (without deploying): %4$s'=>'预览模式处于激活状态：预览模式为 %1$s 或 %2$s 时，更改的设置才会生效。%3$s预览任何更改的设置（不部署）：%4$s','%1$s is write-able. When finished installing the plugin, change the permissions back to the default: %2$s. Permissions are currently %3$s. %4$s'=>'%1$s是可写的。安装完插件后，将权限更改回默认值：%2$s。权限当前%3$s。%4$s','Available in sites'=>'可提供的场所','Only unauthenticated users will view optimized version of a given page.'=>'只有未经身份验证的用户才能查看特定页面的优化版本。','Don\'t optimize videos for logged in users'=>'不要为登录用户优化视频','Optimize videos delivered using JWPlayer script.'=>'优化使用 JWPlayer 脚本传输的视频。','Optimize JWPlayer'=>'优化 JWPlayer','Optimize videos delivered using &lt;video&gt; HTML tag.'=>'优化使用&lt;video&gt;HTML 标签传输的视频。','Optimize &lt;video&gt; HTML tags'=>'优化 HTML 标记','Swarmify API Key required in order to start optimizing your videos experience'=>'需要 Swarmify API 密钥才能开始优化您的视频体验','Enable this if you want to record the metric and transaction data (until the name is changed using PHP function), specify a value of true for this argument to make the agent send the transaction to the daemon. There is a slight performance impact as it takes a few milliseconds for the agent to dump its data. %1$sFrom %2$sNew Relic PHP API doc%3$s%4$s'=>'如果要记录指标和事务数据（直到使用 PHP 函数更改名称），请启用此参数，请为此参数指定一个值 true，以使代理将事务发送到守护程序。由于代理转储其数据需要几毫秒，因此会对性能产生轻微的影响。%1$s来自 %2$s新的 Relic PHP API 文档%3$s%4$s','Enable this to dynamically set proper application name. (See New Relic %1$sPer-directory settings%2$s for other methods.'=>'启用此选项可动态设置正确的应用程序名称。（有关其他方法，请参阅新建遗物%1$s每个目录设置%2$s。','Hypertext Preprocessor'=>'找不到ElastiCache PHP模块','This enables inclusion of %1$sRUM%2$s when using Page Cache together with Browser Cache gzip or when using Page Cache with Disc: Enhanced'=>'这样，当将页面缓存与浏览器缓存 gzip 一起使用时，或者在将页面缓存与光盘一起使用时，可以包含 %1$sRUM%2$s：增强型','Include %1$sRUM%2$s in compressed or cached pages:'=>'在压缩或缓存的页面中包括%1$sRUM%2$s：','Select user roles that %1$sRUM%2$s should be enabled for:'=>'选择应为其启用%1$sRUM%2$s的用户角色：','Use %1$sRUM%2$s only for following user roles'=>'仅将 %1$sRUM%2$s 用于以下用户角色','Save New Relic settings'=>'保存 New Relic 设置','%1$sRUM%2$s enabled:'=>'%1$sRUM%2$s已启用：','Real User Monitoring'=>'真实用户监控','%1$sRUM%2$s ApDex Threshold:'=>'%1$sRUM%2$s ApDex Threshold：','Application ApDex Threshold:'=>'应用程序 ApDex 阈值：','Alerts enabled:'=>'已启用警报：','Application ID:'=>'应用ID：','New Relic may not be installed or not active on this server. %1$sSign up for a (free) account%2$s. Visit %3$sNew Relic%4$s for installation instructions.'=>'此服务器上可能未安装或未启用 New Relic。%1$s注册一个（免费）账户%2$s。请访问 %3$sNew Relic%4$s 获取安装说明。','Show converted image attachments in the Media Library.'=>'在媒体库中显示转换后的图像附件。','Always'=>'总是','If extension is active'=>'如果分机处于活动状态','Never'=>'从不','Visibility:'=>'可见性：','Secondary navigation'=>'次要导航','Primary navigation'=>'主导航','Pings'=>'Pings','Comments'=>'评论','The Loop'=>'循环','Don\'t use fragment cache with the following hooks and for the specified user roles.'=>'对于以下钩子和指定的用户角色，请勿使用片段缓存。','Genesis extension is currently '=>'创世纪扩展目前是 ','is currently'=>'目前','via'=>'通过','Fragment caching'=>'片段缓存','You have not configured well email, API token / global key or domain'=>'您尚未配置好电子邮件、API 令牌/全局密钥或域','%1$sAPI%2$s token / global key:'=>'%1$sAPI%2$s令牌/全局密钥：','Your CloudFlare API token / global key'=>'您的 CloudFlare API 令牌/全局密钥',' or '=>' 或 ','CloudFlare protects and accelerates websites. %1$sSign up now for free%2$s to get started, or if you have an account simply log in to obtain your %3$sAPI%4$s token / global key from the %5$saccount page%6$s to enter it on the General Settings box that appears after plugin activation. Contact the CloudFlare %7$ssupport team%8$s with any questions.'=>'CloudFlare 为网站提供保护和加速。%1$s立即免费注册%2$s即可开始使用，如果您已有账户，只需登录即可从%5$s账户页面%6$s获取您的%3$sAPI%4$s令牌/全局密钥，并将其输入插件激活后出现的常规设置框中。如有任何问题，请联系 CloudFlare %7$s 支持团队%8$s。','%1$sTLS%2$s Client authentication requires CloudFlare to connect to your origin server using a client certificate'=>'%1$sTLS%2$s 客户端身份验证要求 CloudFlare 使用客户端证书连接到您的源服务器','Enable Crypto %1$sTLS%2$s 1.2 feature for this zone and prevent use of previous versions.'=>'为此区域启用加密%1$sTLS%2$s 1.2 功能，并阻止使用以前的版本。','%1$sTLS%2$s 1.2 only:'=>'仅限 %1$sTLS%2$s 1.2：','Enables or disables %1$sSSL%2$s header.'=>'启用或禁用%1$sSSL%2$s标头。','Security header (%1$sSSL%2$s):'=>'安全标头（%1$sSSL%2$s）：','%1$sSSL%2$s encrypts your visitor\'s connection and safeguards credit card numbers and other personal data to and from your website.'=>'%1$sSSL%2$s加密访问者的连接，并保护与您的网站之间的信用卡号和其他个人数据。','Strict'=>'严格','Full (https everywhere)'=>'完整（https 无处不在）','Flexible (HTTPS to end-user only)'=>'灵活（仅对最终用户使用 HTTPS）','Secure Sockets Layer">'=>'缓存SSL（HTTPS）请求">','%1$sSSL%2$s:'=>'%1$sSSL%2$s：','CloudFlare: %1$sSSL%2$s'=>'CloudFlare: %1$sSSL%2$s','Allows customer to continue to use True Client IP (Akamai feature) in the headers we send to the origin.'=>'允许客户继续在我们发送到源的标头中使用 True Client IP（Akamai 功能）。','True client IP:'=>'真正的客户 IP：','Enable IPv6.'=>'启用 IPv6。','IPv6:'=>'IPv6：','Enable %1$sIP%2$s Geolocation to have CloudFlare geolocate visitors to your website and pass the country code to you.'=>'启用 %1$sIP%2$s 地理定位，让 CloudFlare 对网站访客进行地理定位，并将国家代码传递给您。','%1$sIP%2$s geolocation:'=>'%1$sIP%2$s地理位置：','CloudFlare: %1$sIP%2$s'=>'CloudFlare: %1$sIP%2$s','Max size of file allowed for uploading'=>'允许上传的最大文件大小','Max upload:'=>'最大上载文件大小（Mb）','Advanced protection from Distributed Denial of Service (DDoS) attacks on your website.'=>'提供高级保护，防止网站遭受分布式拒绝服务 (DDoS) 攻击。','Distributed Denial of Service'=>'分布式拒绝服务','Advanced %1$sDDoS%2$s protection:'=>'高级 %1$sDDoS%2$s 保护：','The Web Application Firewall (WAF) examines HTTP requests to your website. It inspects both GET and POST requests and applies rules to help filter out illegitimate traffic from legitimate website visitors.'=>'Web 应用程序防火墙 （WAF） 检查对网站的 HTTP 请求。它检查GET和POST请求，并应用规则来帮助过滤掉来自合法网站访问者的非法流量。','Web application firewall:'=>'Web应用防火墙：','When enabled, Always Online will serve pages from our cache if your server is offline'=>'启用后，如果您的服务器离线，"始终在线 "将从我们的缓存中提供页面','Always online:'=>'总是在线：','Browser Integrity Check is similar to Bad Behavior and looks for common HTTP headers abused most commonly by spammers and denies access to your page. It will also challenge visitors that do not have a user agent or a non standard user agent (also commonly used by abuse bots, crawlers or visitors).'=>'浏览器完整性检查与 "不良行为 "类似，可查找最常被垃圾邮件发送者滥用的常见 HTTP 标头，并拒绝访问您的页面。它还会对没有用户代理或非标准用户代理（也是滥用机器人、爬虫或访客常用的）的访客提出质疑。','Browser integrity check:'=>'浏览器完整性检查：','security profile for your website, which will automatically adjust each of the security settings.'=>'您网站的安全配置文件，它将自动调整每个安全设置。','Under Attack'=>'受到攻击','High'=>'高','Medium'=>'中','Low'=>'低','Strips metadata and compresses your images for faster page load times.'=>'去除元数据并压缩图像，以缩短页面加载时间。','Lossy (Further reduce the size of JPEG files for faster image loading)'=>'有损压缩（进一步减小 JPEG 文件的大小，以加快图像加载速度）','Lossless (Reduce the size of PNG, JPEG, and GIF files - no impact on visual quality)'=>'无损（减小 PNG、JPEG 和 GIF 文件的大小 - 对视觉质量没有影响）','Automatically optimize image loading for website visitors on mobile devices'=>'为移动设备上的网站访客自动优化图片加载','Mirage:'=>'海市蜃楼：','When enabled, the Hotlink Protection option ensures that other sites cannot suck up your bandwidth by building pages that use images hosted on your site.'=>'启用后，“热链接保护”选项可确保其他站点无法通过构建使用站点上托管的图像的页面来占用您的带宽。','Hotlink protection:'=>'热链保护：','CloudFlare will proxy customer error pages on any 502,504 errors on origin server instead of showing a default CloudFlare error page. This does not apply to 522 errors and is limited to Enterprise Zones.'=>'CloudFlare 将在源服务器上的任何 502，504 个错误上代理客户错误页面，而不是显示默认的 CloudFlare 错误页面。这不适用于 522 错误，并且仅限于企业区域。','Enable error pages:'=>'启用错误页：','Automatically redirect visitors on mobile devices to a mobile-optimized subdomain'=>'自动将移动设备上的访客重定向至移动优化子域','Mobile redirect:'=>'移动重定向：','CloudFlare will prefetch any URLs that are included in the response headers.'=>'CloudFlare 将预取响应头中包含的任何 URL。','Prefetch preload:'=>'预取预加载：','CloudFlare may buffer the whole payload to deliver it at once to the client versus allowing it to be delivered in chunks.'=>'CloudFlare可以缓冲整个有效负载以立即将其交付给客户端，而不是允许它以块的形式交付。','Response buffering"'=>'响应缓冲"','Encrypt email adresses on your web page from bots, while keeping them visible to humans. '=>'加密来自机器人的网页上的电子邮件地址，同时保持它们对人类可见。 ','Email obfuscation:'=>'电子邮件混淆：','If there is sensitive content on your website that you want visible to real visitors, but that you want to hide from suspicious visitors, all you have to do is wrap the content with CloudFlare SSE tags.'=>'如果您的网站上有敏感内容，您希望真实访问者可见，但您希望对可疑访问者隐藏，您所要做的就是使用CloudFlare SSE标签包装内容。','Server side exclude:'=>'服务器端排除：','Minify %1$sHTML%2$s content.'=>'最小化 %1$sHTML%2$s 内容。','HyperText Markup Language'=>'HyperText Markup Language (超文本标记语言)','Minify %1$sHTML%2$s:'=>'缩小%1$sHTML%2$s：','Minify %1$sCSS%2$s:'=>'最小化 %1$sCSS%2$s：','Minify JavaScript files.'=>'压缩 JS 文件。','Minify %1$sJS%2$s:'=>'缩小%1$sJS%2$s：','Rocket Loader is a general-purpose asynchronous JavaScript loader coupled with a lightweight virtual browser which can safely run any JavaScript code after window.onload.'=>'Rocket Loader 是一个通用的异步 JavaScript 加载器，它与一个轻量级虚拟浏览器相结合，可以在 window.onload 之后安全地运行任何 JavaScript 代码。','Manual (run when attribute present only)'=>'手动（仅在属性存在时运行）','On (automatically run on the JavaScript resources on your site)'=>'开（在网站上的 JavaScript 资源上自动运行）','Controls how long CloudFlare\'s edge servers will cache a resource before getting back to your server for a fresh copy.'=>'控制 CloudFlare 的边缘服务器在返回服务器获取新副本之前缓存资源的时间。','Edge cache TTL:'=>'边缘缓存 TTL：','Specify how long a visitor is allowed access to your site after successfully completing a challenge (such as a CAPTCHA). After the %1$sTTL%2$s has expired the visitor will have to complete a new challenge.'=>'指定访客成功完成挑战（如验证码）后允许访问网站的时间。在 %1$sTTL%2$s 过期后，访问者必须完成新的挑战。','Challenge %1$sTTL%2$s:'=>'挑战%1$sTTL%2$s：','Browser cache %1$sTTL%2$s (in seconds) specifies how long CloudFlare-cached resources will remain on your visitors\' computers.'=>'浏览器缓存 %1$sTTL%2$s（单位：秒）指定 CloudFlare 缓存的资源在访客计算机上保留的时间。','Time-to-Live'=>'时间生活 （TTL）','Browser cache %1$sTTL%2$s:'=>'浏览器缓存%1$sTTL%2$s：','CloudFlare will treat files with the same query strings as the same file in cache, regardless of the order of the query strings.'=>'CloudFlare 会将具有相同查询字符串的文件视为缓存中的同一文件，而不管查询字符串的顺序如何。','Query string sorting:'=>'查询字符串排序：','How the content is cached by CloudFlare'=>'CloudFlare 如何缓存内容','Simplified (ignore the query string when delivering a cached resource)'=>'简化（传递缓存资源时忽略查询字符串）','Basic (cache most static resources (i.e., css, images, and JavaScript)'=>'基本（缓存大多数静态资源（即css，图像和JavaScript）','Aggressive (cache all static resources, including ones with a query string)'=>'进取型（缓存所有静态资源，包括带查询字符串的资源）','Development Mode temporarily allows you to enter development mode for your websites if you need to make changes to your site. This will bypass CloudFlare\'s accelerated cache and slow down your site, but is useful if you are making changes to cacheable content (like images, css, or JavaScript) and would like to see those changes right away.'=>'开发模式临时允许您在需要对网站进行更改时进入网站的开发模式。这将绕过CloudFlare的加速缓存并减慢您的网站速度，但是如果您要对可缓存内容（如图像，css或JavaScript）进行更改并希望立即看到这些更改，这将非常有用。','Exclusion achieved by adding data-cfasync="false" to script tags.'=>'通过在脚本标记中添加 data-cfasync="false" 实现排除。','Exclude minified JS files from being processed by Rocket Loader:'=>'从 Rocket Loader 处理中排除缩小的 JS 文件：','Flush CloudFlare on Post Modifications:'=>'在后期修改上刷新 CloudFlare：','How many minutes data retrieved from CloudFlare: should be stored. Minimum is 1 minute.'=>'从 CloudFlare 获取的数据应存储多少分钟。最少为 1 分钟。','Last month'=>'上个月','Last week'=>'上周','Last 24 hours'=>'最近24小时','Last 12 hours'=>'最近 12 小时','Last 6 hours'=>'最近 6 小时','Last 30 minutes'=>'最后 30 分钟','CloudFlare not available: '=>'CloudFlare 不可用： ','Authenticate your account in order to access settings.'=>'验证您的账户，以便访问设置。','CloudFlare extension is currently '=>'CloudFlare扩展目前是 ','Enable when you have html pages cached on CloudFlare level.'=>'在 CloudFlare 级别缓存 HTML 页面时启用。','Flush CloudFlare on Post Modifications'=>'在后期修改上刷新 CloudFlare','How many minutes data retrieved from CloudFlare should be stored. Minimum is 1 minute.'=>'从CloudFlare检索到的数据应该存储多少分钟。最小值为 1 分钟。','If AMP page URLs are tag based (/my-page/amp/) or query string based (/my-page?amp)'=>'如果 AMP 页面 URL 是基于标签 (/my-page/amp/) 还是基于查询字符串 (/my-page?amp)','Postfix to page titles'=>'对页面标题的后缀','Check if you want to add postfix to each post title.'=>'检查是否要在每个帖子标题中添加后缀。','Add postfix to page titles'=>'向页面标题添加后缀','Failed to select database'=>'未能选择数据库','Failed to connect to mysql server'=>'连接 mysql 服务器失败','To obtain API key you can %1$s, log in, and paste the key in above field.'=>'要获取 API 密钥，您可以%1$s、登录并将密钥粘贴到上面的字段中。','Site was successfully configured.'=>'网站已成功配置。','%1$sCDN%2$s %3$sHTTPS%4$s %5$sCNAME%6$s:'=>'%1$sCDN%2$s %3$sHTTPS%4$s %5$sCNAME%6$s：','This website domain has to be %1$sCNAME%2$s pointing to this %3$sCDN%4$s domain for %5$sHTTP%6$s requests'=>'该网站域名必须是 %1$sCNAME%2$s 指向该 %3$sCDN%4$s 域名，以处理 %5$sHTTP%6$s 请求','HyperText Transfer Protocol'=>'超文本传输协议','If you\'re an existing StackPath customer, enable CDN and Authorize. If you need help configuring your CDN, we also offer Premium Services to assist you.'=>'如果您是 StackPath 的现有客户，请启用 CDN 和授权。如果您在配置 CDN 时需要帮助，我们还提供高级服务来帮助您。','Enhance your website Performance with StackPath\'s CDN services. StackPath works magically with W3 Total Cache to speed up your site around the world for as little as $10 a month.'=>'使用StackPath的CDN服务增强您的网站性能。StackPath神奇地与W3 Total Cache配合使用，以每月只需$ 10的速度在全球范围内加速您的网站。','Primary %1$sCNAME%2$s:'=>'主要%1$s别名%2$s：','Traffic Type:'=>'交通类型：','Origin protocol:'=>'源站协议：','Origin host:'=>'起源主机：','Application Programming Interface'=>'Application Programming Interface (应用程序编程接口)','%1$sAPI%2$s key:'=>'%1$sAPI%2$s键：','currently set to %1$s%2$s%3$s will be changed to %4$s%5$s%6$s'=>'当前设置为 %1$s%2$s%3$s 将更改为 %4$s%5$s%6$s','Hostname(s) mapped to %1$sCDN%2$s host, this value will replace your site\'s hostname in the %3$sHTML%4$s. You can manage them from RackSpace management console and load here afterwards.'=>'映射到 %1$sCDN%2$s 主机的主机名，该值将取代 %3$sHTML%4$s 中的网站主机名。您可以通过 RackSpace 管理控制台对其进行管理，之后再加载到此处。','Reload %1$sCNAME%2$ss from RackSpace'=>'从 RackSpace 重新加载%1$sCNAME%2$ss','%1$sCDN%2$s host (%3$sCNAME%4$s target):'=>'%1$sCDN%2$s 主机（%3$sCNAME%4$s 目标）：','Purge %1$sCDN%2$s completely'=>'完全清除 %1$sCDN%2$s','Enter the hostname provided by your %1$sCDN%2$s provider, this value will replace your site\'s hostname in the %3$sHTML%4$s.'=>'输入由 %1$sCDN%2$s 提供商提供的主机名，该值将取代 %3$sHTML%4$s 中的网站主机名。','Disabled (always use %1$sHTTP%2$s)'=>'已禁用（始终使用 %1$sHTTP%2$s）','Enabled (always use %1$sSSL%2$s)'=>'已启用（始终使用 %1$sSSL%2$s）','HyperText Transfer Protocol over SSL'=>'通过 SSL 的超文本传输协议','This website domain has to be %1$sCNAME%2$s pointing to this %3$sCDN%4$s domain for %5$sHTTPS%6$s requests'=>'此网站域必须%1$sCNAME%2$s指向此%3$sCDN%4$s域以进行%5$sHTTPS%6$s请求','%1$sCDN%2$s %3$sHTTP%4$s %5$sCNAME%6$s:'=>'%1$sCDN%2$s %3$sHTTP%4$s %5$sCNAME%6$s：','Plugin was successfully configured to use this service.%1$sMake sure you have updated domain DNS records.'=>'插件已成功配置为使用此服务。%1$s确保您更新了域名 DNS 记录。','CDN hostname:'=>'CDN 主机名：','Account Short Name:'=>'账户简称：','API Token:'=>'API令牌：','Enter hostname mapped to %1$sCDN%2$s host, this value will replace your site\'s hostname in the %3$sHTML%4$s.'=>'输入映射到%1$sCDN%2$s主机的主机名，此值将替换%3$sHTML%4$s中站点的主机名。','%1$sCNAME%2$ss to use'=>'要使用的 %1$sCNAME%2$s s','Hypertext Markup Language'=>'超文本标记语言','Hostname provided by your %1$sCDN%2$s provider, this value will replace your site\'s hostname in the %3$sHTML%4$s.'=>'由 %1$sCDN%2$s 提供商提供的主机名，该值将取代 %3$sHTML%4$s 中的网站主机名。','Configure %1$sCNAME%2$ss'=>'配置%1$sCNAME%2$s','Some %1$sCDN%2$s providers may or may not support %3$sSSL%4$s, contact your vendor for more information.'=>'某些%1$sCDN%2$s提供商可能支持也可能不支持%3$sSSL%4$s，请与您的供应商联系以获取更多信息。','%1$sSSL%2$s support:'=>'%1$sSSL%2$s支持：','%1CDN%2 host (%3$sCNAME%4$s target):'=>'%1CDN%2 主机（%3$sCNAME%4$s目标）：','Add new folder:'=>'新建文件夹：','Select the %1$sCDN%2$s type you wish to use.'=>'选择要使用的%1$sCDN%2$s类型。','JavaScript'=>'JavaScript','Theme files, media library attachments, %1$sCSS%2$s, %3$sJS%4$s files etc will quickly for site visitors.'=>'主题文件，媒体库附件，%1$sCSS%2$s，%3$sJS%4$s文件等将快速为网站访问者提供。','%1$sCDN%2$s'=>'%1$s加元%2$s',' will handle'=>' 处理','Add new site: '=>'添加新站点： ','Site:'=>'网站：','Ignore Cache Control:'=>'忽略缓存控制：','Origin IP Resolution:'=>'源 IP 解析度：','IP of your WordPress host'=>'您的WordPress主机的IP','Origin IP:'=>'原产地 IP：','Origin URL:'=>'原生网址：','Select the %1$sCDN%2$s type you wish to use. %3$s'=>'选择要使用的 %1$sCDN%2$s 类型。%3$s','%1$sFSD%2$s %3$sCDN%4$s Type:'=>'%1$s消防处%2$s %3$s%4$s型：','%1$sFSD%2$s %3$sCDN%4$s:'=>'%1$s消防处%2$s %3$scdn%4$s：','If you do not have a %1$sCDN%2$s provider try StackPath. %3$sSign up now to enjoy a special offer!%4$s.'=>'如果您没有%1$sCDN%2$s提供程序请尝试 StackPath。%3$s立即注册以享受特别优惠！%4$s.','Access Secret:'=>'访问的秘密:','Access Key:'=>'访问密钥：','Amazon Web Services'=>'Amazon Web服务','Your %1$sAWS%2$s CloudFront Account Credentials'=>'您的 %1$sAWS%2$s CloudFront 帐户凭据','Forward Host Header:'=>'转发主机标头：','Forward Query String:'=>'前向查询字符串：','Forward Cookies:'=>'转发饼干：','Internet Protocol'=>'互联网协议','Domain Name System'=>'Domain Name System (域名系统)','Create an apex %1$sDNS%2$s record pointing to your WordPress host %3$sIP%4$s.%5$sCloudFront will use this host to mirror your site.%6$sTip: If your real domain name is domain.com, then the host for the apex record should be origin.domain.com with the host %7$sIP%8$s of domain.com, e.g.:'=>'创建指向 WordPress 主机 %3$sIP%4$s 的顶点%1$sDNS%2$s记录。%5$sCloudFront 将使用此主机来镜像您的站点。%6$s提示：如果您的真实域名 domain.com，则顶点记录的主机应与 domain.com 的主机%7$sIP%8$sorigin.domain.com，例如：','Canonical Name'=>'规范名','%1$sCDN%2$s %3$sCNAME%4$s:'=>'%1$scdn%2$s %3$scname%4$s：','Bucket doesn\'t exist: %1$s.'=>'存储桶不存在：%1$s。','One of:'=>'其中之一：','Restricts the %1$sURL%2$ss which can be used as the target of form submissions from a given context.'=>'限制%1$sURL%2$ss，该 url 可用作从给定上下文提交表单的目标。','Cascading Style Sheet'=>'层叠样式表','Specifies valid sources for %1$sCSS%2$s stylesheets.'=>'指定%1$sCSS%2$s样式表的有效源。','Restricts the %1$sURL%2$ss which can be used in a document\'s &lt;base&gt; element.'=>'限制可在文档的&lt;base&gt;元素中使用的%1$sURL%2$s。','The Content Security Policy (%1$sCSP%2$s) header reduces the risk of %3$sXSS%4$s attacks by allowing you to define where resources can be retrieved from, preventing browsers from loading data from any other locations. This makes it harder for an attacker to inject malicious code into your site.'=>'内容安全策略 （%1$sCSP%2$s） 标头允许您定义可以从何处检索资源，从而防止浏览器从任何其他位置加载数据，从而降低%3$sXSS%4$s攻击的风险。这使得攻击者更难将恶意代码注入您的网站。','This optional field can be used to specify a %1$sURL%2$s that clients will send reports to if pin validation failures occur. The report is sent as a POST request with a JSON body.'=>'此可选字段可用于指定%1$s URL%2$s，如果发生引脚验证失败，客户端将向其发送报告。报告作为带有 JSON 正文的 POST 请求发送。','This field is %1$salso required%2$s and represents your backup %3$sSPKI%4$s fingerprint. This pin is any public key not in your current certificate chain and serves as a backup in case your certificate expires or has to be revoked.'=>'此字段%1$s也是必需的%2$s表示备份%3$sSPKI%4$s指纹。此 PIN 是当前证书链中不在任何公钥中，可用作备份，以防您的证书过期或必须吊销。','Subject Public Key Information'=>'主题公钥信息','This field is %1$srequired%2$s and represents a %3$sSPKI%4$s fingerprint. This pin is any public key within your current certificate chain.'=>'此字段%1$s必需%2$s表示%3$sSPKI%4$s指纹。此 PIN 是当前证书链中的任何公钥。','HTTP Public Key Pinning'=>'HTTP 公钥固定','%1$sHTTP%2$s Public Key Pinning (%3$sHPKP%4$s) is a security feature for %5$sHTTP%6$sS websites that can prevent fraudulently issued certificates from being used to impersonate existing secure websites.'=>'%1$sHTTP%2$s 公钥固定 （%3$sHPKP%4$s） 是%5$sHTTP%6$sS 网站的一项安全功能，可以防止欺诈性颁发的证书被用于模拟现有的安全网站。','Cross-Site Scripting'=>'跨站脚本','Transport Layer Security'=>'传输层安全','Secure Sockets Layer'=>'安全套接字协议层','HTTP Strict Transport Security'=>'HTTP严格传输安全性','%1$sHTTP%2$s Strict-Transport-Security (%3$sHSTS%4$s) enforces secure (%5$sHTTP%6$s over %7$sSSL%8$s/%9$sTLS%10$s) connections to the server. This can help mitigate adverse effects caused by bugs and session leaks through cookies and links. It also helps defend against man-in-the-middle attacks.  If there are %11$sSSL%12$s negotiation warnings then users will not be permitted to ignore them.'=>'%1$sHTTP%2$s严格传输安全 （%3$sHSTS%4$s） 通过 %7$sSSL%8$s/%9$sTLS%10$s） 连接到服务器强制实施安全 （%5$sHTTP%6$s。这有助于减轻通过 Cookie 和链接的错误和会话泄漏造成的不利影响。它还有助于防御中间人攻击。 如果存在%11$sSSL%12$s协商警告，则不允许用户忽略它们。','Uniform Resource Locator'=>'统一资源定位符','This setting prevents attacks that are caused by passing session IDs in %1$sURL%2$ss.'=>'此设置可防止因在 %1$sURL%2$ss 中传递会话 ID 而导致的攻击。','Quick Reference Chart'=>'快速参考图表','Hypertext Transfer Protocol'=>'超文本传输协议','%1$sHTTP%2$s security headers provide another layer of protection for your website by helping to mitigate attacks and security vulnerabilities.'=>'%1$sHTTP%2$s安全标头通过帮助缓解攻击和安全漏洞，为您的网站提供另一层保护。','Unable to connect to the filesystem (using %1$s). Please confirm your credentials.  %2$s'=>'无法连接到文件系统（使用 %1$s）。请确认您的凭据：%2$s','%1$sLearn more%2$s.'=>'%1$s了解更多%2$s。','Images queued for conversion.  Progress can be seen in the %1$sMedia Library%2$s.'=>'图片排队等待转换。进度可以在 %1$s 媒体库 %2$s 中看到。','Submitted'=>'已提交','Minified JS Rocket Loader Exclude:'=>'缩小JS不包括：','%1$sThis feature works best in WordPress version 5.8 and higher.  You are running WordPress version %2$s.  Please %3$supdate now%4$s to benefit from this feature.%5$s'=>'%1$s此功能在WordPress 5.8及更高版本中效果最好。您正在运行WordPress版本%2$s。请立即%3$s更新%4$s以从该功能中受益。%5$s','Adds the ability to convert images into the modern WebP format for better performance using our remote API service.'=>'增加了使用我们的远程API服务将图像转换为现代WebP格式以获得更好性能的能力。','Missing converted attachment id.'=>'缺少已转换的附件id。','Invalid API response.'=>'无效的API响应。','File "%1$s" does not exist.'=>'文件“%1$s”不存在。','Missing input post id.'=>'缺少输入帖子id。','Switch to %1$slist mode%2$s for WebP conversions.'=>'切换到%1$s列表模式%2$s进行WebP转换。','All selected optimizations have been reverted.'=>'所有选定的优化都已还原。','Successful: %1$u | Skipped: %2$u | Errored: %3$u | Invalid: %4$u'=>'成功：%1$u |跳过：%2$u |错误：%3$u |无效：%4$u','Submitted %1$u image for processing.'=>'已提交 %1$u 章图片进行处理。','Attachment id: '=>'附件ID： ','Image Service'=>'图片服务','The converted image would be larger than the original; conversion canceled.  %1$sLearn more%2$s.'=>'转换后的图片比原始图片大；转换已取消。%1$s了解更多%2$s。','Refreshing...'=>'刷新...','API error.  Please reload the page to try again,'=>'接口错误。 请重新加载页面以重试，','Failed to retrieve a response.  Please reload the page to try again.'=>'无响应，请重新加载页面重试。','Error'=>'错误','Revert'=>'还原','Reverted'=>'已还原','Reverting...'=>'还原中...','Not converted'=>'未转换','Converted'=>'已转换','Processing...'=>'处理中.....','Sending...'=>'发送中…','Convert'=>'转换','Total Cache Image Service'=>'总缓存图片服务','Settings saved.'=>'设置保存。','Total Cache Image Service has been activated. Now, you can %1$sadjust the settings%2$s or go to the %3$sMedia Library%2$s to convert images to WebP.  %4$sLearn more%2$s.'=>'总缓存映像服务已激活。现在，您可以%1$s调整设置%2$s或转到%3$s媒体库%2$s将图像转换为WebP。%4$s学习更多%2$s。','Media Library'=>'媒体库','%1$sThis extension works best in WordPress version 5.8 and higher.  You are running WordPress version %2$s.  Please %3$supdate now%4$s to benefit from this feature.'=>'%1$s此扩展在WordPress 5.8及更高版本中效果最好。您正在运行WordPress版本%2$s。请立即%3$s更新%4$s以从该功能中获益。','Adds the ability to convert images in the Media Library to the modern WebP format for better performance.'=>'增加了将媒体库中的图像转换为现代WebP格式的功能，以获得更好的性能。','Monthly limit:'=>'每月限额：','Monthly requests:'=>'每月请求：','Hourly limit:'=>'每小时限制：','Hourly requests:'=>'每小时请求：','Image Service API usage:'=>'图像服务API使用：','Refresh'=>'刷新','Unconverted:'=>'未转化：','Not converted:'=>'未转换：','Processing:'=>'处理：','Sending:'=>'发送中：','Converted:'=>'转换：','Total:'=>'总计：','Counts and filesizes by status:'=>'按状态计数和文件大小：','Revert all converted images in the media library.'=>'还原媒体库中所有转换后的图像。','Revert all images:'=>'还原所有图像：','Convert all images in the media library.'=>'转换媒体库中的所有图像。','Convert all images:'=>'转换所有图像：','Tools'=>'工具','Auto-convert images on upload.'=>'上传时自动转换图像。','Auto-convert:'=>'自动将 PnG 转换为 JPEG(损耗)','Image compression type.'=>'图像压缩类型。','Compression type:'=>'压缩类型：','Every Ten Seconds'=>'每十秒','Unlimited'=>'无限','Unknown'=>'未知','Please verify your license key in %1$sGeneral Settings%2$s.'=>'请在%1$s常规设置%2$s中验证您的许可证密钥。','Invalid input image MIME type.'=>'无效的输入图像MIME类型。','Valid image data is required.'=>'需要有效的图像数据。','An image file is required.'=>'需要一个图像文件。','Invalid output image MIME type.'=>'输出图像MIME类型无效。','You reached your monthly limit of %1$d; try again later or %2$supgrade to Pro%3$s for unlimited.'=>'您已达到每月%1$d的限额；请稍后再试，或%2$s升级到Pro%3$s，不受限制。',' or %1$supgrade to Pro%2$s for higher limits'=>'或将%1$s升级到Pro%2$s以获得更高的限制','You reached your hourly limit of %1$d; try again later%2$s.'=>'您达到了%1$d的小时限制；请稍后再试%2$s。','Error: Received a non-200 response code: '=>'错误：收到非200响应代码：','WP Error: '=>'WP错误：','Detection of the below modules may not be possible on all environments. As such "Not detected" means that the environment disallowed detection for the given module which may still be installed/enabled whereas "Not installed" means the given module was detected but is not installed/detected.'=>'并非所有环境都能检测到以下模块。因此，“未检测到”表示环境不允许检测到仍可能安装/启用的给定模块，而“未安装”表示检测到给定模块，但未安装/检测到该模块。','The outer distance off the scrolling area from which to start loading the elements (example: 100px, 10%).'=>'从滚动区域开始加载元素的外部距离（例如：100px，10%）。','Threshold'=>'阈值','Controls whether or not the current document is allowed to use the Navigator.share() of Web Share API to share text, links, images, and other content to arbitrary destinations of user\'s choice, e.g. mobile apps.'=>'控件是否允许当前文档使用导航器。Web share API的 Navigator.share()，用于将文本、链接、图像和其他内容共享到用户选择的任意目的地，例如移动应用程序。','Controls whether the current document is allowed to use the WebVR API. When this policy is disabled, the Promise returned by Navigator.getVRDisplays() will reject with a DOMException. Keep in mind that the WebVR standard is in the process of being replaced with WebXR.'=>'控制当前文档是否允许使用 WebVR API。当此策略被禁用时，导航器退回的承诺。getVR 播放 （） 将拒绝 DOM 例外。请记住，WebVR 标准正在被 WebXR 取代。','Controls whether the current document is allowed to trigger device vibrations via Navigator.vibrate() method of Vibration API.'=>'控制当前文档是否允许通过导航器触发设备振动。 振动 API 方法。','Controls whether the current document is allowed to use Screen Wake Lock API to indicate that device should not turn off or dim the screen.'=>'控制当前文档是否允许使用屏幕唤醒锁 API 表示设备不应关闭或调暗屏幕。','Controls whether the current document is allowed to use the Web Authentication API to retrieve already stored public-key credentials, i.e. via navigator.credentials.get({publicKey: ..., ...}).'=>'控制当前文档是否允许使用 Web 身份验证 API 检索已存储的公钥凭据，即通过导航器.凭据. get（[公共钥匙：...','Controls the availability of mechanisms that enables the page author to take control over the behavior of spatial navigation, or to cancel it outright.'=>'控制使页面作者能够控制空间导航行为或直接取消空间导航的机制的可用性。','Controls whether the current document is allowed to use the Gamepad API. When this policy is disabled, calls to Navigator.getGamepads() will throw a SecurityError DOMException, and the gamepadconnected and gamepaddisconnected events will not fire.'=>'控制当前文档是否允许使用游戏板 API。当此策略被禁用时，呼叫导航器.getGamepad（）将抛出一个安全器 DOM 例外，游戏板连接和游戏板分离事件不会开火。','Controls whether tasks should execute in frames while they\'re outside of the visible viewport.'=>'控制任务是否应在可见视场之外以帧执行。','Controls whether tasks should execute in frames while they\'re not being rendered (e.g. if an iframe is hidden or display: none).'=>'控制任务在未渲染时是否应在帧中执行（例如，如果 i 帧被隐藏或显示：没有）。','Controls whether the use of the Battery Status API is allowed. When this policy is disabled, the Promise returned by Navigator.getBattery() will reject with a NotAllowedError DOMException.'=>'控制是否允许使用Battery Status API。禁用此策略时，Navigator.getBattery()返回的Promise将使用NotAllowedError DOMException拒绝。','Lazy load google map'=>'懒加载谷歌地图','Use %1$sinline%2$s method only when your website has just a few pages'=>'仅在网站只有几个页面时使用 %1$sinline%2$s 方法','Examine the overall performance of caching method backends'=>'检查缓存方法后端的整体性能','Visualize your performance over time with graphs'=>'用图形可视化您的性能','View detailed information about your site’s performance'=>'查看有关您网站性能的详细信息','Users who upgrade to W3 Total Cache Pro will have access to the new Statistics page, which provides an in-depth view of the performance of your site.'=>'升级到 W3 Total Cache Pro 的用户可以访问新的 "统计 "页面，深入了解网站性能。','Format of your access log from webserver configuration.'=>'Web服务器配置中访问日志的格式。','Where your access log is located.'=>'访问日志所在的位置。','Webserver type generating access logs.'=>'生成访问日志的Web服务器类型。','The number of intervals that are represented in the graph.'=>'图表中表示的间隔数。','The duration of time in seconds to collect statistics per interval.'=>'每个间隔收集统计信息的持续时间（秒）。','Store transients in database'=>'将瞬态存储在数据库中','Enable caching for wp-admin requests'=>'为wp管理员请求启用缓存','Minify method:'=>'缩小方法：','Allows policy management to be shared between a dynamic pool of servers. For example, each 
						server in a pool to use opcode caching (which is not a shared resource) and purging is 
						then syncronized between any number of servers in real-time; each server therefore behaves 
						identically even though resources are not shared.'=>'允许在动态服务器池之间共享策略管理。例如，池中的每台服务器使用操作码缓存（不是共享资源），然后在任意数量的服务器之间实时同步清除；因此，即使资源不共享，每个服务器的行为都是相同的。','Preview mode:'=>'预览模式：','Or please share'=>'或者请分享','Vote:'=>'投票：','Secret key not specified.'=>'未指定的秘密密钥。','CloudFront distribution not specified.'=>'未指定云正面分布。','Fix incorrect server document root path.  Uses the WordPress ABSPATH ("%1$s") in place of the current server document root ("%2$s").'=>'修复不正确的服务器文档根路径。 使用 WordPress ABSPATH（"%1$s"）代替当前服务器文档根（"%2$s"）。','Fix document root path'=>'修复文档根路径','Europe (Paris)'=>'欧洲/巴黎','Europe (London)'=>'欧洲（伦敦)','Europe (Ireland)'=>'欧洲（爱尔兰）','Europe (Milan)'=>'欧洲（米兰）','Europe (Stockholm)'=>'欧洲/斯德哥尔摩','Europe (Frankfurt)'=>'欧洲（法兰克福)','China (Beijing)'=>'中国（北京）','Africa (Cape Town)'=>'非洲（开普敦）','How to use minify HTML'=>'如何使用缩小HTML','Choosing a minification method'=>'选择缩小方法','How to use manual minify'=>'如何使用手动缩小','Learn more'=>'了解更多','Minify frequently asked questions'=>'缩小常见问题','%1$sPage Cache%2$s engine set to %1$s%3$s%2$s'=>'%1$s页面%2$s，引擎设置为%1$s%3$s%2$s','Manage cache groups for user agents, referrers, and cookies.'=>'管理用户代理、引用方和 Cookie 的缓存组。','Additional features to extend the functionality of W3 Total Cache, such as Accelerated Mobile Pages (AMP) for Minify and support for New Relic.'=>'扩展 W3 总缓存功能的其他功能，例如用于明化的加速移动页面 （AMP）和支持新遗物。','Persistently store objects to reduce execution time for common operations.'=>'持久地存储对象以减少常用操作的执行时间。','Persistently store data to reduce post, page and feed creation time.'=>'持久存储数据以减少文章、页面和订阅源的创建时间。','Improves PHP performance by storing precompiled script bytecode in shared memory.'=>'通过将预编译的脚本字节码存储在共享内存中来提高PHP性能。','Host static files with a CDN to reduce page load time.'=>'使用CDN托管静态文件，以减少页面加载时间。','Content Delivery Network (CDN)'=>'内容交付网络（CDN）','Defer loading offscreen images, making pages load faster.'=>'延迟加载屏幕外图像，使页面加载速度更快。','Reduce load time by decreasing the size and number of CSS and JS files.'=>'通过减少CSS和JS文件的大小和数量来减少加载时间。','Page caching decreases the website response time, making pages load faster.'=>'页面缓存减少了网站响应时间，使页面加载更快。','Purge Logs provide information on when your cache has been purged and what triggered it. If you are troubleshooting an issue with your cache being cleared, Purge Logs can tell you why.'=>'清除日志提供有关何时清除缓存以及是什么触发缓存的信息。如果您正在解决缓存被清除的问题，清除日志可以告诉您原因。','Purge Logs'=>'清除日志','Analytics for your WordPress and Server cache that allow you to track the size, time and hit/miss ratio of each type of cache, giving you the information needed to gain maximum performance.'=>'WordPress和服务器缓存的分析，允许您跟踪每种类型缓存的大小、时间和命中/未命中率，为您提供获得最大性能所需的信息。','Save server resources or add scale and performance by caching the WordPress Rest API with W3TC Pro.'=>'通过使用W3TC Pro缓存WordPress Rest API来节省服务器资源或增加规模和性能。','Rest API Caching'=>'Rest API缓存','Unlocking the fragment caching module delivers enhanced performance for plugins and themes that use the WordPress Transient API.'=>'解锁片段缓存模块为使用WordPress Transient API的插件和主题提供了增强的性能。','Improve the performance of your Genesis, WPML powered site, and much more. StudioPress\' Genesis Framework is up to 60% faster with W3TC Pro.'=>'提高Genesis、WPML驱动的站点的性能，等等。使用W3TC Pro，StudioPress的Genesis框架速度提高了60%。','Extension Framework'=>'扩展框架','Render blocking CSS delays a webpage from being visible in a timely manner. Eliminate this easily with the click of a button in W3 Total Cache Pro.'=>'渲染阻止CSS延迟网页及时可见。只需点击W3 Total Cache Pro中的一个按钮即可轻松消除此问题。','Eliminate Render Blocking CSS'=>'消除渲染阻塞CSS','Provide the best user experience possible by enhancing by hosting HTML pages and RSS feeds with (supported) CDN\'s high speed global networks.'=>'通过使用（支持的）CDN的高速全球网络托管HTML页面和RSS提要，尽可能提供最佳的用户体验。','Full Site Delivery via CDN'=>'通过CDN进行全站交付','Defer loading offscreen Google Maps, making pages load faster.'=>'推迟加载屏幕外的谷歌地图，使页面加载速度更快。','More info'=>'更多信息','Launch'=>'启动','The Setup Guide wizard quickly walks you through configuring W3 Total Cache.'=>'"设置指南"向导可快速引导您完成配置 W3 总缓存。','Setup Guide Wizard'=>'设置指南向导','Are you sure that you want to leave this page?'=>'您确定要离开此页面吗？','PRO FEATURE'=>'高级特色功能','Page Caching using %1$s%2$s%3$s'=>'使用页面缓存%1$s%2$s%3$s','Cache Groups'=>'缓存组','Feature Showcase'=>'功能展示','We\'re here to help you!  Visit our %1$sSupport Center%2$s for helpful information and to ask questions.'=>'我们是来帮你的！请访问我们的%1$s支持中心%2$s以获取有用信息并提出问题。','Need help?'=>'需要帮助？','Please visit %1$sGeneral Settings%2$s to learn more about these features.'=>'请访问%1$s常规设置%2$s以了解有关这些功能的更多信息。','Your website\'s performance can still be improved by configuring %1$sminify%2$s settings, setting up a %1$sCDN%2$s, and more!'=>'通过配置%1$sminify%2$s设置、设置%1$sCDN%2$s等方式，您的网站性能仍然可以得到提升!','What\'s Next?'=>'下一步是什么？','%1$sLazy Load%2$s images? %1$s%3$s%2$s'=>'%1$s延迟加载%2$s图像？%1$s%3$s%2$s','%1$sBrowser Cache%2$s headers set for JavaScript, CSS, and images? %1$s%3$s%2$s'=>'为JavaScript，CSS和图像设置了%1$s浏览器缓存%2$s标头吗？ %1$s %3$s %2$s','%1$sObject Cache%2$s engine set to %1$s%3$s%2$s'=>'%1$s对象缓存%2$s引擎设置为%1$s%3$s%2$s','UNKNOWN'=>'未知','%1$sDatabase Cache%2$s engine set to %1$s%3$s%2$s'=>'%1$s数据库缓存%2$s引擎设置为%1$s%3$s%2$s','%1$sTime to First Byte%2$s has changed by %1$s%3$s%2$s'=>'%1$s到第一个字节%2$s时间已按%1$s%3$s%2$s','Setup Complete!'=>'安装完成！','Pages containing images and other objects can have their load time reduced by deferring them until they are needed.  For example, images can be loaded when a visitor scrolls down the page to make them visible.'=>'包含图像和其他对象的页面可以通过将其延迟到需要时才加载来缩短加载时间。
例如，当访问者向下滚动页面以使其可见时，可以加载图像。','Cache-Control Header'=>'缓存控制标题','Setting'=>'设置','Test Browser Cache'=>'测试浏览器缓存','To improve %1$sBrowser Cache%2$s, we recommend enabling %1$sBrowser Cache%2$s.'=>'为了改善%1$s浏览器缓存%2$s，我们建议启用%1$s浏览器缓存%2$s。','The %1$sCache-Control%2$s header tells your browser how it should cache specific files.  The %1$smax-age%2$s setting tells your browser how long, in seconds, it should use its cached version of a file before requesting an updated one.'=>'%1$s Cache-Control %2$s标头告诉您的浏览器应如何缓存特定文件。 %1$s max-age %2$s设置告诉您的浏览器在请求更新文件之前，应使用文件的缓存版本多长时间（以秒为单位）。','can help ensure browsers are properly caching your assets.'=>'有助于确保浏览器正确缓存您的资源。','To render your website, browsers must download many different types of assets, including javascript files, CSS stylesheets, images, and more.  For most assets, once a browser has downloaded them, they shouldn\'t have to download them again.'=>'要渲染您的网站，浏览器必须下载许多不同类型的资源，包括javascript文件，CSS样式表，图像等。对于大多数资源，一旦浏览器下载了它们，就不必再次下载它们。','Test Object Cache'=>'测试对象缓存','can help you speed up dynamic pages by persistently storing objects.'=>'可以通过持久存储对象来帮助您加快动态页面的速度。','WordPress caches objects used to build pages, but does not reuse them for future page requests.'=>'WordPress会缓存用于构建页面的对象，但不会将其用于将来的页面请求。','By default, this feature is disabled.  We recommend using Redis or Memcached, otherwise leave this feature disabled as the server database engine may be faster than using disk caching.'=>'默认情况下，此功能处于禁用状态。我们建议使用Redis或memcached，否则请禁用此功能，因为服务器数据库引擎可能比使用磁盘缓存更快。','Testing'=>'测试','Many database queries are made in every dynamic page request.  A database cache may speed up the generation of dynamic pages.  Database Cache serves query results directly from a storage engine.'=>'在每个动态页面请求中都会进行许多数据库查询。数据库缓存可以加快动态页面的生成。数据库缓存直接从存储引擎提供查询结果。','Time (ms)'=>'时间（女士）','Storage Engine'=>'储存引擎','Select'=>'选择','Test URL:'=>'测试网址：','Measuring'=>'测量','Test Page Cache'=>'测试页缓存','We\'ll test your homepage with Page Cache disabled and then with several storage engines.  You should review the test results and choose the best for your website.'=>'我们将在禁用页面缓存的情况下测试您的主页，然后使用几个存储引擎进行测试。您应该检查测试结果，并选择最适合您网站的测试结果。','Time to First Byte'=>'到第一个字节的时间','can help you speed up'=>'可以帮助您加快速度','The time it takes between a visitor\'s browser page request and receiving the first byte of a response is referred to as %1$sTime to First Byte%2$s.'=>'从访问者的浏览器页面请求到收到响应的第一个字节之间的时间称为%1$s到第一个字节%2$s。','By allowing us to collect data about how W3 Total Cache is used, we can improve our features and experience for everyone. This data will not include any personally identifiable information.  Feel free to review our %1$sterms of use and privacy policy%2$s.'=>'通过允许我们收集有关W3 Total Cache使用情况的数据，我们可以为每个人改进我们的功能和体验。这些数据将不包括任何个人身份信息。 请随时查看我们的%1$s使用条款和隐私政策%2$s。','If you prefer to configure the settings on your own, you can %1$sskip this setup guide%2$s.'=>'如果您希望自己配置设置，可以%1$s跳过此设置指南%2$s。','provides many options to help your website perform faster.  While the ideal settings vary for every website, there are a few settings we recommend that you enable now.'=>'提供了许多选项来帮助您的网站执行速度。 虽然每个网站的理想设置各不相同，但我们建议您现在就启用一些设置。','You have selected the Performance Suite that professionals have consistently ranked #1 for options and speed improvements.'=>'您已经选择了专业人员一致认为在选项和速度改进方面排名第一的性能套件。','Welcome to the W3 Total Cache Setup Guide!'=>'欢迎使用W3 Total Cache安装指南！','More Caching Options'=>'更多缓存选项','Lazy Load'=>'延迟加载','Welcome'=>'欢迎','Not Enabled'=>'未启用','Unavailable'=>'不可用','Could not update configuration.  Please reload the page to try again or click skip button to abort the setup guide.'=>'无法更新配置。请重新加载页面重试，或单击跳过按钮中止安装指南。','Could not perform this test.  Please reload the page to try again or click skip button to abort the setup guide.'=>'无法执行此测试。请重新加载页面重试，或单击跳过按钮中止安装指南。','Testing complete.  Click Next to advance to the section and see the results.'=>'测试完成。单击“下一步”前进到该部分并查看结果。','Not present'=>'不存在','Requested cache storage engine is invalid'=>'请求的缓存存储引擎无效','Requested cache storage engine is not available'=>'请求的缓存存储引擎不可用','Settings not updated'=>'设置未更新','Settings updated'=>'设置更新','Invalid choice'=>'无效选择','Security violation'=>'违反安全规定','DASHBOARD'=>'仪表盘','NEXT'=>'下一个','PREVIOUS'=>'上一个','SKIP'=>'跳过','Setup Guide'=>'安装指南','By allowing us to collect data about how W3 Total Cache is used, we can improve our features and experience for everyone. This data will not include any personally identifiable information.%1$sFeel free to review our %2$sterms of use and privacy policy%3$s.'=>'通过允许我们收集有关W3 Total Cache使用情况的数据，我们可以为每个人改进我们的功能和体验。这些数据将不包括任何个人身份信息。%1$s请随时查看我们的%2$s使用条款和隐私政策%3$s。','Minify engine settings:'=>'缩小引擎设置：','Minify only'=>'仅缩小','Combine & Minify'=>'合并并缩小','(required for gzip compression support)'=>'（需要gzip压缩支持）','Edit file <strong>%s</strong> and replace all lines between and including <strong>%s</strong> and <strong>%s</strong> markers with:'=>'编辑文件<strong>%s</strong>，并将<strong>%s</strong>和<strong>%s</strong>标记之间的所有行替换为：','Report - 30 days'=>'报告-30天','or use FTP form to allow <strong>W3 Total Cache</strong> make it automatically.'=>'或者使用FTP上传允许<strong>W3 Total Cache</strong>自动生成。','Please execute commands manually'=>'请手动执行命令','<strong>W3 Total Cache Error:</strong> Files and directories could not be automatically deleted.'=>'<strong>W3 Total Cache 错误:</strong> 文件和目录无法被自动删除。','Create the <strong>%s</strong> file and paste the following text into it: <textarea>%s</textarea> <br />'=>'创建<strong>%s</strong>文件并且粘贴下面文本内容到文件中：<textarea>%s</textarea><br />','New customer? Sign up now to speed up your site!'=>'新用户？现在就注册给你的网站加速！','Static files cache successfully emptied.'=>'静态缓存文件已经成功被清除。','Statistics'=>'统计','Disable statistics'=>'禁用统计','W3 Total Cache: Statistics collection is currently enabled. This consumes additional resources, and is not recommended to be run continuously. %s %s'=>'W3 Total Cache：当前已启用统计信息收集。这会消耗额外的资源，并且不建议连续运行。%s%s','Discard invalid selectors'=>'丢弃无效的选择器','Remove space before !important'=>'删除前面的空格！重要','Never minify the following <acronym title="Cascading Style Sheet">CSS</acronym> files:'=>'永远不要缩小以下<acronym title="级联样式表">CSS</acronym>文件：','Never minify the following <acronym title="JavaScript">JS</acronym> files:'=>'永远不要缩小以下<acronym title="JavaScript">JS</acronym>文件：','Select distribution to use'=>'选择要使用的发行版','CloudFlare: Protection'=>'CloudFlare：保护','Images polishing:'=>'图像修改：','CloudFlare: Image Processing'=>'CloudFlare：图像处理','CloudFlare: Content Processing'=>'CloudFlare：内容处理','Cache level:'=>'缓存级别：','CloudFlare: Caching'=>'CloudFlare: 正在缓存','Page caching:'=>'页面正在缓存中:','Widget statistics interval:'=>'小工具统计间隔:','Zone:'=>'时区:','Purge CloudFlare cache'=>'清除CloundFlare缓存','Credentials'=>'凭据','Sign Up Now '=>'马上注册','Application monitoring has detected that your page load time is higher than 300ms. It is recommended that you enable the following features: %s %s'=>'应用的监控程序发现你的页面加载时间超过300毫秒。建议您启用下面的特性：%s %s','Extension <strong>%s</strong> has been successfully activated.'=>'扩展<strong>%s</strong>已成功激活。','default-src:'=>'默认目录：','sandbox:'=>'沙盒:','frame-ancestors:'=>'父框架:','form-action:'=>'表单操作：','plugin-types:'=>'插件类型：','object-src:'=>'对象文件目录:','media-src:'=>'媒体文件目录:','img-src:'=>'图片目录:','style-src:'=>'style-src:','script-src:'=>'script-src:','font-src:'=>'font-src:','connect-src:'=>'内容目录:','frame-src:'=>'frame-src:','base-uri:'=>'基本 URI：','Content Security Policy'=>'内容安全协议','Referrer Policy'=>'推荐人政策','Report Mode Only:'=>'仅报告模式:','Extra Parameters:'=>'额外参数:','Public Key (Backup):'=>'公钥(备份):','Public Key:'=>'公钥:','<acronym title="Hypertext Transfer Protocol">HTTP</acronym> Public Key Pinning'=>'<acronym title="超文本传输协议">HTTP </acronym>公钥固定','X-Content-Type-Options'=>'X-Content-Type-Options','X-<acronym title="Cross-Site Scripting">XSS</acronym>-Protection'=>'X-<acronym title="跨站脚本">XSS</acronym>-防护','X-Frame-Options'=>'X-Frame选项','Directive:'=>'指令:','<acronym title="Hypertext Transfer Protocol">HTTP</acronym> Strict Transport Security policy'=>'<acronym title="Hypertext Transfer Protocol">HTTP</acronym> 严格传输安全策略','Use cookies to store session IDs:'=>'使用Cookie来存储会话ID：','Send session cookies only to secure connections:'=>'仅向安全连接发送会话Cookie：','Access session cookies through the <acronym title="Hypertext Transfer Protocol">HTTP</acronym> only:'=>'仅通过 <acronym title="Hypertext Transfer Protocol">HTTP</acronym> 访问会话cookies：','Enable <acronym title="Hypertext Transfer Protocol">HTTP</acronym> (brotli) compression</label>'=>'启用 <acronym title="Hypertext Transfer Protocol">HTTP</acronym> (brotli) 压缩</label>','Activating the <a href="%s">WPML</a> extension for W3 Total Cache may be helpful for your site. %s%s'=>'正在为 W3 Total Cache 激活 <a href="%s">WPML</a> 扩展也许能给您的网站提供帮助. %s%s','Localization is a type of personalization that makes websites more difficult to scale. This extension reduces the response time of websites localized by WPML.'=>'本地化是个性化的一种类型，使网站更难扩展。这个扩展可以减少被WPML本地化的网站的响应时间。','Improve the caching performance of websites localized by WPML.'=>'提高WPML本地化网站的缓存性能。','Improves page caching interoperability with WPML.'=>'提高与WPML的页面缓存互操作性。','W3 Total Cache\'s Page caching cannot work effectively when WPML Language URL format is "Language name added as a parameter" used. Please consider another URL format. Visit the WPML -&gt; Languages settings. %s'=>'当使用WPML语言URL格式为 "语言名称作为参数添加 "时，W3 Total Cache的页面缓存无法有效工作。请考虑其他URL格式。访问WPML -&gt; 语言设置。%s','PageCache Priming triggered successfully.'=>'已成功触发页面缓存启动。','PageCache Priming did failed: %s'=>'页面缓存启动失败：%s','PageCache Garbage cleanup failed: %s'=>'页面缓存垃圾清理失败：%s','Configuration successfully imported.'=>'配置已成功导入。','Config import failed: '=>'配置导入失败：','<operation> parameter is not specified'=>'<operation> 路径未指定','Option value update failed.'=>'选项值更新失败。','Option updated successfully.'=>'选项已成功更新。','<value> parameter '=>'<value> 参数','<value> parameter is not specified'=>'<value> 参数未指定','Unknown type '=>'未知类型','<name> parameter is not specified'=>'<name> 参数未指明','Not specified what to flush'=>'未指定要清洗的内容','Posts/pages flushed successfully.'=>'帖子/页面成功被清洗。','Flushing posts/pages failed.'=>'清洗帖子/页面失败。','Everything flushed successfully.'=>'所有清洗成功。','Flushing all failed.'=>'所有清洗失败。','Environment adjusted.'=>'环境调整。','Environment adjustment failed with error'=>'环境调整失败，出现错误','Use specific settings'=>'使用特定设置','Use common settings'=>'使用通用设置','Save Settings & Purge Caches'=>'保存设置和清除缓存','Access Log Format:'=>'访问日志格式：','Access Log Filename:'=>'访问日志文件名:','Webserver:'=>'网站服务:','Enable collecting statistics from an Access Log.  This provides much more precise statistics.'=>'启用从访问日志收集统计数据。这能够提供更精确的统计数据。','Parse server access log'=>'解析服务器访问日志','Collect CPU usage'=>'收集CPU使用率','Use the system reported averages of CPU resource usage.'=>'使用系统报告的CPU资源平均使用率。','Slots collected:'=>'收集槽:','Slot time (seconds):'=>'槽时间（秒）：','Use the caching statistics to compare the performance of different configurations like caching methods, object lifetimes and so on. Did you know that we offer premium support, customization and audit services? %1$sClick here for more information%2$s.'=>'使用缓存统计信息来比较不同配置的性能，比如缓存方法、对象生存期等等。您知道我们提供高级支持、定制和审计服务吗? %1$s 点击这里获取更多信息 %2$s。','Some statistics are available directly on your Performance Dashboard, however, the comprehensive suite of statistics are available on the Statistics screen. Web server logs created by Nginx or Apache can be analyzed if accessible.'=>'一些统计信息可以直接在性能仪表盘上获得。然而，在统计信息面板上可以获得更多可用的统计信息。如果可以访问，Nginx或Apache创建的Web服务器日志可以被分析。','Statistics provides near-complete transparency into the behavior of your caching performance, allowing you to identify opportunities to further improve your website speed and ensure operations are working as expected. Includes metrics like cache sizes, object lifetimes, hit vs miss ratio, etc across every caching method configured in your settings.'=>'统计数据几乎完全透明地显示了缓存性能的行为，允许您确定进一步提高网站速度的机会，并确保操作按预期进行。在设置中配置的每个缓存方法中，包括缓存大小、对象生存时间、命中率和未命中率等指标。','Enable statistics collection. Note that this consumes additional resources and is not recommended to be run continuously.'=>'启用统计信息收集。请注意，这会消耗额外的资源，并且不建议连续运行。','Cache usage statistics'=>'缓存使用情况统计','Clear Log'=>'清除日志','Example extension'=>'示例扩展','Optimize your video performance by enabling the Swarmify SmartVideo™ solution.'=>'通过启用Swarmify SmartVideo™解决方案优化您的视频性能。','Configure CNAMEs'=>'配置CNAMEs','Service:'=>'服务：','If you already have a Swarmify configuration key, or need to update your existing key, click here:'=>'如果您已经有一个Swarmify配置密钥，或者需要更新您现有的密钥，请点击这里：','Free 14 day limited trial'=>'免费14天限时试用','Just as the load time and overall performance of your website impacts user satisfaction, so does the performance of your online videos. Optimize your video performance by enabling the Swarmify SmartVideo&#8482 solution.'=>'正如网站的加载时间和整体性能会影响用户满意度一样，在线视频的性能也会影响用户满意度。通过启用Swarmify SmartVideo#8482解决方案优化视频性能。','Select container to use'=>'选择要使用的容器','W3TC Community Edition'=>'W3TC社区版','Please update your PHP. <strong>W3 Total Cache</strong> requires PHP version 5.6 or above'=>'请更新您的PHP。<strong>W3 Total Cache</strong>需要PHP 5.3或更高版本','CDN site is not configured correctly: Delivery Domain must match your site domain'=>'CDN站点配置不正确：传递域必须与站点域匹配','Problem purging'=>'问题清除','Unknown error'=>'未知错误','Invalid Request URL'=>'无效的请求URL','Select Application'=>'选择应用程序','TransparentCDN'=>'透明CDN','Limelight'=>'Limelight','CloudFlare (extension not activated)'=>'CloudFlare CDN(插件未开启)','Security Headers: Quick Reference'=>'安全标头: 快速参考','Credentials are not specified.'=>'证书未指定.','Error. Check your parameters and try again or contact with support.'=>'错误。请检查您的参数并重试，或与支持人员联系。','Ok. Correct parameters'=>'好的。正确的参数','Test the API parameters offered for you site'=>'测试为您的站点提供的API参数','Save CloudFlare settings'=>'保存CloudFlare设置','The name should be a single word, and cannot contain any dots (.).'=>'这个名称应当是一个单词并且不能含有任何标点 (.).','The domain name through which visitors retrieve content. You will be provided with a target domain to use as an alias for this CNAME'=>'访问者检索内容的域名。您将被提供一个目标域，用作这个CNAME的别名','Create new service'=>'创建新服务','Support Us'=>'支持我们','Debug: Overlays'=>'调试：覆盖','Manage Extensions'=>'管理扩展','Purge Modules'=>'清除模块','Purge Current Page'=>'清除当前页面','Purge All Caches'=>'清除所有缓存','Cache Storage'=>'缓存存储','System Info'=>'系统信息','Web Requests'=>'网络请求','Enable reverse proxy caching via varnish'=>'通过varnish启用反向代理缓存','Enjoying W3TC? Please support us!'=>'享受W3TC吗？请支持我们！','Select host to use'=>'选择要使用的主机','Page Caching:'=>'页面缓存：','Object Cache Purges Log'=>'对象缓存清除日志','Upgrade to Pro'=>'升级到专业版','Redis password:'=>'Redis密码：','Redis Database ID:'=>'Redis数据库ID：','Redis hostname:port / <acronym title="Internet Protocol">IP</acronym>:port:'=>'Redis hostname:port / <acronym title="互联网协议">IP</acronym>:端口：','Binary protocol'=>'二进制协议','Memcached password:'=>'Memcached密码：','Memcached username:'=>'Memcached用户名：','Persistent connection'=>'持续连接','You\'re running debug mode, it\'s using Resources and not recommend to run continuously. %1$s'=>'您正在运行调试模式，它正在使用资源，建议不要连续运行。%s','The setting change(s) made either invalidate the cached data or modify the behavior of the site. %1$s now to provide a consistent user experience. %2$s'=>'所做的设置更改会使缓存的数据无效或修改站点的行为。%s现在提供一致的用户体验%s','nginx.conf rules have been updated. Please restart nginx server to provide a consistent user experience. %1$s'=>'Nginx.conf规则已更新。请重新启动nginx服务器以提供一致的用户体验。%s','Specify API Key'=>'指定API密钥','Content is cached for each group separately.'=>'分别为每个组缓存内容。','No groups added. All Cookies recieve the same page and minify cache results.'=>'未添加任何组。所有Cookie都会收到相同的页面并缩小缓存结果。','Specify the cookies for this group. Values like \'cookie\', \'cookie=value\', and cookie[a-z]+=value[a-z]+ are supported. Remember to escape special characters like spaces, dots or dashes with a backslash. Regular expressions are also supported.'=>'指定此组的Cookie。支持“cookie”、“cookie=value”和cookie[a-z]+=value[a-z]+等值。记住用反斜杠转义空格、点或破折号等特殊字符。还支持正则表达式。','Cookies:'=>'Cookies:','Cache:'=>'缓存：','of Cookies by specifying names in the Cookies field. Assign a set of Cookies to ensure that a unique cache is created for each Cookie group. Drag and drop groups into order (if needed) to determine their priority (top -&gt; down).'=>'通过在Cookie字段中指定名称来定义Cookie。分配一组Cookie，以确保为每个Cookie组创建唯一的缓存。将组按顺序拖放（如果需要）以确定其优先级（从上到下）。','Manage Cookie Groups'=>'管理Cookie组','Invalid API key'=>'无效的API密钥','Select folder'=>'选择文件夹','Failed to purge all: '=>'未能清除所有：','Failed to purge: '=>'清除失败：','View Details'=>'查看详情','Once enabled, each file request will update the cache with the latest version. When this setting is off, the Opcode Cache will not check, instead PHP must be restarted in order for setting changes to be reflected.'=>'一旦启用，每个文件请求都将使用最新版本更新缓存。关闭此设置时，操作码缓存将不会检查，而必须重新启动PHP才能反映设置更改。','Opcode: Zend Opcache'=>'操作码：Zend Opcache','Not Available'=>'不可用','The plugin has detected the following issues:. '=>'该插件检测到以下问题：。 ','New Relic is not running correctly. '=>'新文物未正确运行。 ','Legacy: New Relic is software analytics platform offering app performance management and mobile monitoring solutions.'=>'遗留问题：New Relic是一个软件分析平台，提供应用程序性能管理和移动监控解决方案。','Select site to use'=>'选择要使用的站点','For even better performance, combine FSD with other powerful features like Browser Cache, Minify, Fragment caching, or Lazy Load! Did you know that we offer premium support, customization and audit services? %1$sClick here for more information%2$s.'=>'为了获得更好的性能，请将FSD与浏览器缓存、缩小、片段缓存或延迟加载等其他强大功能结合起来！您知道我们提供高级支持、定制和审核服务吗？%1$s单击此处获取详细信息%2$s。','Want even faster speeds? The full site delivery Content Delivery Network will speed up your website by over 60% to increase conversions, revenue and reach your website visitors globally. With a Full Site Content Delivery Network (CDN), your website and all its assets will be available instantly to your visitors all over the world at blazing fast speeds.'=>'想要更快的速度？完整的网站交付内容交付网络将使您的网站加速超过60 % to，从而增加转化次数，增加收入并覆盖全球的网站访问者。借助完整的站点内容交付网络（CDN），您的网站及其所有资产将以极快的速度即时提供给全世界的访问者。','Deliver visitors the lowest possible response and load times for all site content including HTML, media (e.g. images or fonts), CSS, and JavaScript.'=>'为访客提供对所有站点内容（包括HTML，媒体（例如，图像或字体），CSS和JavaScript）的最低响应和加载时间。','Configure service'=>'配置服务','Failed to update CloudFlare settings:'=>'更新 CloudFlare CDN设置失败:','CloudFlare settings are successfully updated.'=>'CloudFlare CDN设置更新成功.','CloudFlare cache successfully emptied.'=>'CloudFlare 缓存清除成功.','Failed to purge CloudFlare cache: '=>'清除 CloudFlare 缓存失败: ','AMP URL Postfix:'=>'AMP URL 后缀：','AMP URL Type:'=>'AMP URL 类型：','Your StackPath Account credentials'=>'您的StackPath帐户凭据','Logged In:'=>'登录:','JWPlayer:'=>'JWPlayer：','&lt;video&gt;:'=>'& lt; 视频 & gt;:','API Key:'=>'API密钥：','Exclusive offers availabel for W3TC users!'=>'W3TC用户可享受独家优惠！','Configure zone'=>'配置区域','Your LimeLight Account credentials'=>'你的受到注目帐户凭据','If external script file names vary, use regular expressions in the "Include external files/libraries" field to simplify matching.'=>'如果外部脚本文件名不同，请在“包含外部文件/库”字段中使用正则表达式以简化匹配。','Use Regular Expressions for file name matching'=>'使用正则表达式进行文件名匹配','Always ignore the specified pages / directories. Use relative paths. Omit: protocol, hostname, leading forward slash and query strings.'=>'始终忽略指定的页面/目录。使用相对路径。省略：协议、主机名、前导正斜杠和查询字符串。',' %1$s%2$sNot supported by "Disk: Enhanced" page cache method for Nginx%3$s'=>'<br /><b>Nginx的“磁盘：增强”页面缓存方法不支持</b>','Message Bus'=>'消息库','Opcode Cache'=>'操作码缓存','Disable caching once specified constants defined.'=>'一旦定义了指定的常量，则禁用缓存。','Reject constants:'=>'拒绝常量：','Level II optimisations'=>'二级优化','Need help? Take a look at our %1$spremium support, customization and audit services%2$s.'=>'需要帮忙？查看我们的%1$s高级支持，定制和审核服务%2$s。','Faster paint time is a key last step in lowering bounce rates even for repeat page views. Enable this feature to significantly enhance your website’s user experience by reducing wait times and ensuring that users can interact with your website as quickly as possible.'=>'缩短绘制时间是降低跳出率的关键最后一步，即使对于重复页面浏览也是如此。启用此功能可以减少等待时间，并确保用户可以尽快与您的网站进行交互，从而显着增强您网站的用户体验。','Website visitors cannot navigate your website until a given page is ready - reduce the wait time with this feature.'=>'网站访问者在给定页面就绪之前无法浏览您的网站-使用此功能可以减少等待时间。','Eliminate render-blocking <acronym title="Cascading Style Sheet">CSS</acronym> by moving it to <acronym title="Hypertext Transfer Protocol">HTTP</acronym> body'=>'通过将其移动到<acronym title="Hypertext Transfer Protocol">HTTP</acronym>正文来消除渲染阻塞<acronym title="Cascading Style Sheet">CSS</acronym>','Create as new bucket'=>'创建为新存储器','Create as new bucket with distribution'=>'创建为具有分发的新存储桶','Host of API endpoint, comptabile with Amazon S3 API'=>'API端点的主机，与Amazon S3 API兼容','API host:'=>'主机API：','Enable this option if you don\'t have special public/private key files.'=>'如果您没有特殊的公钥文件, 请启用此选项。','FTP over SSH (SFTP)'=>'通过 SSH (SFTP) 的 FTP','SSL-FTP connection (FTPS)'=>'SSL-FTP连接（FTPS）','Plain FTP'=>'普通FTP','Sometimes, you\'ll encounter a complex issue involving your cache being purged for an unknown reason. The Purge Logs functionality can help you easily resolve those issues.'=>'有时，缓存因未知原因会被清除。清除日志功能可以帮助您轻松解决这些问题。','Purge Logs provide information on when your cache has been purged and what triggered it.'=>'清除日志提供有关缓存何时被清除以及触发原因的信息。','Purge Logs:'=>'清除日志：','Anonymously track usage to improve product quality'=>'匿名跟踪使用情况以提高产品质量','Please enter the license key provided after %1$s.'=>'请输入 %1$s 后提供的许可证密钥。','YUI Compressor (PHP)'=>'YUI压缩器(PHP)','Narcissus'=>'Narcissus','Google Closure Compiler (Local Java)'=>'Google Closure编译器（本地Java）','Google Closure Compiler (Web Service)'=>'Google Closure编译器（网络服务）','Minify (default)'=>'缩小（默认）','Redis'=>'Redis','Nginx + Memcached'=>'Nginx + Memcached','Disabled: see Requirements'=>'禁用：请参阅要求','The Expires header already sets the max-age.'=>'Expires 标头已设置 max-age。','Remove query strings from static resources'=>'从静态资源中删除查询字符串','Enable <acronym title="Hypertext Transfer Protocol">HTTP</acronym> (brotli) compression'=>'启用 <acronym title="Hypertext Transfer Protocol"> http </acronym> (brotli) 压缩','Configuration: Objects'=>'配置：对象','%1$sTo enable that, switch off "Use single network configuration file for all sites" option at General settings page and use specific settings for each blog.'=>'%1$s要启用此功能，请在常规设置页面关闭 "为所有站点使用单一网络配置文件 "选项，并为每个博客使用特定设置。','New Relic Module'=>'新的 Relic 模块','Install eAccelerator Module'=>'安装eAccelerator模块','Install XCache Module'=>'安装XCache模块','Install Memcached Module'=>'安装Memcached模块','Install Memcached Deamon'=>'安装Memcached守护程序','Server Preparation'=>'服务器准备','Services'=>'服务','Rewrite Rules (based on active settings)'=>'重写规则(基于活动设置)','Initial Installation'=>'初始安装','Specify redis password'=>'指定Redis密码','Database ID to use'=>'要使用的数据库 id','Using persistent connection doesn\'t reinitialize redis driver on each request'=>'使用持久连接不会在每个请求上重新初始化redis驱动程序','Using binary protocol can increase throughput.'=>'使用二进制协议可以增加吞吐量。','Use binary protocol:'=>'使用二进制协议：','When Amazon ElastiCache used, specify configuration endpoint as Memcached host'=>'使用Amazon ElastiCache时，将配置端点指定为Memcached主机','Using persistent connection doesn\'t reinitialize memcached driver on each request'=>'使用持久连接不会在每个请求上重新初始化memcached驱动程序','You\'re using the Community Edition of W3 Total Cache. Maximize your website\'s speed even more by upgrading to %1$sW3 Total Cache Pro%2$s to unlock advanced anaytics, fragment caching, full site delivery, extension support and other tools that will allow you to completely fine tune your website\'s performance.'=>'您正在使用W3 Total Cache的社区版。通过升级到%1$s W3 Total Cache Pro %2$s来解锁高级分析，片段缓存，完整站点交付，扩展支持和其他工具，这些可以使您完全优化网站的性能，从而最大程度地提高网站的速度。','Did you know that we offer premium support services?%1$s Our experts will configure W3 Total Cache for you! %2$sClick here for info%3$s.'=>'您知道我们提供高级支持服务吗？%1$s 我们的专家将为您配置 W3 总缓存！%2$s点击这里获取信息%3$s。','Specify memcached password, when <acronym title="Simple Authentication and Security Layer">SASL</acronym> authentication used'=>'使用 <acronym title="简单身份验证和安全层">SASL</acronym> 身份验证时，指定 memcached 密码','<br>Available when memcached extension installed, built with <acronym title="Simple Authentication and Security Layer">SASL</acronym>'=>'<br>安装 memcached 扩展时可用，使用 <acronym title="简单身份验证和安全层">SASL</acronym>构建','Specify memcached username, when <acronym title="Simple Authentication and Security Layer">SASL</acronym> authentication used'=>'使用 <acronym title="简单身份验证和安全层">SASL</acronym> 身份验证时，指定 memcached 用户名','When Amazon ElastiCache used, specify configuration endpoint as Memecached host'=>'使用Amazon ElastiCache时，将配置端点指定为Memecached主机','ElastiCache <acronym title="Hypertext Preprocessor">PHP</acronym> module not found'=>'未找到 ElastiCache <acronym title="超文本预处理器">PHP</acronym>模块','Node Auto Discovery:'=>'节点自动发现：','Use persistent connection:'=>'使用持久连接：','Unlock more speed, %1$s now!'=>'解锁更多速度，立即 %1$s！','Use that to store transients in database even when external cache is used. That allows transient values to survive object cache cleaning / expiration'=>'使用它可以将瞬态存储在数据库中, 即使使用外部缓存也是如此。允许瞬态值在对象缓存清理/过期后继续存在','Enabling this option will increase wp-admin performance, but may cause side-effects'=>'启用此选项将提高 wp-admin 性能, 但可能会导致副作用','Always ignore all pages filed under the specified custom fields. Separate name-value pairs with an equals sign (i.e., name=value).'=>'始终忽略在指定自定义字段下归档的所有页面。 使用等号（即name = value）分隔名称 - 值对。','Always ignore all pages filed under the specified author usernames.'=>'始终忽略在指定作者用户名下提交的所有页面。','Always ignore all pages filed under the specified tag slugs.'=>'始终忽略在指定标签slugs下提交的所有页面。','Always ignore all pages filed under the specified category slugs.'=>'始终忽略在指定类别slugs下提交的所有页面。','Overwrites key of page caching via custom filters by postponing entry extraction during the init action.'=>'通过在init操作期间推迟条目提取，覆盖通过自定义过滤器进行页面缓存的键。','Late caching:'=>'后期缓存：','If you use WordPress as a backend for integrations, API caching may be for you. Similar to page caching, repeat requests will benefit by having significantly lower response times and consume fewer resources to deliver. If WordPress is not used as a backend, for additional security, the API can be disabled completely.'=>'如果您使用WordPress作为集成的后端，则API缓存可能适合您。与页面缓存类似，重复请求将受益于响应时间显着减少且消耗更少的资源来交付。如果不将WordPress用作后端，则可以提高安全性，因此可以完全禁用该API。','If you\'re using the WordPress API make sure to use caching to scale performance.'=>'如果您使用的是WordPress API，请确保使用缓存来扩展性能。','If the same WordPress content is accessed from different domains'=>'如果从不同的域访问相同的WordPress内容','Cache alias hostnames:'=>'缓存别名主机名：','Aliases'=>'别名','Go Faster With Pro'=>'使用Pro更快','Unlock more performance options'=>'解锁更多性能选项','Defined (false)'=>'定义 (假)','Defined (true)'=>'定义 (true)','Redis extension:'=>'Redis 扩展：','Memcached extension:'=>'Memcached 扩展：','Installed (OPCache)'=>'已安装 (OPCache)','Compatibility Check'=>'兼容性检查','tweeted'=>'啾啾','Thank you! You\'ve been using W3 Total Cache for seven days or so! Please support us:'=>'谢谢！您已经使用 W3 Total Cache 七天左右!请支持我们：','Don\'t reconfigure, I know what I\'m doing'=>'不要重新配置,我知道我在做什么','Configure distribution'=>'发布配置','Caching Statistics'=>'缓存统计信息','Test StackPath'=>'测试StackPath CDN服务','CDN: Manual Purge'=>'CDN:手工选定页面','Test upload'=>'测试上传','Folder:'=>'文件夹：','Discussions'=>'讨论','Metrics are not available for browser applications'=>'指标不适用于浏览器应用程序','Region:'=>'地区：','Authorize:'=>'授权：','Activating the <a href="%s">Genesis Theme</a> extension for W3 Total Cache may be helpful for your site. <a href="%s">Click here</a> to try it. %s'=>'激活W3 Total Cache的<a href="%s">Genesis Theme</a>扩展可能对您的网站有所帮助<a href="%s">单击此处</a>进行尝试。%s','Please enable <strong>Fragment Cache</strong> module to make sure <strong>Genesis extension</strong> works properly.'=>'请启用 <strong>碎片缓存</strong>模块以确保<strong>起源延伸</strong> 工作正常.','Increase the performance of themes powered by the Genesis Theme Framework by up to 60%.'=>'将由Genesis主题框架支持的主题的性能提高多达60％。','Never cache pages that use these custom fields:'=>'永远不要缓存使用这些自定义字段的页面：','Never cache pages by these authors:'=>'永远不要缓存这些作者的页面：','Never cache pages that use these tags:'=>'永远不要缓存使用这些标签的网页：','Never cache pages associated with these categories:'=>'切勿缓存与这些类别关联的页面：','Disable <acronym title="Unicode Transformation Format">UTF</acronym>-8 blog charset support'=>'禁用 <acronym title="Unicode转换格式">UTF</acronym>-8博客字符集支持','Your Highwinds API Token'=>'您的 Highwinds API密钥','Usage Statistics'=>'使用统计','No zone defined'=>'无已定义的区域','Configuration:'=>'配置：','Use <acronym title="Content Delivery Network">CDN</acronym> links for the Media Library on admin pages'=>'在管理页面的媒体库中使用 <acronym title="内容交付网络">CDN</acronym>链接','<acronym title="Full Site Delivery">FSD</acronym> <acronym title="Content Delivery Network">CDN</acronym>'=>'<acronym title="Full Site Delivery">全站传输(FSD)</acronym> <acronym title="Content Delivery Network">内容分发网络(CDN)</acronym>','Cache genesis footer:'=>'高速缓存生成页脚:','Footer'=>'页脚','List of pages / posts that should not have the terms loop cached. Specify one page / post per line. This area supports regular expressions.'=>'不应缓存的含条件循环的页面/文章. 每行指定一页/文章. 此区域支持正则表达式.','Excluded terms pages / posts:'=>'不包括的任期页面/帖子:','Exclusions'=>'排除','Sidebar'=>'侧边栏','Content'=>'内容','Header'=>'页眉','Decline'=>'拒绝','Accept'=>'接受','Renew Now'=>'现在续费','It looks like your W3 Total Cache Pro license has expired. %1$s to continue using the Pro features'=>'您的 W3 Total Cache Pro 许可证似乎已过期。%1$s 继续使用专业版功能','Upgrade'=>'升级','Edit file <strong>%s</strong> and add the following rules above the WordPress directives:'=>'编辑文件<strong>%s</strong>并在WordPress指令上添加以下规则：','Your RackSpace API key'=>'您的Rackspace API密钥','Google Drive'=>'谷歌云端硬盘','Amazon Simple Storage Service (S3) Compatible'=>'兼容版亚马逊S3云存储','Amazon CloudFront Over S3'=>'亚马逊云前超过 S3','Verizon Digital Media Services (EdgeCast) / Media Temple ProCDN'=>'Verizon 数字媒体服务 (EdgeCast) / Media Temple ProCDN','StackPath SecureCDN (Legacy)'=>'StackPath SecureCDN(传统)','StackPath (recommended)'=>'StackPath (推荐)','RackSpace CDN'=>'RackSpace CDN','LimeLight'=>'LimeLighy CDN','Highwinds'=>'Highwinds','Origin Push:'=>'原点推:','Origin Pull / Mirror:'=>'手动拉取的原始主机名称:','License key could not be detected in ini file.'=>'在 ini 文件中无法检测到许可证密钥。','Not supported: %s.'=>'不支持：%s。','Select service to use'=>'选择要使用的服务','Swarmify%s'=>'Swarmify%s','logged in user rejected'=>'登录用户被拒绝','WP_ADMIN page'=>'WP_ADMIN 页','Allows you to control which origins can use which features.'=>'允许您控制哪些起源可以使用哪些功能。','Defines the defaults for directives you leave unspecified. Generally, this applies to any directive that ends with -src.'=>'定义未指定的指令的默认值。通常, 这适用于以-src 结尾的任何指令。','This directive operates similarly to the &lt;iframe&gt; sandbox attribute by applying restrictions to a page\'s actions, including preventing popups, preventing the execution of plugins and scripts, and enforcing a same-origin policy.'=>'此指令的操作类似于 & lt; iframe & gt; 沙盒属性, 方法是对页面的操作应用限制, 包括防止弹出窗口、阻止插件和脚本的执行以及强制执行同源策略。','Restricts the set of plugins that can be embedded into a document by limiting the types of resources which can be loaded.'=>'通过限制可以加载的资源类型来限制可嵌入到文档中的插件集。','Specifies valid parents that may embed a page using &lt;frame&gt;, &lt;iframe&gt;, &lt;object&gt;, &lt;embed&gt;, or &lt;applet&gt;.'=>'指定可能使用 & lt; 框架 & gt;、& lt; iframe & gt;、& lt; 对象 & gt;、& lt; 嵌入、或 & lt; 小程序 & gt; 嵌入页面的有效父级。','Specifies valid sources for JavaScript.'=>'指定 javascript 的有效源。','Allows control over the &lt;object&gt;, &lt;embed&gt;, and &lt;applet&gt; elements used by Flash and other plugins.'=>'允许控制 flash 和其他插件使用的 & lt; 对象 & gt;、& lt; 嵌入 & gt; 和 & lt; 小程序 & gt; 元素。','Specifies valid sources for loading media using the &lt;audio&gt; and &lt;video&gt; elements.'=>'指定使用 &lt;audio&gt; and &lt;video&gt; 元素加载媒体的有效源。','Specifies valid sources for images and favicons.'=>'指定图像和图标的有效源。','Restricts from where the protected resource can embed frames.'=>'限制受保护资源嵌入框架的位置。','Specifies the origins that can serve web fonts.'=>'指定可以为 web 字体提供服务的来源。','Limits the origins to which you can connect via XMLHttpRequest, WebSockets, and EventSource.'=>'限制可以通过 xmlhttpren、websocket 和事件源连接到的来源。','Not Set'=>'没有设置','This header restricts the values of the referer header in outbound links.'=>'此标头限制出站链接中引用标头的值。','Yes = Don\'t Enforce HPKP'=>'是=不执行HPKP','No = Enforce HPKP'=>'否 = 强制 hppp','This instructs the browser to not MIME-sniff a response outside its declared content-type. It helps to reduce drive-by download attacks and stops sites from serving malevolent content that could masquerade as an executable or dynamic HTML file.'=>'这指示浏览器不要对其声明的内容类型之外的响应进行MIME嗅探。 它有助于减少偷渡式下载攻击，并阻止网站提供可能伪装成可执行文件或动态HTML文件的恶意内容。','This header enables the %1$sXSS%2$s filter. It helps to stop malicious scripts from being injected into your website. Although this is already built into and enabled by default in most browsers today it is made available here to enforce its reactivation if it was disabled within the user\'s browser.'=>'此标头启用 %1$sXSS%2$s 筛选器。它有助于阻止恶意脚本注入您的网站。尽管此功能已经内置于当今大多数浏览器中，并且默认情况下已启用，但如果在用户的浏览器中禁用了它，则可以在此处强制重新激活它。','This tells the browser if it is permitted to render a page within a frame-like tag (i.e., &lt;frame&gt;, &lt;iframe&gt; or &lt;object&gt;). This is useful for preventing clickjacking attacks.'=>'这将告知浏览器是否允许在类似框架的标记 (i.e., &lt;frame&gt;, &lt;iframe&gt; or &lt;object&gt;). 中呈现页面。这对于防止点击劫持攻击非常有用。','This will prevent the user\'s session ID from being transmitted in plain text, making it much harder to hijack the user\'s session.'=>'这将阻止用户的会话ID以纯文本形式传输，从而更难以劫持用户的会话。','This tells the user\'s browser not to make the session cookie accessible to client side scripting such as JavaScript. This makes it harder for an attacker to hijack the session ID and masquerade as the effected user.'=>'这告诉用户的浏览器不要使会话cookie可以访问客户端脚本，例如JavaScript。 这使得攻击者更难以劫持会话ID并伪装成受影响的用户。','Security Headers'=>'安全标头','Controls whether the current document is allowed to use the WebXR Device API.'=>'控制当前文档是否允许使用 WebXR 设备 API。','Controls whether the current document is allowed to use Wake Lock API to indicate that device should not enter power-saving mode.'=>'控制当前文档是否允许使用唤醒锁 API 表示设备不应进入节电模式。','Controls whether the current document is allowed to use the WebUSB API.'=>'控制当前文档是否允许使用 WebUSB API。','Controls whether the current document is allowed to change the size of media elements after the initial layout is complete.'=>'控制当前文档是否允许在初始布局完成后更改媒体元素的大小。','Controls whether the current document is allowed to download and display unoptimized images.'=>'控制当前文档是否允许下载和显示未优化的图像。','Controls whether the current document is allowed to make synchronous XMLHttpRequest requests.'=>'控制当前文档是否允许同步 XMLHttp 请求请求。','Controls whether the current document is allowed to play audio via any methods.'=>'控制当前文档是否允许通过任何方法播放音频。','Controls whether the current document is allowed to play a video in a Picture-in-Picture mode via the corresponding API.'=>'控制当前文档是否允许通过相应的 API 在图片中播放视频。','Controls whether the current document is allowed to use the Payment Request API.'=>'控制当前文档是否允许使用付款请求 API。','Controls whether the current document is allowed to download and display large images.'=>'控制当前文档是否允许下载和显示大图像。','Controls whether the current document is allowed to use the Web MIDI API.'=>'控制当前文档是否允许使用 Web MIDI API。','Controls whether the current document is allowed to use audio input devices.'=>'控制当前文档是否允许使用音频输入设备。','Controls whether the current document is allowed to gather information about the orientation of the device through the Magnetometer interface.'=>'控制当前文档是否允许通过磁力计接口收集有关设备方向的信息。','Controls whether the current document is allowed to display images in legacy formats.'=>'控制当前文档是否允许以旧格式显示图像。','Controls whether the current document is allowed to show layout animations.'=>'控制当前文档是否允许显示布局动画。','Controls whether the current document is allowed to gather information about the orientation of the device through the Gyroscope interface.'=>'控制当前文档是否允许通过陀螺仪界面收集有关设备方向的信息。','Controls whether the current document is allowed to use the Geolocation Interface.'=>'控制当前文档是否允许使用地理定位接口。','Controls whether the current document is allowed to use Element.requestFullScreen().'=>'控制当前文档是否允许使用元素。','Controls whether the current document is allowed to use the Encrypted Media Extensions API (EME).'=>'控制当前文档是否允许使用Encrypted Media Extensions API（EME）。','Controls whether the current document is allowed to set document.domain.'=>'控制是否允许当前文档设置document.domain。','Controls whether or not the document is permitted to use Screen Capture API.'=>'控制是否允许文档使用Screen Capture API。','Controls whether the current document is allowed to use video input devices.'=>'控制是否允许当前文档使用视频输入设备。','Controls whether the current document is allowed to autoplay media requested through the HTMLMediaElement interface.'=>'控制是否允许当前文档自动播放通过HTMLMediaElement接口请求的媒体。','Controls whether the current document is allowed to gather information about the amount of light in the environment around the device through the AmbientLightSensor interface.'=>'控制当前文档是否允许通过环境光传感器接口收集有关设备周围环境中光量的信息。','Controls whether the current document is allowed to gather information about the acceleration of the device through the Accelerometer interface.'=>'控制当前文档是否允许通过加速度计接口收集有关设备加速的信息。','Remove jquery-migrate support from your website front-end.'=>'从您的网站前端删除jquery-migrate支持。','Disable jquery-migrate on the front-end'=>'在前端禁用 jquery 迁移','Remove wp-embed.js script from your website. oEmbed functionality still works but you will not be able to embed other WordPress posts on your pages.'=>'从您的网站中删除 wp-embed.js 脚本。oEmbed 功能仍然有效，但您将无法在网页上嵌入其他 WordPress 帖子。','Disable wp-embed script'=>'禁用 wp 嵌入脚本','Remove emojis support from your website.'=>'从您的网站中删除表情符号支持。','Disable Emoji'=>'禁用表情符号','In addition to lazy loading images, with %1$sW3 Total Cache Pro%2$s you can lazy load %3$sGoogle Maps%4$s! More information and settings can be found on the %5$sUser Experience page%6$s.'=>'除了延迟加载图像外，使用%1$sW3 Total Cache Pro%2$s您还可以延迟加载%3$sGoogle Maps%4$s！有关详细信息和设置，请参阅%5$s用户体验页面%6$s。','Lazy Load Google Maps'=>'延迟加载Google地图','Defer loading offscreen images.'=>'延迟加载屏幕外图像。','Lazy Load Images'=>'延迟加载图像','User Experience'=>'用户体验','Test TransparentCDN'=>'测试透明CDN','Client secret:'=>'客户端密钥：','Client id:'=>'客户端ID:','Company id:'=>'公司 ID：','Cache Misses'=>'未命中的缓存','Cache Hits'=>'命中的缓存','Transferred'=>'调动','Content Zone:'=>'内容区域:','Status'=>'状态','Done'=>'完成','Succeeded'=>'已成功','Select zone to use'=>'选择要使用的CDN区域','Select region'=>'选择区域','Select stack to use'=>'选择要使用的stack','Learn more about Pro'=>'了解更多PRO','Plus, there\'s even more that allow you to completely fine tune your website\'s performance.'=>'此外，还有更多功能可以让您完全微调网站的性能。','Extension Support'=>'扩展支持','Full Site Delivery'=>'全网站分发','Advanced Analytics'=>'高级分析','You\'re using the Community Edition of W3 Total Cache. Maximize your website\'s speed even more by upgrading to %1$sW3 Total Cache Pro%2$s to unlock:'=>'您使用的是 W3 总缓存的社区版。通过升级到 %1$sW3 总缓存 Pro%2$s来解锁，使网站的速度更加最大化：','Content Delivery Network Full Site Delivery via %s'=>'CDN 全站传输通过 %s','South America (São Paulo)'=>'南美洲（ 圣保罗 ）','Middle East (Bahrain)'=>'中东（巴林）','China (Ningxia)'=>'中国（宁夏）','Canada (Central)'=>'加拿大(中部)','Asia Pacific (Sydney)'=>'亚太地区（悉尼）','Asia Pacific (Singapore)'=>'亚太地区（新加坡）','Asia Pacific (Mumbai)'=>'亚太地区（孟买）','Asia Pacific (Osaka-Local)'=>'亚太地区(大阪本地)','Asia Pacific (Seoul)'=>'亚太地区（首尔）','Asia Pacific (Tokyo)'=>'亚太地区(东京)','Asia Pacific (Hong Kong)'=>'亚太地区（香港）','US West (Oregon)'=>'美国西部（俄勒冈州）','US West (N. California)'=>'美国西部(加利福尼亚州)','US East (Ohio)'=>'美国东部(俄亥俄州)','US East (N. Virginia)'=>'美国东部（弗吉尼亚州北部）','action %s does not exist'=>'操作 %s 不存在','Access key not specified.'=>'鉴权密钥未指定.','Script Embed method:'=>'脚本嵌入方法：','Exclude tags containing words'=>'排除包含单词的标记','Process background images'=>'处理背景图像','Process HTML image tags'=>'处理 HTML 图像标记','Lazy Loading'=>'延迟加载','Adds compatibility for accelerated mobile pages (AMP) to minify.'=>'添加兼容性列表来使移动加速页面精简.','Page Cache: Current Page'=>'页面缓存：当前页面','REST API disabled.'=>'REST API已禁用。','rejected by filter: '=>'被筛选器拒绝: ','XSL not tracked'=>'未跟踪 XSL','Reauthorize'=>'重新授权','Configuration: Full-Site Delivery'=>'设置:全站推送','API key not specified.'=>'API密钥未指定.','Next'=>'下一个','Email:'=>'邮箱：','Fragment caching is a powerful, but advanced feature. If you need help, take a look at our premium support, customization and audit services.'=>'片段缓存是一个强大但高级的功能。如果您需要帮助，请查看我们的高级支持，自定义和审核服务。','Fragment caching extends the core functionality of WordPress by enabling caching policies to be set on groups of objects that are cached. This allows you to optimize various elements in themes and plugins to use caching to save resources and reduce response times. You can also use caching methods like Memcached or Redis (for example) to scale. Instructions for use are available in the FAQ available under the help menu. This feature also gives you control over the caching policies by the group as well as visibility into the configuration by extending the WordPress Object API with additional functionality.'=>'片段缓存通过启用缓存对象组设置缓存策略来扩展 WordPress 的核心功能。这允许您优化主题和插件中的各种元素，以使用缓存来节省资源和减少响应时间。还可以使用缓存方法（例如，Memcached 或 Redis）进行缩放。使用说明可在帮助菜单下的常见问题解答中找到。此功能还允许您通过扩展 WordPress 对象 API 和其他功能来控制组的缓存策略以及配置可见性。','Increase the performance of dynamic sites that cannot benefit from the caching of entire pages.'=>'提高无法从整个页面缓存中受益的动态网站的性能。','https://www.boldgrid.com/'=>'https://www.boldgrid.com/','BoldGrid'=>'BoldGrid','https://www.boldgrid.com/totalcache/'=>'https://www.boldgrid.com/totalcache/','The highest rated and most complete WordPress performance plugin. Dramatically improve the speed and user experience of your site. Add browser, page, object and database caching as well as minify and content delivery network (CDN) to WordPress.'=>'评分最高、最完整的WordPress性能插件。极大地提高了网站的速度和用户体验。 将浏览器、页面、对象和数据库缓存以及最小化和内容交付网络（CDN）添加到WordPress。','W3 Total Cache'=>'W3 Total Cache','Spread the Word'=>'传言','Premium Services'=>'高级服务','PageSpeed Report'=>'页面速度报告','News'=>'新闻','view visualizations'=>'查看可视化','Forums'=>'论坛','Varnish servers:'=>'Varnish服务器：','Amazon <acronym title="Simple Notification Service">SNS</acronym> region:'=>'亚马逊 <acronym title="简单通知服务">SNS</acronym> 区域：','Topic <acronym title="Identification">ID</acronym>:'=>'话题<acronym title="Identification">ID</acronym>:','<acronym title="Application Programming Interface">API</acronym> secret:'=>'<acronym title="Application Programming Interface">API</acronym> secret:','Enable cache purge via Amazon <acronym title="Simple Notification Service">SNS</acronym>'=>'通过 Amazon <acronym title="简单通知服务">SNS</acronym>启用缓存清除功能','Referrer groups'=>'推荐组','Handle <acronym title="Extensible Markup Language">XML</acronym> mime type'=>'处理 <acronym title="Extensible Markup Language">XML</acronym>IME 类型','Specify page headers:'=>'指定页眉：','Non-trailing slash pages:'=>'非尾随斜杠页面：','Cache exception list:'=>'缓存例外列表：','Rejected cookies:'=>'被拒绝Cookie：','Accepted query strings:'=>'接受的查询字符串：','Comment cookie lifetime:'=>'评论Cookie寿命：',' Disable caching of HEAD <acronym title="Hypertext Transfer Protocol">HTTP</acronym> requests'=>' 禁止缓存 HEAD <acronym title="超文本传输协议">HTTP</acronym>请求','Purge sitemaps:'=>'清除站点地图：','Additional pages:'=>'附加页面：','Purge limit:'=>'清除限制：','Specify the feed types to purge:'=>'指定要清除的Feed类型：','Yearly archive pages'=>'年度归档页面','Monthly archive pages'=>'每月归档页面','Daily archive pages'=>'每日归档页','Post terms feeds'=>'文章术语订阅源','Post author feed'=>'文章作者订阅源','Post comments feed'=>'文章评论订阅源','Post terms pages'=>'发布术语页面','Post author pages'=>'发表作者页面','Post comments pages'=>'发表评论页面','Blog feed'=>'博客订阅','Post page'=>'文章页面','Posts page'=>'文章页面','Front page'=>'首页','Preload the post cache upon publish events'=>'在发布事件时预加载后缓存','Sitemap <acronym title="Uniform Resource Indicator">URL</acronym>:'=>'网站地图<acronym title="Uniform Resource Indicator">URL</acronym>：','Pages per interval:'=>'每间隔页：','Update interval:'=>'更新间隔：','Automatically prime the page cache'=>'自动启动页面缓存','Don\'t cache pages for following user roles'=>'以下用户组不缓存页面','Don\'t cache pages for logged in users'=>'已登录用户不缓存页面','Cache 404 (not found) pages'=>'缓存404页面','Cache <acronym title="Uniform Resource Identifier">URI</acronym>s with query string variables'=>'缓存带有查询字符串变量的 <acronym title="统一资源标识符">URI</acronym>','Cache <acronym title="Secure Socket Layer">SSL</acronym> (<acronym title="HyperText Transfer Protocol over SSL">HTTPS</acronym>) requests'=>'缓存 <acronym title="安全套接字层">SSL</acronym>（<acronym title="通过 SSL 的超文本传输协议">HTTPS</acronym>）请求','Cache feeds: site, categories, tags, comments'=>'缓存站点地图，分类，评论、标签','Don\'t cache front page'=>'不缓存首页','Cache posts page'=>'缓存文章页','Cache front page'=>'缓存首页','Page Cache:'=>'页面缓存：','Page Cache Method:'=>'页面缓存方式：','Flush all cache on post, comment etc changes.'=>'刷新后，评论等变化的所有缓存。','Non-persistent groups:'=>'非持久性组:','Global groups:'=>'全局组：','Default lifetime of cache objects:'=>'缓存对象的默认生存期：','Object Cache:'=>'对象缓存：','Object Cache Method:'=>'对象缓存方式：','Enable XMIT:'=>'启用XMIT：','Use %1$sPHP%2$s function to set application name:'=>'使用PHP函数设置应用程序名称：','Cache time:'=>'缓存时间：','User Agent groups'=>'用户代理组','User Agents:'=>'用户代理：','Disable all the built-in micro optimizations'=>'禁用所有内置微优化','Preserve unnecessary semicolons'=>'保留不必要的分号','Minify only, do not obfuscate local symbols'=>'仅缩小，不会混淆局部符号','Line break after:'=>'换行后:','Line break removal (not safe, not applied when combine only is active)'=>'换行去除 (并不安全，如果只合成处于活动状态不适用)','Wrap after:'=>'包装后:','Hide comments'=>'隐藏评论','Clean'=>'清理','Line break removal'=>'换行符','Regroup selectors:'=>'重组选择：','Case for properties:'=>'案例属性：','Optimize shorthands:'=>'优化速记符：','Compression:'=>'压缩：','Add timestamp'=>'添加时间戳','Preserve CSS'=>'保存 CSS','Discard invalid properties'=>'丢弃无效的属性','Sort Selectors (caution)'=>'排序选择器 (慎用)','Sort Properties'=>'排序属性','Remove last ;'=>'删除最后;','Lowercase selectors'=>'小写选择','Compress font-weight'=>'压缩字体粗细','Compress colors'=>'压缩颜色','Remove unnecessary backslashes'=>'删除不必要的反斜杠','Line break removal (not applied when combine only is active)'=>'删除换行（结合时不仅适用有效）','Preserved comment removal (not applied when combine only is active)'=>'删除注释（仅在组合处于活动状态时不应用）','Compilation level:'=>'编译级别：','Pretty print'=>'优质打印','Include external files/libraries:'=>'包括外部文件/库：','Never minify the following pages:'=>'切勿缩小以下页面：','Update external files every:'=>'每次更新外部文件：','@import handling:'=>'@import处理：','Before <span class="html-tag">&lt;/body&gt;</span>'=>'之前<span class="html-tag">&lt;/body&gt;</span>','After <span class="html-tag">&lt;body&gt;</span>'=>'之后<span class="html-tag">&lt;body&gt;</span>','Combine only'=>'仅合并','Embed type:'=>'嵌入类型：','Ignored comment stems:'=>'忽略评论词：','Don\'t minify feeds'=>'不要压缩Feeds','Inline <acronym title="JavaScript">JS</acronym> minification'=>'内联 <acronym title="JavaScript">JS</acronym> 缩小','Inline <acronym title="Cascading Style Sheet">CSS</acronym> minification'=>'内联<acronym title="Cascading Style Sheet">CSS</acronym>缩小','Minify error notification:'=>'缩小错误通知：','Disable minify for logged in users'=>'禁用登录用户的缩小','Rewrite <acronym title="Uniform Resource Locator">URL</acronym> structure'=>'重写 <acronym title="统一资源定位器">URL</acronym> 结构','Minify mode:'=>'压缩模式:','<acronym title="Cascading Style Sheets">CSS</acronym> minifier:'=>'<acronym title="Cascading Style Sheets">CSS</acronym> 压缩器:','<acronym title="JavaScript">JS</acronym> minifier:'=>'<acronym title="JavaScript">JS</acronym> 压缩:','<acronym title="Hypertext Markup Language">HTML</acronym> minifier:'=>'<acronym title="Hypertext Markup Language">HTML</acronym> 压缩:','Minify:'=>'压缩：','Minify Cache Method:'=>'压缩缓存方式：','License:'=>'授权：','Verify rewrite rules'=>'验证重写规则','Nginx server configuration file path'=>'Nginx服务器配置文件路径','Use single network configuration file for all sites.'=>'所有站点使用单一的网络配置文件。','Enable Google PageSpeed dashboard widget'=>'启用 Google PageSpeed 面板小工具','Manual fragment groups:'=>'手动碎片组:','Default lifetime of cached fragments:'=>'缓存碎片的默认生命周期：','Fragment Caching'=>'片段缓存','Fragment Cache Method:'=>'碎片缓存方式：','Reject query words:'=>'拒绝查询的词:','Ignored query stems:'=>'忽略查询的词干:','Never cache the following pages:'=>'不要缓存以下页面：','Garbage collection interval:'=>'垃圾收集间隔：','Maximum lifetime of cache objects:'=>'缓存对象的最长生命周期：','Memcached hostname:port / <acronym title="Internet Protocol">IP</acronym>:port:'=>'Memcached主机名：端口/ <acronym title="Internet Protocol">IP</acronym>：端口：','Don\'t cache queries for logged in users'=>'不要为登录用户缓存查询','Database Cache:'=>'数据库缓存:','Database Cache Method:'=>'数据库缓存方式:','Rejected files:'=>'被拒绝的文件：','Rejected user agents:'=>'被阻止的用户代理:','Custom file list:'=>'自定义文件列表:','File types to import:'=>'要导入的文件类型:','Theme file types to upload:'=>'要上传的主题文件:','wp-includes file types to upload:'=>'要上传的 wp-includes 文件:','Re-transfer cycle limit:'=>'重新传输循环限制:','Re-transfer cycle interval:'=>'重新传输循环时间间隔:','Auto upload interval:'=>'自动上传时间间隔:','Export changed files automatically'=>'自动导出被修改的文件','Disable <acronym title="Content Delivery Network">CDN</acronym> on the following pages:'=>'为下列页面关闭<acronym title="Content Delivery Network">CDN</acronym> 功能:','Disable <acronym title="Content Delivery Network">CDN</acronym> for the following roles'=>'为下列角色关闭<acronym title="Content Delivery Network">CDN</acronym> 功能','Disable <acronym title="Content Delivery Network">CDN</acronym> on <acronym title="Secure Sockets Layer">SSL</acronym> pages'=>'在<acronym title="Secure Sockets Layer">SSL</acronym> 网页上关闭<acronym title="Content Delivery Network">CDN</acronym>','Add canonical header'=>'添加canonical 页头','Import external media library attachments'=>'导入外部媒体库附件','Force over-writing of existing files'=>'强制覆盖已经存在的文件','Host custom files'=>'主机自定义文件','Host minified <acronym title="Cascading Style Sheet">CSS</acronym> and <acronym title="JavaScript">JS</acronym> files'=>'主机精简压缩的 <acronym title="Cascading Style Sheet">CSS</acronym> 和<acronym title="JavaScript">JS</acronym> 文件','Host theme files'=>'主机主题文件','Host wp-includes/ files'=>'主机 wp-includes/ 文件','Host attachments'=>'主机附件','<acronym title="Content Delivery Network">CDN</acronym> Type:'=>'<acronym title="Content Delivery Network">内容分发网络(CDN)</acronym>类型:','<acronym title="Content Delivery Network">CDN</acronym>:'=>'<acronym title="Content Delivery Network">内容分发网络(CDN)</acronym>:','Enable <acronym title="Hypertext Transfer Protocol">HTTP</acronym> (gzip) compression</label>'=>'启用 <acronym title="Hypertext Transfer Protocol">HTTP</acronym> (gzip) 压缩</label>','Disable cookies for static files'=>'禁用静态文件的cookie','Set entity tag (ETag)'=>'设置实体标签（ETag）','Cache Control policy:'=>'缓存控制策略:','Expires header lifetime:'=>'请求头过期时间：','404 error exception list:'=>'404错误排除列表:','Do not process 404 errors for static objects with WordPress'=>'不要为WordPress的静态对象显示404错误','Prevent caching exception list:'=>'阻止缓存过期的列表:','Browser Cache:'=>'浏览器缓存:','disable'=>'禁用','deploy'=>'部署',' <a href="?page=w3tc_cdn#configuration">Specify it here</a>.'=>' <a href="?page=w3tc_cdn#configuration">在这里选定</a>.','A configuration issue prevents <acronym title="Content Delivery Network">CDN</acronym> from working: '=>'一个配置文件错误阻止<acronym title="Content Delivery Network">CDN</acronym>正常运行: ','The <strong>"Replace default hostname with"</strong> field cannot be empty.'=>'<strong>"用默认主机名替换"</strong>字段不能为空。','The <strong>"Account name", "Account key" and "Container"</strong> fields cannot be empty.'=>'<strong>"帐户名称"、"帐户密钥"和"容器"</strong> 字段不能为空。','The <strong>"Username", "API key", "Container" and "Replace default hostname with"</strong> fields cannot be empty.'=>'<strong>"API 键"、"容器"和"替换默认主机名与"的"用户名"</strong> 字段不能为空。','The <strong>"Access key", "Secret key" and "Replace default hostname with"</strong> fields cannot be empty.'=>'<strong>"访问键"、"秘密钥匙"和"替换默认主机名与"</strong> 字段不能为空。','The <strong>"Access key", "Secret key", "Bucket" and "Replace default hostname with"</strong> fields cannot be empty.'=>'<strong>"访问键"、"秘密钥匙"、"储存位置"和"替换默认主机名与"</strong> 字段不能为空。','The <strong>"Access key", "Secret key" and "Bucket"</strong> fields cannot be empty.'=>'该 <strong>"Access key", "Secret key" 和"Bucket"</strong> 字段不能为空.','A configuration issue prevents <acronym title="Content Delivery Network">CDN</acronym> from working:
                                        The <strong>"Replace default hostname with"</strong>
                                        field cannot be empty. Enter <acronym
                                        title="Content Delivery Network">CDN</acronym>
                                        provider hostname <a href="?page=w3tc_cdn#configuration">here</a>.
                                        <em>(This is the hostname used in order to view objects
                                        in a browser.)</em>'=>'由于配置问题，<acronym title="内容交付网络">CDN</acronym> 无法工作：
                                        <strong>"用默认主机名替换"</strong>字段不能为空。
                                        字段不能为空。输入 <acronym
                                        title="内容交付网络">CDN</acronym>。
                                        提供商主机名<a href="?page=w3tc_cdn#configuration">此处</a>。
                                        <em>（这是在浏览器中查看对象时使用的主机名）</em>（这是在浏览器中查看对象时使用的主机名）</em>。
                                        在浏览器中查看对象时使用的主机名）</em','update the path'=>'更新路径','The uploads path found in the database (%s) is inconsistent with the actual path. Please manually adjust the upload path either in miscellaneous settings or if not using a custom path %s automatically to resolve the issue.'=>'在数据库中找到的上传路径(%s)和实际路径不符合.请手动调整上传路径,在杂项设置中设置或者使用自定义路径(%s)来自动解决这个问题.','The uploads directory is not available. Default WordPress directories will be created: <strong>%s</strong>.'=>'上传目录当前不可用.默认的WordPress目录将会被创建在如下目录: <strong>%s</strong>.','Either the PHP configuration, web server configuration or a script in the WordPress installation has %1$szlib.output_compression%2$s enabled.%3$sPlease locate and disable this setting to ensure proper HTTP compression behavior. %4$s'=>'无论是 PHP 配置、Web 服务器配置还是 WordPress 安装中的脚本都启用了 %1$szlib.output_compression%2$s。%3$s请找到并禁用此设置，以确保正确的 HTTP 压缩行为。%4$s','Unfortunately the PHP installation is incomplete, the %1$szlib module is missing%2$s. This is a core PHP module. Notify the server administrator. %3$s'=>'不幸的是，PHP安装不完整，缺少%1$szlib模块%2$s。这是一个核心的PHP模块。通知服务器管理员。%3$s','The <strong>CURL PHP</strong> extension is not available. Please install it to enable S3 or CloudFront functionality. %s'=>'<strong>CURL PHP</strong> 扩展不可用。请安装它以启用 S3 或 CloudFront 功能。%s','Object Cache: %s.'=>'对象缓存: %s.','Database Cache: %s.'=>'数据库缓存: %s.','Minify: %s.'=>'缩小: %s.','Page Cache: %s.'=>'页面缓存: %s.','Browser Cache and use compression'=>'浏览器缓存和使用压缩','CDN'=>'CDN','Encountered issue with CDN: %s.'=>'CDN出现偶然错误: %s.','Install page'=>'安装页面','Encountered issue with CDN: %s. See %s for instructions of creating correct table.'=>'CDN出现偶然错误: %s. 查看 %s 以查看正确创建表的指示.','unsuccessful transfer queue'=>'队列转换失败','The %s has unresolved errors. Empty the queue to restore normal operation.'=>'该 %s 有未解决的错误。清空队列恢复正常运行.','export the media library'=>'导出媒体库','Settings that affect Browser Cache settings for files hosted by the CDN have been changed. To apply the new settings %s and %s. %s'=>'影响主办 CDN 的文件浏览器缓存设置的设置已更改。要应用新设置 %s 和 %s %s','Make sure to %s and upload the %s, files to the <acronym title="Content Delivery Network">CDN</acronym> to ensure proper operation. %s'=>'确保 %s 并将 %s 文件上传到 <acronym title="内容交付网络">CDN</acronym>，以确保正常运行。%s','Upgraded WordPress? Please %s files now to ensure proper operation. %s'=>'您是否已经升级过WordPress? 请现在 %s 文件以确保设置正确. %s','upload active theme files'=>'上传活动主题文件','The active theme has changed, please %s now to ensure proper operation. %s'=>'当前激活的主题已经更改,请现在 %s 以确保设置正确. %s','Empty the minify cache'=>'清空压缩缓存','Recently an error occurred while creating the CSS / JS minify cache: %s. %s'=>'最近创建CSS / JS minify缓存时出错：%s. %s','Empty the object cache'=>'清空对象缓存','The setting change(s) made either invalidate the cached data or modify the behavior of the site. %s now to provide a consistent user experience.'=>'设置更改使缓存的数据无效或修改站点的行为。 %s现在提供一致的用户体验。',' and '=>' 和 ','One or more plugins have been activated or deactivated, please %1$s. %2$s'=>'一个或多个插件已被激活或停用，请%1$s。%2$s','minify settings'=>'压缩设置','check the %1$s to maintain the desired user experience'=>'检查%1$s以保持所需的用户体验','empty the page cache'=>'清空页面缓存','Required files and directories have been automatically created'=>'所需的文件和目录已经自动创建','Pull Zone was automatically created.'=>'自动创建拖动区域。','Edge mode has been disabled.'=>'边缘模式已被禁用。','Edge mode has been enabled.'=>'边缘模式已被启用。','The add-in has been removed.'=>'该加载项已被删除。','New relic settings have been updated.'=>'New Relic的设置已更新。','Post successfully purged.'=>'文章成功清除.','Attachment successfully purged.'=>'附件成功清除。','Preview settings successfully deployed. Preview mode remains enabled until it\'s disabled. Continue testing new settings or disable preview mode if done.'=>'预览设置成功部署。直到它的关闭预览模式保持启用状态。继续测试新的设置，或者如果做禁用预览模式。','Preview mode was successfully disabled'=>'预览模式已成功停用','Preview mode was successfully enabled'=>'已成功启用预览模式','Settings successfully restored.'=>'设置成功恢复。','Settings successfully imported.'=>'设置已成功导入。','The support request has been successfully sent.'=>'支持请求已成功发送。','%1$s was successfully purged.'=>'已成功清除 %1$s。','Varnish servers successfully purged.'=>'清漆服务器成功清洗。','Media Query string has been successfully updated.'=>'媒体查询字符串已成功更新。','Minify cache successfully emptied.'=>'成功清空缓存。','Fragment cache successfully emptied.'=>'片段缓存清空成功。','Object cache successfully emptied.'=>'对象缓存清空成功。','Database cache successfully emptied.'=>'数据库缓存清空成功。','Page cache successfully emptied.'=>'页面缓存清空成功。','Disk cache(s) successfully emptied.'=>'磁盘缓存已成功清空。','Opcode cache(s) successfully emptied.'=>'操作码缓存已成功清空。','Memcached cache(s) successfully emptied.'=>'Memcached缓存已成功清空.','All caches successfully emptied.'=>'所有缓存都成功清空。','Plugin configuration successfully updated.'=>'插件配置已成功更新。','Pull Zone could not be automatically created.'=>'无法自动创建拖动区。','Unable to purge post.'=>'无法清除文章.','Unable to purge attachment.'=>'无法清除附件。','Configuration file could not be imported.'=>'配置文件无法导入.','Unable to upload config file.'=>'无法上传配置文件.','Please select config file.'=>'请选择配置文件。','Unable to send the support request.'=>'无法发送支持请求。','Please enter WP Admin password, be sure it\'s spelled correctly.'=>'请输入 WP 管理员密码，请确保其拼写正确。','Please enter an administrator login. Create a temporary one just for this support case if needed.'=>'请输入一个管理员登录。如果需要请创建一个临时仅为此支持情况.','Please describe the issue in the issue description field.'=>'请说明问题描述领域中的问题.','Please enter subject in the subject field.'=>'请在主题字段中输入主题。','Please enter your phone in the phone field.'=>'请在电话字段中输入您的电话.','Please enter valid email address in the E-Mail field.'=>'请在电子邮件字段中输入有效的电子邮件地址.','Please enter your name in the Name field'=>'请在名称字段中输入你的名字','Please select request type.'=>'请选择请求类型.','Fancy permalinks are disabled. Please %1$s it first, then re-attempt to enabling the \'Do not process 404 errors for static objects with WordPress\'.'=>'花式永久链接已禁用。请先将其设置为%1$s，然后重新尝试启用 "不处理 WordPress 静态对象的 404 错误"。','Fancy permalinks are disabled. Please %1$s it first, then re-attempt to enabling enhanced disk mode.'=>'花式永久链接已禁用。请先将其设置为 %1$s，然后重新尝试启用增强磁盘模式。','Empty Caches'=>'清空缓存','Application Monitoring using New Relic%s'=>'使用New Relic%s的应用程序监视','Content Delivery Network via %1$s%2$s'=>'通过 %1$s%2$s 的内容交付网络','Fragment Caching %d/%d fragments using %s%s'=>'片缓存%d/%d个碎片，使用%s%s','Object Caching %1$d/%2$d objects using %3$s%4$s'=>'使用 %1$d%2$d对象缓存%3$s%4$s','Database Caching using %1$s%2$s'=>'使用数据库缓存%1$s%2$s','Database Caching %1$d/%2$d queries in %3$.3f seconds using %4$s%5$s'=>'数据库缓存%1$d/%2$d查询，在%3$.3f秒内使用%4$s%5$s','Minified using %s%s'=>'缩小使用了%s%s','Empty Fragment Cache'=>'清空碎片缓存','Empty Object Cache'=>'清空对象缓存','Test Database Cache'=>'数据库缓存','Empty Minify Cache'=>'清空压缩缓存','Empty Page Cache'=>'清空页面缓存','Empty All Caches'=>'清空所有缓存','Empty OpCode Cache'=>'空操作码缓存','click here'=>'点击这里','Please review the <a href="%s">settings</a>.'=>'请参阅 <a href="%s">设置</a>.','logged in role is rejected'=>'登录角色被拒绝','DONOTAUTORUM constant is defined'=>'DONOTAUTORUM定义常量','DOING_AJAX constant is defined'=>'DOING_AJAX定义常量','Extensions'=>'扩展','Purge from CDN'=>'从CDN拉取数据','The Object Cache add-in file object-cache.php is not a W3 Total Cache drop-in. Remove it or disable Object Caching. %1$s'=>'对象缓存加载项文件对象缓存.php不是 W3 总缓存下拉列表。删除它或禁用对象缓存。%1$s','DONOTCACHEOBJECT constant is defined'=>'DONOTCACHEOBJECT 常数定义','Object caching is disabled'=>'对象缓存被禁用','API Key is invalid.'=>'API 密钥无效.','Configured license key does not match license key(s) in account: <br />%s <br />%s'=>'已配置的许可密钥与帐号中的许可密钥不匹配: <br />%s <br />%s','Application ID is not configured. Enter/Select application name.'=>'未配置应用程序 ID.  输入 / 选择应用程序名称.','Account ID is not configured.'=>'未配置帐号 ID.','API Key is not configured.'=>'未配置 API 密钥.','PHP agent is not enabled.'=>'PHP 代理未启用.','PHP module is not enabled.'=>'PHP 模块未启用.','Web Server'=>'Web 服务器','Operating System'=>'操作系统','PHP version'=>'PHP版本','Supported'=>'已支持','Performance'=>'性能','About'=>'关于','Install'=>'安装','Support'=>'支持','FAQ'=>'常见问题','Content Delivery Network'=>'内容分发网络','General Settings'=>'常规设置','The W3 Total Cache license key is activated for this site.'=>'The W3 Total Cache 许可证密钥激活这个网站.','The W3 Total Cache license key is deactivated for this site.'=>'此网站停用 W3 Total Cache许可证密钥。','The W3 Total Cache license key can\'t be verified.'=>'无法验证  W3 Total Cache许可证密钥。','The W3 Total Cache license key is not active for this site.'=>'W3 总缓存许可证密钥未激活此网站。','The W3 Total Cache license key is not active.'=>'W3 Total Cache许可证密钥未处于活动状态。','Please enter it again.'=>'请重新输入。','The W3 Total Cache license key you entered is not valid.'=>'您输入的  W3 Total Cache许可证密钥无效。','Yes, remove it for me'=>'是的，为我删除它','The Page Cache add-in file advanced-cache.php is not a W3 Total Cache drop-in.
                    It should be removed. %s'=>'页面缓存加载项文件advanced-cache.php不是W3 Total Cache插件。
                    应该删除它。 %s','Purge from cache'=>'从缓存清除','<strong>ERROR</strong>: WordPress %s requires MySQL 4.1.2 or higher'=>'<strong>错误</strong>: WordPress %s 需要 MySQL 4.1.2 或者更高版本','Remove it for me'=>'为我删除它','The Database add-in file db.php is not a W3 Total Cache drop-in.
                Remove it or disable Database Caching. %s'=>'这个数据库附件文件 db.php 不是一个 W3 Total Cache 高级缓存文件.
                    删除这个文件或者关闭数据库缓存. %s','User is logged in'=>'用户已登录','Query is rejected'=>'查询被拒绝','Short init'=>'短初始化','wp-admin'=>'wp-admin','XMLRPC request'=>'XMLPRC请求','Application request'=>'应用请求','Doing cron'=>'正在执行cron作业任务','Cookie is rejected'=>'Cookie被拒绝','Request URI is rejected'=>'请求的 URI 拒绝','Doing AJAX'=>'处理AJAX文件','DONOTCACHEDB constant is defined'=>'DONOTCACHEDB 常数定义','Database caching is disabled'=>'数据库缓存已禁用','Request-wide '=>'整个请求 ','PageCache Garbage cleanup triggered successfully.'=>'页面缓存回收站清空成功.','Files purged successfully.'=>'数据清除成功.','Files did not successfully purge with error %s'=>'数据清除失败. 错误: %s','The query string was updated successfully.'=>'更新队列字符串成功.','updating the query string failed. with error %s'=>'更新队列字符串失败. 错误: %s','The page cache is flushed successfully.'=>'页面缓存已被成功清空.','Flushing the page cache failed.'=>'刷新页面缓存失败。','This is not a valid post id.'=>'这不是一个有效的网站ID.','The page is flushed from cache successfully.'=>'页面已从缓存成功清空.','Flushing the page from cache failed.'=>'从页面中清空缓存失败.','The object cache is flushed successfully.'=>'对象缓存已被成功清空.','Flushing the object cache failed.'=>'清空对象缓存失败.','The minify cache is flushed successfully.'=>'精简压缩的缓存已被成功清空.','Flushing the minify cache failed.'=>'清除精简压缩的缓存失败.','The DB cache is flushed successfully.'=>'数据库缓存已被成功清空.','Flushing the DB cache failed.'=>'清空数据库缓存失败.','CDN module: Required Database SQL'=>'CDN模块:需要数据库SQL','Failure to pull zone: '=>'下载区域失败： ','Malformed Authorization Key.'=>'格式不正确的授权码。','Empty Authorization Key.'=>'空的鉴权密钥.','Server Error'=>'服务器错误','Invalid Request'=>'无效的请求','Invalid Request URI'=>'无效的请求URI','Authentication Failure or Insufficient Access Rights'=>'鉴权失败/访问权限不足','Invalid Request Parameter'=>'无效的请求参数','Empty token.'=>'空令牌。','Empty account #.'=>'清空帐户#.','Empty zones list.'=>'空的区域列表.','Unable to purge (%s).'=>'无法清除(%s)','Invalid response.'=>'无效的响应。','Constructor error (%s).'=>'构造函数的错误 (%s).','Empty password.'=>'空密码。','Empty username.'=>'空用户名.','Network Deactivate'=>'网络停用','Deactivate this plugin'=>'停用此插件','The following plugins are not compatible with W3 Total Cache and will cause unintended results:'=>'以下插件与W3 Total Cache不兼容，会导致意想不到的结果：','Invalid engine.'=>'无效的引擎。','Empty JAR file path.'=>'空的 JAR 文件路径。','Empty JAVA executable path.'=>'空的 JAVA 可执行文件的路径。','Test failed.'=>'测试失败。','Test passed.'=>'测试通过。','Suggest a New Feature'=>'建议的新功能','Submit a Bug Report'=>'提交一个 Bug 报告','Added by W3 Total Cache'=>'已由 W3 Total Cache 添加','You do not have the rights to perform this action.'=>'您没有执行此操作的权限.','Database Cluster configuration file has been successfully saved'=>'数据库群集配置文件已成功保存','Content-Disposition: attachment; filename=%s.json'=>'内容处理：附件;文件名=%s.json','Created successfully.'=>'创建成功。','Incorrect type.'=>'类型不正确。','Error: %s'=>'错误: %s','Test passed'=>'测试通过','Incorrect engine '=>'引擎不正确 ','Empty files list.'=>'空文件列表。','Content Delivery Network (CDN): Purge Tool'=>'内容分发网络(CDN):清除工具','Custom files export'=>'自定义文件导出','Minify files export'=>'压缩文件导出','Theme files export'=>'主题文件导出','Includes files export'=>'包括文件导出','Modify attachment URLs'=>'更改附件URL','Media Library import'=>'媒体库导入','Media Library export'=>'媒体库导出','Unsuccessful file transfer queue.'=>'文件传输失败。','Number of processed queue items: %d'=>'队列中已处理的文件数量:%d','Queue successfully emptied.'=>'队列成功清空.','File successfully deleted from the queue.'=>'文件成功地从队列中删除.','Database'=>'数据库','unknown'=>'未知','Fraud'=>'作弊','Suspended'=>'暂停','Cancelled'=>'已取消','Active'=>'激活','Pending'=>'待定','Select zone'=>'영역선택','Date:'=>'日期：','Upgrade your New Relic account to enable more metrics.'=>'升级你的New Relic的帐户，以使更多的指标。','Subscription level:'=>'订阅级别：','PHP agent:'=>'PHP 代理:','Database times'=>'数据库时间','Web Transaction times'=>'Web交易时间','Page load times'=>'页面加载时间','Top 5 slowest times'=>'前5最慢时间','Average times'=>'平均时间','You have not configured API key and Account Id.'=>'您尚未配置 API 密钥和帐号 ID.','Current customers'=>'现有客户','Swarmify is a service that lets you speed up your site even more with W3 Total Cache.'=>'Swarmify 是一项可让您使用 W3 Total Cache 加快网站速度的服务。','New customers'=>'新客户','Hits'=>'点击','File'=>'文件','Content Breakdown'=>'内容分解','Requests'=>'请求','Report - 7 days'=>'报告-7天','Reports'=>'报告','Manage'=>'管理','This widget requires JavaScript.'=>'这个小部件需要 JavaScript。','Loading&#8230;'=>'加载&#8230;','Refresh Analysis'=>'刷新分析','e.g.: domain.com'=>'例如: domain.com','Domains to rename:'=>'域名重命名：','Purge queue is empty'=>'清除队列为空','Empty purge queue'=>'清空清除队列','Delete queue is empty'=>'删除队列为空','Empty delete queue'=>'空删除队列','Upload queue is empty'=>'上传队列为空','Process CDN queue now'=>'立刻处理CDN队列','Empty upload queue'=>'空的上传队列','Date'=>'日期','Last Error'=>'上次错误','Remote Path'=>'远程路径','Local Path'=>'本地路径','Purge queue'=>'清除队列','Delete queue'=>'删除队列','Upload queue'=>'上传队列','This tool lists the pending file uploads and deletions.'=>'此工具将列出上传和删除的待处理文件。','Files to purge:'=>'要清除的文件：','the specific file with its extension, with the specific parameters.'=>'其扩展名，具有特定参数的具体文件。','the specific file with its extension, with all variation of parameters.'=>'特定的文件，其扩展名，与所有的参数变化。','the specific file with its extension, and without parameters.'=>'特定的文件，其扩展名和不带参数。','the specific file (when the file does not have an extension), and without parameters.'=>'特定的文件 （当文件没有扩展名），和不带参数。','all files in the directory whose extension is "jpg".'=>'在对其扩展名是"jpg"目录中的所有文件。','all files in the directory with no extension, with all parameter variations.'=>'不带扩展名，所有的参数变化与目录中的所有文件。','the directory itself (only when accessed directly without any file).'=>'该目录本身 （仅在访问时直接没有任何文件）。','Remove objects from the %1$sCDN%2$s by specifying the relative path on individual lines below and clicking the "Purge" button when done. For example:'=>'从 %1$sCDN%2$s 中删除对象，方法是在下面各行中指定相对路径，完成后点击 "清除 "按钮。例如：','Add the following directives to your .htaccess file or if there are several hundred they should be added directly to your configuration file:'=>'将以下指令添加到.htaccess文件中，或者如果有几百个，应直接添加到配置文件中：','Create a list of permanent (301) redirects for use in your site\'s .htaccess file'=>'在您的站点的.htaccess 文件中创建永久 (301) 重定向为使用列表','Total posts:'=>'文章总数：','This tool will copy post or page attachments into the Media Library allowing WordPress to work as intended.'=>'这个工具将复制的文章或页面附件到媒体库允许WordPress的工作打算。','Total media library attachments:'=>'总媒体库附件：','Start'=>'开始','Last response:'=>'最后回应：','Time elapsed:'=>'已用时间:','Status:'=>'状态：','Processed:'=>'已处理:','Total files:'=>'文件总数：','This tool will upload files of the selected type to content delivery network provider.'=>'此工具会将所选类型的文件上载到内容传送网络提供商。','Request premium services, suggest a feature or submit a bug using the form below:'=>'使用以下表格申请高级服务，建议功能或提交错误：','Forum Topic URL: '=>'论坛主题 URL： ','Phone: '=>'电话： ','Subject: '=>'主题： ','E-Mail: '=>'电子邮箱： ','Report <acronym title="Uniform Resource Locator">URL</acronym>:'=>'报告 <acronym title="统一资源定位器"> URL </acronym>：','No groups added. All referrers recieve the same page and minify cache results.'=>'没有添加任何组。 所有引荐来源都会收到相同的页面并缩小缓存结果。','Specify the referrers for this group. Remember to escape special characters like spaces, dots or dashes with a backslash. Regular expressions are also supported.'=>'指定此组的引荐来源。 请记住使用反斜杠转义空格，点或破折号等特殊字符。 也支持正则表达式。','Referrers:'=>'引荐：','A 302 redirect is used to send this group of referrers to another hostname (domain).'=>'302重定向用于将此组引用者发送到另一个主机名（域）。','Assign this group of referrers to a specific theme. Selecting "Pass-through" allows any plugin(s) (e.g. referrer plugins) to properly handle requests for these referrers. If the "redirect users to" field is not empty, this setting is ignored.'=>'将此组引荐分配给特定主题。 选择“传递”允许任何插件（例如引用者插件）正确处理对这些引用者的请求。 如果“将用户重定向到”字段不为空，则忽略此设置。','-- Pass-through --'=>'-- 传递  --','Delete group'=>'删除组','of referrers by specifying names in the referrers field. Assign a set of referrers to use a specific theme, redirect them to another domain, create referrer groups to ensure that a unique cache is created for each referrer group. Drag and drop groups into order (if needed) to determine their priority (top -&gt; down).'=>'通过在referrers字段中指定名称来引用。 分配一组引用来使用特定主题，将它们重定向到另一个域，创建引用者组以确保为每个引用者组创建唯一缓存。 将组拖放到订单（如果需要）以确定其优先级（(top -&gt; down）。','Specify fragment groups that should be managed by W3 Total Cache. Enter one action per line comma delimited, e.g. (group, action1, action2). Include the prefix used for a transient by a theme or plugin.'=>'指定应由W3 Total Cache管理的片段组。 每行以逗号分隔输入一个操作，例如： （group，action1，action2）。 包含主题或插件用于瞬态的前缀。','No groups have been registered.'=>'没有群组已注册.','The groups above will be flushed upon setting changes.'=>'该群组将上述设置后，改变被刷新.','Registered fragment groups:'=>'已注册碎片组：','Overview'=>'概述','if needed.'=>'如果需要的话.','Specify additional page headers to cache.'=>'指定要缓存的其他页眉。','Cache the specified pages even if they don\'t have trailing slash.'=>'即使指定的页没有尾随斜线，也要缓存它们。','Cache the specified pages / directories even if listed in the "never cache the following pages" field. Supports regular expression (See %1$s%2$sFAQ%3$s%4$s)'=>'缓存指定的页面/目录，即使列在“从不缓存以下页面”字段中。支持正则表达式（请参阅%1$s%2$s常见问题解答%3$s%4$s）','Always ignore the specified pages / directories. Supports regular expressions (See %1$s%2$sFAQ%3$s%4$s)'=>'始终忽略指定的页面/目录。支持正则表达式（参见 %1$s%2$sFAQ%3$s%4$s）','Never cache pages that use the specified cookies.'=>'切勿缓存使用指定cookie的页面。','Never send cache pages for these user agents.'=>'永远不要为这些用户代理发送缓存页面。','Reject HEAD requests:'=>'拒绝HEAD请求：','Resolve issues incorrect odd character encoding that may appear in cached pages.'=>'解决可能出现在缓存页面中的错误奇数字符编码问题。','Charset:'=>'字符集：','Compatibility mode:'=>'兼容模式：','Enables support for WordPress functionality in fragment caching for the page caching engine. Use of this feature may increase response times.'=>'在页面缓存引擎的片段缓存中支持WordPress功能。 使用此功能可能会增加响应时间。','Late initialization:'=>'延迟初始化：','Specify a regular expression that matches your sitemaps.'=>'指定与站点地图匹配的正则表达式。','Specify additional pages to purge. Including parent page in path. Ex: parent/posts.'=>'指定要清除的其他页面。 在路径中包含父页面。 例如：parent/posts.','Specify number of pages that lists posts (archive etc) that should be purged on post updates etc, i.e example.com/ ... example.com/page/5. %1$s0 means all pages that lists posts are purged, i.e example.com/page/2 ... .'=>'指定列出应在帖子更新等时清除的帖子（存档等）的页面数量，即 example.com/...example.com/page/5。%1$s0表示列出帖子的所有页面都将被清除，即 example.com/page/2... 。','Specify the pages and feeds to purge when posts are created, edited, or comments posted. The defaults are recommended because additional options may reduce server performance:'=>'指定在发布，编辑或发布帖子时清除的页面和订阅源。 建议使用默认值，因为其他选项可能会降低服务器性能：','Purge Policy: '=>'清除策略: ','Limit the number of pages to create per batch. Fewer pages may be better for under-powered servers.'=>'限制每批创建的页数。 对于功能不足的服务器，页面可能更少。','The number of seconds to wait before creating another set of cached pages.'=>'创建另一组缓存页面之前等待的秒数。','Select user roles that should not receive cached pages:'=>'选择不应接收缓存页面的用户角色：','Unauthenticated users may view a cached version of the last authenticated user\'s view of a given page. Disabling this option is not recommended.'=>'未经身份验证的用户可以查看最后一个经过身份验证的用户对给定页面的视图的缓存版本。 建议不要禁用此选项。','Reduce server load by caching 404 pages. If the disk enhanced method of disk caching is used, 404 pages will be returned with a 200 response code. Use at your own risk.'=>'通过缓存404页面减少服务器负载。 如果使用磁盘增强的磁盘缓存方法，将返回404页面，其中包含200个响应代码。 使用风险由您自己承担。','Search result (and similar) pages will be cached if enabled.'=>'如果启用，搜索结果（和Similar）页面将被缓存。','By default the front page is cached when using static front page in reading settings.'=>'默认的前台页面高速缓存使用静态首页中读取设置时。','For many blogs this is your most visited page, it is recommended that you cache it.'=>'对于很多博客这您参观人数最多的页面，建议你对它的缓存。','Page caching via %1$s is currently %2$s'=>'页面缓存通过：%1$s来缓存，目前状态：%2$s','Enabling this option will increase load on server on certain actions but will guarantee that the Object Cache is always clean and contains latest changes. %1$sEnable if you are experiencing issues with options displaying wrong value/state (checkboxes etc).%2$2'=>'启用此选项将增加服务器上某些操作的负载，但将保证对象缓存始终是干净的，并且包含最新的更改。%1$s如果遇到显示错误值/状态（复选框等）的选项的问题，请启用。%2$2','Groups that should not be cached.'=>'不应该被缓存的群体。','Groups shared amongst sites in network mode.'=>'网络模式中的站点间共享的组.','Object caching via %1$s is currently %2$s.'=>'通过 %1$s 进行的对象缓存目前为 %2$s。','This is required when using New Relic on a network install to set the proper names for sites.'=>'这是必需的当使用New Relic在网络安装设置站点的专名。','Behavior Settings'=>'行为设置','How many minutes data retrieved from New Relic should be stored. Minimum is 1 minute.'=>'多少分钟从New Relic应该存储检索数据。最低是1分钟。','Dashboard Settings'=>'仪表板设置','Application settings are only visible when New Relic is enabled'=>'当启用New Relic时，应用程序设置是可见的','Application settings could not be retrieved. New Relic may not be properly configured, %1$sreview the settings%2$s.'=>'无法检索应用程序设置。新遗物可能未正确配置，%1$s查看设置%2$s。','Application Settings'=>'应用程序设置','Per the above, make sure that visitors are notified about the cookie as per any regulations in your market.'=>'根据上述内容，请确保根据您所在市场的任何规定向访问者通知cookie。','Enabling even a single user agent group will set a cookie called "w3tc_referrer." It is used to ensure a consistent user experience across page views. Make sure any reverse proxy servers etc respect this cookie for proper operation.'=>'即使启用单个用户代理组，也会设置一个名为“w3tc_referrer”的cookie。 它用于确保跨页面视图的一致用户体验。 确保任何反向代理服务器等尊重此cookie以进行正确操作。','No groups added. All user agents recieve the same page and minify cache results.'=>'没有添加的组。所有的用户代理接收相同的页和缩小缓存结果。','Specify the user agents for this group. Remember to escape special characters like spaces, dots or dashes with a backslash. Regular expressions are also supported.'=>'指定此组的用户代理。记住要像空格、 圆点或短划线以反斜杠的特殊字符进行转义。此外支持正则表达式。','User agents:'=>'用户代理:','A 302 redirect is used to send this group of users to another hostname (domain); recommended if a 3rd party service provides a mobile version of your site.'=>'一个 302 重定向用于将此用户组发送到另一个主机名 （域）;建议如果第三方服务提供您的网站的移动版本。','Redirect users to:'=>'将用户重定向到：','Assign this group of user agents to a specific theme. Selecting "Pass-through" allows any plugin(s) (e.g. mobile plugins) to properly handle requests for these user agents. If the "redirect users to" field is not empty, this setting is ignored.'=>'分配这个组中的用户代理到一个特定的主题。选择 "直通 "允许任何插件(s)（如移动插件）妥善处理这些用户代理的请求。如果"将用户重定向到"字段不为空，将忽略此设置.','Enabled:'=>'启用：','Group name:'=>'组名称：','of user agents by specifying names in the user agents field. Assign a set of user agents to use a specific theme, redirect them to another domain or if an existing mobile plugin is active, create user agent groups to ensure that a unique cache is created for each user agent group. Drag and drop groups into order (if needed) to determine their priority (top -&gt; down).'=>'通过在用户代理字段中指定名称来用户代理。 分配一组用户代理以使用特定主题，将它们重定向到另一个域，或者如果现有移动插件处于活动状态，请创建用户代理组以确保为每个用户代理组创建唯一缓存。 将组拖放到订单（如果需要）以确定其优先级（top -&gt; down）。','Create a group'=>'创建一个组','Specify external files/libraries that should be combined.'=>'指定应合并的外部文件/库。','Specify user agents that will never receive minified content.'=>'指定用户代理中永远不会接收压缩的内容。','Specify the interval between download and update of external files in the minify cache. Hint: 6 hours is 21600 seconds. 12 hours is 43200 seconds. 24 hours is 86400 seconds.'=>'指定在缩小缓存下载外部文件更新之间的时间间隔。提示：6小时为21600秒。12小时是43200秒。24小时是86400秒。','Add a style sheet'=>'添加样式表','Files are minified by template. First select the theme to manage, then add style sheets used in all templates to the "All Templates" group. Use the menu above to manage style sheets unique to a specific template. If necessary drag &amp; drop to resolve dependency issues (due to incorrect order).'=>'文件压缩的模板。首先选择要管理的主题，然后将样式表添加到“所有模板”组中使用的所有模板中。使用上面的菜单管理独特的样式表到一个特定的模板。如果必要的拖放，解决依赖问题（由于不正确的顺序）。','Add a script'=>'添加一个脚本','Files are minified by template. First select the theme to manage, then add scripts used in all templates to the "All Templates" group. Use the menu above to manage scripts unique to a specific template. If necessary drag &amp; drop to resolve dependency issues (due to incorrect order).'=>'文件按模板缩小。 首先选择要管理的主题，然后将所有模板中使用的脚本添加到“所有模板”组。 使用上面的菜单管理特定模板特有的脚本。 必要时拖拽和 删除以解决依赖性问题（由于错误的顺序）。','Theme:'=>'主题：','Non-blocking using "asyncsrc"'=>'使用“asyncsrc”进行非阻塞','Non-blocking using "extsrc"'=>'使用“extsrc”进行非阻塞','Non-blocking using "defer"'=>'使用“延迟”进行无阻塞','Non-blocking using "async"'=>'使用“异步”进行非阻塞','Non-blocking using JS'=>'使用JS进行非阻塞','Default (blocking)'=>'默认（阻止）','Operations in areas:'=>'区域设置：','Do not remove comments that contain these terms.'=>'请勿删除包含这些字词的评论。','Notify when minify cache creation errors occur.'=>'发生缩小缓存创建错误时通知。','Both Admin &amp; Email Notification'=>'管理员和电子邮件通知','Email Notification'=>'电子邮件通知','Admin Notification'=>'管理员通知','Authenticated users will not receive minified pages if this option is enabled.'=>'如果启用此选项，则经过身份验证的用户将不会收到缩小的页面。','%1$s to make existing file modifications visible to visitors with a primed cache.'=>'%1$s，使现有文件修改对具有预处理缓存的访问者可见。','wizard.'=>'向导.','help'=>'帮助','Get minify hints using the'=>'获取使用压缩提示','Minify via %1$s is currently %2$s.'=>'Minify via %1$s 目前正在%2$s。','symbols (set to 0 to disable)'=>'符号 (设置为0禁用)','Path to JAR file:'=>'JAR路径：','Path to JAVA executable:'=>'JAVA可执行文件的路径：','Test YUI Compressor'=>'测试 YUI 压缩器','Merge selectors with the same properties (fast)'=>'合并有相同属性的选择符（快速)','Only seperate selectors (split at ,)'=>'只有独立的选择（分割时,)','Do not change anything'=>'什么都不用改','Uppercase'=>'大写','Lowercase'=>'小写','None'=>'无','All optimisations'=>'所有优化','Safe optimisations'=>'安全优化','Don\'t optimise'=>'不优化','Low (higher readability)'=>'低（注重可读性)','Standard (balance between readability and size)'=>'标准 (可读性和大小之间的平衡)','High (moderate readability, smaller size)'=>'高 (可读性一般，尺寸更小)','Highest (no readability, smallest size)'=>'最高 (没有可读性，最小尺寸)','Test Closure Compiler'=>'测试关闭编译器','Advanced optimizations'=>'高级优化','Simple optimizations'=>'简单优化','Whitespace only'=>'仅空白','Consider using memcached for objects that must persist across web server restarts or that you wish to share amongst your server pool, e.g.: database objects or page cache.'=>'考虑将memcached用于必须在Web服务器重新启动时保留的对象，或者您希望在服务器池之间共享的对象，例如：数据库对象或页面缓存。','Restarting the web server will empty the opcode cache, which means it will have to be rebuilt over time and your site\'s performance will suffer during this period. Still, an opcode cache should be installed in any case to maximize WordPress performance.'=>'重新启动Web服务器将清空操作码缓存，这意味着它将不得不重建，并且在此期间您的站点性能将受到影响。 不过，在任何情况下都应安装操作码缓存以最大化WordPress性能。','In the case where Apache is not used, the .htaccess file located in the root directory of the WordPress installation, wp-content/w3tc/pgcache/.htaccess and wp-content/w3tc/min/.htaccess contain directives that must be manually created for your web server software.'=>'在不使用Apache的情况下，位于WordPress安装的根目录中的.htaccess文件，wp-content / w3tc / pgcache / .htaccess和wp-content / w3tc / min / .htaccess包含必须手动执行的指令 为您的Web服务器软件创建。','Other'=>'其他','Rewrite Rules'=>'重写规则','%1$sOptional:%2$s On the "%3$sObject Cache%4$s" tab the recommended settings are preset. If using a shared hosting account use the "disk" method with caution, the response time of the disk may not be fast enough, so this option is disabled by default. Test this option with and without database cache to ensure that it provides a performance increase.'=>'%1$s可选：%2$s在“%3$s对象缓存%4$s”选项卡上，预设了建议的设置。如果使用共享主机帐户请谨慎使用“磁盘”方法，则磁盘的响应时间可能不够快，因此默认情况下禁用此选项。使用和不使用数据库缓存测试此选项，以确保它能提高性能。','%1$sOptional:%2$s On the "%3$sDatabase Cache%4$s" tab the recommended settings are preset. If using a shared hosting account use the "disk" method with caution; in either of these cases the response time of the disk may not be fast enough, so this option is disabled by default.'=>'%1$s可选：%2$s 在"%3$s数据库缓存%4$s"选项卡上，预设了推荐设置。如果使用共享主机账户，请谨慎使用 "磁盘 "方法；在这两种情况下，磁盘的响应速度可能不够快，因此默认禁用此选项。','1. The "Compatibility Mode" option found in the advanced section of the %1$s"Page Cache Settings"%2$s tab will enable functionality that optimizes the interoperablity of caching with WordPress, is disabled by default, but highly recommended. Years of testing in hundreds of thousands of installations have helped us learn how to make caching behave well with WordPress. The tradeoff is that disk enhanced page cache performance under load tests will be decreased by ~20%% at scale.'=>'1.在“页面缓存设置”选项卡的高级部分中%1$s“页面缓存设置”%2$s选项卡中的“兼容模式”选项将启用优化与WordPress缓存的互操作性的功能，默认情况下处于禁用状态，但强烈建议使用。在数十万次安装中多年的测试帮助我们学会了如何使用WordPress使缓存运行良好。权衡是，负载测试下的磁盘增强型页面缓存性能将大规模降低约 20%%。','On the "%1$sGeneral%2$s" tab and select your caching methods for page, database and minify. In most cases, "disk enhanced" mode for page cache, "disk" mode for minify and "disk" mode for database caching are "good" settings.'=>'在“%1$s一般%2$s”选项卡上，选择页面，数据库和缩小的缓存方法。在大多数情况下，用于页面缓存的“磁盘增强”模式、用于缩小的“磁盘”模式和用于数据库缓存的“磁盘”模式都是“良好”设置。','Set the permissions of wp-content/ back to 755, e.g.:'=>'将wp-content /的权限设置为755，例如：','Revert all settings to the defaults. Any settings staged in preview mode will not be modified.'=>'将所有设置恢复为默认值. 在预览模式中暂存的任何设置不会被修改.','Restore Default Settings'=>'恢复默认设置','Reset configuration:'=>'重置配置：','Download the active settings file.'=>'下载活动设置文件。','Download'=>'下载','Export configuration:'=>'导出配置：','Upload and replace the active settings file.'=>'上传并替换活动设置文件。','Upload'=>'上传','Import configuration:'=>'导入配置:','Debug mode:'=>'调试模式：','Try this option if your hosting environment uses a network based file system for a possible performance improvement.'=>'如果您的主机环境使用基于网络的文件系统来提高性能, 请尝试此选项.','Enable file locking'=>'启用文件锁定','If empty the default path will be used..'=>'如果为空，将使用默认路径..','Only one configuration file for whole network will be created and used. Recommended if all sites have the same configuration.'=>'将只创建和使用整个网络的一个配置文件.  建议在所有网站具有相同的配置.','Display Google PageSpeed results on the WordPress dashboard.'=>'在 WordPress 仪表板上显示 Google PageSpeed 结果。','upgrading'=>'升级','Verify license key'=>'验证许可证密钥','Application name:'=>'应用名称：','Specify the Amazon %1$sSNS%2$s service endpoint hostname. If empty, then default "sns.us-east-1.amazonaws.com" will be used.'=>'指定 Amazon %1$sSNS%2$s 服务端点主机名。如果为空，则使用默认的 "sns.us-east-1.amazonaws.com"。','Reverse Proxy'=>'反向代理','Self-hosted / File Transfer Protocol Upload'=>'自托管/文件传输协议上传','Rackspace Cloud Files'=>'Rackspace云存储','Microsoft Azure Storage'=>'微软Azure云存储','Amazon Simple Storage Service (S3)'=>'亚马逊简单存储服务（S3）','Generic Mirror'=>'通用镜像','Cotendo (Akamai)'=>'Cotendo (Akamai)','AT&amp;T'=>'AT&amp;T','Amazon CloudFront'=>'亚马逊CloudFront CDN','Akamai'=>'Akamai','<acronym title="Content Delivery Network">CDN</acronym>'=>'<acronym title="Content Delivery Network">CDN</acronym>','Reduce server load and decrease response time by using the cache available in site visitor\'s web browser.'=>'通过在网站访问者的Web浏览器中使用缓存来减少服务器的负载，减少响应时间。','Shared Server:'=>'共享服务器：','Caching database objects decreases the response time of your site. Best used if object caching is not possible.'=>'缓存数据库对象，降低您的网站的响应时间。最佳使用，如果对象缓存是不可能的.','CSS Tidy'=>'CSS Tidy','YUI Compressor'=>'YUI 压缩工具','JSMin (default)'=>'JSMin (默认)','HTML Tidy'=>'HTML清理','Disk'=>'磁盘','Select manual mode to use fields on the minify settings tab to specify files to be minified, otherwise files will be minified automatically.'=>'选择手动模式用在缩小设置标签字段指定要压缩的文件，否则文件将自动缩小。','Manual'=>'手动','Auto'=>'自动','Minify'=>'压缩','Memcached'=>'内存缓存','Multiple Servers:'=>'多个服务器：','Opcode: WinCache'=>'操作码：WinCache','Opcode: XCache'=>'操作码：XCache','Opcode: eAccelerator'=>'操作码：eAccelerator','Opcode: Alternative PHP Cache (APC / APCu)'=>'操作码: 替代 php 缓存 (apc/apcu)','Dedicated / Virtual Server:'=>'专用/虚拟服务器：','Disk: Enhanced'=>'磁盘：增强','Disk: Basic'=>'磁盘：基本','Shared Server (disk enhanced is best):'=>'共享服务器（磁盘增强是最好的）：','Caching pages will reduce the response time of your site and increase the scale of your web server.'=>'缓存页面会减少您的网站的响应时间, 并提高 Web 服务器的规模.','Use preview mode to test configuration scenarios prior to releasing them (deploy) on the actual site. Preview mode remains active even after deploying settings until the feature is disabled.'=>'使用预览模式之前，释放之前的（部署），实际应用测试的配置方案。甚至直到功能被禁用部署设置后，预览模式仍然有效。','To preview any changed settings (without deploying): %1$s'=>'预览任何更改的设置（不部署）：%1$s','Deploy'=>'部署','Disable'=>'停用','Save Settings'=>'保存设置','Visit extension site'=>'访问扩展网站','Visit author homepage'=>'访问作者主页','By %s'=>'由 %s','Version %s'=>'%s版本','Requirements: %s'=>'必须条件: %s','Activate this extension'=>'激活此扩展','Deactivate this extension'=>'停用此扩展程序','Description'=>'描述','Extension'=>'扩展','Select All'=>'全选','%s extension'=>'%s 扩展','Apply'=>'应用','Deactivate'=>'停用','Activate'=>'激活','Bulk Actions'=>'批量操作','Save configuration file'=>'保存配置文件','Note: Changes will have immediate effect on your database configuration. If the application stops working creating the settings file, edit or remove this configuration file manually at %1$s/wp-content/db-cluster-config.php%2$s.'=>'注意：更改会立即影响数据库配置。如果应用程序在创建设置文件时停止工作，请在 %1$s/wp-content/db-cluster-config.php%2$s 下手动编辑或删除此配置文件。','Database Cluster Configuration File'=>'数据库群集配置文件','here'=>'这里','Please enter the license key you received after successful checkout %1$s.'=>'请输入您在成功结账后收到的许可证密钥%1$s。','upgrade'=>'升级','Do not cache queries that contain these words or regular expressions.'=>'不要缓存包含这些单词或正则表达式的查询.','Do not cache queries that contain these terms. Any entered prefix (set in wp-config.php) will be replaced with current database prefix (default: wp_). Query stems can be identified using debug mode.'=>'不要缓存包含这些术语的查询. 任何输入的前缀 (在 wp-config.php 中设置) 都将替换为当前数据库前缀 (默认值: wp_). 可以使用调试模式识别查询词组.','Always ignore the specified pages / directories. Supports regular expressions (See %1$s%2$sFAQ%3$s%4$s).'=>'始终忽略指定的页面/目录。支持正则表达式（请参阅%1$s%2$s常见问题解答%3$s%4$s）。','If caching to disk, specify how frequently expired cache data is removed. For busy sites, a lower value is best.'=>'如果缓存到磁盘，请指定删除过期缓存数据的频率。 对于繁忙的站点，较低的值是最好的。','Determines the natural expiration time of unchanged cache items. The higher the value, the larger the cache.'=>'确定未更改的缓存项的自然到期时间。 值越高，缓存越大。','Multiple servers may be used and seperated by a comma; e.g. 127.0.0.1:11211, domain.com:11211'=>'可以使用多个服务器，并用逗号隔开；例如，127.0.0.1:11211、domain.com:11211','Test'=>'测试','Enabling this option is recommended to maintain default WordPress behavior.'=>'建议启用此选项以保持默认的WordPress行为。','Database caching via %1$s is currently %2$s.'=>'通过 %1$s 进行的数据库缓存目前为 %2$s。','Dashboard'=>'仪表盘','Manage Referrer Groups'=>'管理引用组','Manage User Agent Groups'=>'管理用户代理组','Media'=>'媒体','Note(s)'=>'备注','Purge Policy'=>'清除策略','Cache Preload'=>'缓存预加载','Main Menu'=>'主菜单','Import / Export Settings'=>'导入/导出设置','Debug'=>'调试','Miscellaneous'=>'杂项','Licensing'=>'许可','Monitoring'=>'监控','Browser Cache'=>'浏览器缓存','Fragment Cache'=>'片段缓存','Object Cache'=>'对象缓存','Database Cache'=>'数据库缓存','Page Cache'=>'页面缓存','You can use placeholders {wp_content_dir}, {plugins_dir}, {uploads_dir} instead of writing folder paths (wp-content, wp-content/plugins, wp-content/uploads).'=>'您可以使用占位符 {wp_content_dir}, {plugins_dir}, {uploads_dir}, 而不是编写文件夹路径 (wp-content, wp-content/plugins, wp-content/uploads).','Note(s):'=>'备注(s):','To upload files in blogs.dir for current blog write wp-content/&lt;currentblog&gt;/.'=>'要在当前博客的blogs.dir中上传文件，请写入wp-content /＆lt; current blog＆gt; /。','Automatically import files hosted with 3rd parties of these types (if used in your posts / pages) to your media library.'=>'自动将由这些类型的第三方托管的文件（如果在您的帖子/页面中使用）导入到媒体库。','Number of files processed per upload attempt.'=>'每次尝试上传处理的文件数量。','The number of seconds to wait before upload attempt.'=>'尝试上传前的等待秒数。','Specify the interval between upload of changed files.'=>'指定上传和更改文件的时间间隔。','Automatically attempt to find and upload changed files.'=>'自动尝试去查找和上传被修改的文件。','Automatically upload minify files'=>'自动上传缩小文件','Always ignore the specified pages / directories. Supports regular expression (See %1$s%2$sFAQ%3$s%4$s)'=>'始终忽略指定的页面/目录。支持正则表达式（请参阅%1$s%2$s常见问题解答%3$s%4$s）','Select user roles that will use the origin server exclusively:'=>'选择将独占使用原始服务器的用户角色:','Advanced'=>'高级','Configuration'=>'配置','If modified files are not always detected and replaced, use this option to over-write them.'=>'如果修改的文件不总是被检测和替换，请使用此选项覆盖它们。','Upload custom files'=>'上传自定义文件','Upload minify files'=>'上传缩小文件','Upload theme files'=>'上传主题文件','Upload includes files'=>'包括文件上传','Upload attachments'=>'上传附件','to make existing file modifications visible to visitors with a primed cache.'=>'以使现有文件修改对带有加注缓存的访问者可见。','Update media query string'=>'更新媒体查询字符串','if the domain name of your site has ever changed.'=>'如果您的网站的域名已更改.','Purge'=>'清除','if some objects appear to be missing.'=>'如果一些对象似乎丢失。','unsuccessful file transfers'=>'不成功的文件传输','importing attachments into the Media Library'=>'将附件导入到媒体库','Content Delivery Network support via %1$s is currently %2$s.'=>'通过%1$s提供支持的CDN 现在被通过 %2$s提供.','Test S3 upload'=>'测试 S3 上传','Test Cloud Files upload'=>'测试云文件上传','Location:'=>'位置：','<acronym title="Application Programming Interface">API</acronym> key:'=>'<acronym title="Application Programming Interface">API</acronym> 密钥:','Test Mirror'=>'测试 Mirror','Authorize'=>'授权','Specify account credentials:'=>'指定帐户凭据：','Sign Up Now and Save 25%'=>'现在注册享受75折优惠,为您节省25%','Create account:'=>'创建账户:','Test FTP server'=>'测试 FTP 服务器','Specify the directory where files must be uploaded to be accessible in a web browser (the document root).'=>'指定要在Web浏览器中（文档根）上载文件必须被访问的目录。','Specify the server\'s address, e.g.: "ftp.domain.com". Try "127.0.0.1" if using a sub-domain on the same server as your site.'=>'指定服务器的地址，例如：“FTP。域名.com”。尝试“127.0.0.1”如果使用同一服务器上的子域名为您的网站。','Enable this option only if there are connectivity issues, otherwise it\'s not recommended.'=>'只有当有连接问题时才启用此选项，否则不推荐使用。','Test EdgeCast'=>'测试EdgeCast','Test Cotendo'=>'测试cotendo','Zones to purge:'=>'要清除的区域:','Add CNAME'=>'添加CNAME','Delete'=>'删除','(reserved for JS before </body>)'=>'（预留在JS前</body>）','(reserved for JS after <body>)'=>'（为JS<body>后预留）','(reserved for JS in <head>)'=>'（为JS<head>内预留）','(reserved for CSS)'=>'（留给CSS）','Test CloudFront distribution'=>'CloudFront分布测试','Create distribution'=>'创建分布','Origin:'=>'来源:','Test S3 upload &amp; CloudFront distribution'=>'S3上传测试 &amp; CloudFront分布','Bucket:'=>'Bucket:','Secret key:'=>'密钥:','Access key ID:'=>'访问密钥ID：','Test Microsoft Azure Storage upload'=>'测试微软的 Azure 存储上传','or CNAME:'=>'或 CNAME:','Create container'=>'创建容器','Container:'=>'容器:','Account key:'=>'帐户key:','Account name:'=>'帐户名:','Token:'=>'令牌：','Account #:'=>'帐户 #:','Test akamai'=>'测试 akamai','Replace site\'s hostname with:'=>'将网站的主机名替换为：','Disabled (always use HTTP)'=>'关闭(强制HTTP)','Enabled (always use SSL)'=>'开启(强制SSL)','Auto (determine connection type automatically)'=>'自动（自动确定连接类型）','Purge action:'=>'清除操作:','Domain to purge:'=>'要清除的域名:','Specify email addresses for completed removal notifications. One email per line.'=>'指定已完成删除通知的邮箱地址. 每行一个邮箱地址.','Email notification:'=>'电子邮件通知：','Password:'=>'密码：','Username:'=>'用户名：','cache ("public")'=>'缓存 ("public")','Media &amp; Other Files'=>'媒体&amp;其它文件','no-cache ("max-age=0, private, no-store, no-cache, must-revalidate")'=>'不缓存（“最大年龄= 0，没有商店，没有私人缓存，必须重新验证”）','cache with validation ("public, must-revalidate, proxy-revalidate")'=>'具有验证的缓存（“public，must-revalidate，proxy-revalidate”）','Specify browser cache policy for posts, pages, feeds and text-based files.'=>'为帖子，页面，订阅源和基于文本的文件指定浏览器缓存策略。','don\'t cache ("max-age=0, private, no-store, no-cache, must-revalidate")'=>'不缓存 ("最大-age< a0: 0, 专用, 无存储, 无缓存, 必须重新验证")','cache without proxy ("private, must-revalidate")'=>'无代理缓存 ("private, must-revalidate")','cache with max-age and validation ("max-age=EXPIRES_SECONDS, public, must-revalidate, proxy-revalidate")'=>'具有max-age和验证的缓存（“max-age = EXPIRES SECONDS，public，must-revalidate，proxy-revalidate”）','cache with max-age ("public, max-age=EXPIRES_SECONDS")'=>'使用max-age（“public，max-age = EXPIRES SECONDS”）的缓存','seconds'=>'秒','Specify browser cache policy for Cascading Style Sheets and JavaScript files.'=>'为层叠样式表和JavaScript文件指定浏览器缓存策略。','Never process 404 (not found) events for the specified %1$sURI%2$ss.'=>'从不处理指定 %1$sURI%2$s 的 404（未找到）事件。','If enabled - you may get 404 File Not Found response for some files generated on-the-fly by WordPress plugins. You may add those file %1$sURI%2$ss to 404 error exception list below to avoid that.'=>'如果启用，WordPress 插件即时生成的某些文件可能会收到 404 File Not Found（未找到文件）响应。您可以将这些文件 %1$sURI%2$ss 添加到下面的 404 错误异常列表中，以避免出现这种情况。','Reduce server load by allowing the web server to handle 404 (not found) errors for static files (images etc).'=>'通过允许Web服务器处理静态文件（图像等）的404（未找到）错误来减少服务器负载。','Removes Set-Cookie header for responses.'=>'删除响应的Set-Cookie标头。','Don\'t set cookies for static files'=>'不要为静态文件设置cookie','Do not add the prevent caching query string to the specified %1$sURI%2$ss. Supports regular expressions.'=>'不要将阻止缓存查询字符串添加到指定的%1$sURI%2$s。支持正则表达式。','Whenever settings are changed, a new query string will be generated and appended to objects allowing the new policy to be applied.'=>'每当更改设置时，将生成新的查询字符串并将其附加到允许应用新策略的对象。','Prevent caching of objects after settings change'=>'在设置更改后阻止对象的缓存','Reduce the download time for text-based files.'=>'减少基于文本的文件的下载时间。','Enable <acronym title="Hypertext Transfer Protocol">HTTP</acronym> (gzip) compression'=>'启用 <acronym title="Hypertext Transfer Protocol">HTTP</acronym> (gzip) 压缩','Set this header to assist in identifying optimized files.'=>'设置此标头以帮助识别优化文件。','Set W3 Total Cache header'=>'设置W3 Total Cache标头','Set the ETag header to encourage browser caching of files.'=>'设置ETag标头以鼓励浏览器缓存文件。','Set entity tag (eTag)'=>'设置实体标签（ETag）','Set pragma and cache-control headers to encourage browser caching of files.'=>'设置pragma和缓存控制标头以鼓励浏览器缓存文件。','Set cache control header'=>'设置缓存控制头','Set the expires header to encourage browser caching of files.'=>'设置expires标头以鼓励浏览器缓存文件。','Set expires header'=>'设置过期的请求头','Set the Last-Modified header to enable 304 Not Modified response.'=>'设置Last-Modified标头以启用304 Not Modified响应。','Set Last-Modified header'=>'设置最后被修改的请求头','Specify global browser cache policy.'=>'指定全局浏览器缓存策略。','General'=>'常规设置','Browser caching is currently %1$s.'=>'浏览器缓存目前是 %1$s。','Please reach out to all of these people and support their projects if you\'re so inclined.'=>'如果您愿意，请伸手帮助这些人和支持他们的项目。','It\'s quite difficult to recall all of the innovators that have shared their thoughts, code and experiences in the blogosphere over the years, but here are some names to get you started:'=>'很难回忆起多年来在博客圈分享他们的想法，代码和经验的所有创新者，但这里有一些名字可以帮助你入门：','Who do I thank for all of this?'=>'我要感谢谁呢？','Your users have less data to download, you can now serve more visitors at once without upgrading your hardware and you don\'t have to change how you do anything; just set it and forget it.'=>'您的用户有更少的数据下载。您现在可以同时服务更多的访问者，而无需升级您的硬件，并且您不必改变您的任何做事情的方式: 只需设置好了就不用管它了。','JavaScript embedding group and location management'=>'JavaScript嵌入组和位置管理','Minification of posts / pages and feeds'=>'文章/页面和Feed的缩小','Caching of feeds (site, categories, tags, comments, search results) in memory or on disk'=>'在内存或磁盘上缓存源（站点，类别，标签，注释，搜索结果）','Caching of objects in memory or on disk'=>'在内存或磁盘上缓存对象','Caching of database objects in memory or on disk'=>'在内存或磁盘上缓存数据库对象','Caching of pages / posts in memory or on disk or on %1$sCDN%2$s (mirror only)'=>'在内存、磁盘或%1$sCDN%2$s（仅限镜像）中缓存页面/帖子','Increased web server concurrency and increased scale (easily sustain high traffic spikes)'=>'增加Web服务器并发性并扩大规模（轻松维持高流量峰值）','Optimized progressive render (pages start rendering immediately)'=>'优化的渐进式渲染（页面立即开始渲染）','Increased visitor time on site'=>'增加访客网站停留时间','Improved Google search engine ranking'=>'改进Google搜索引擎排名','User experience is an important aspect of every web site and all web sites can benefit from effective caching and file size reduction. We have applied web site optimization methods typically used with high traffic sites and simplified their implementation. Coupling these methods either %1$s and/or opcode caching and the %2$sCDN%3$s of your choosing to provide the following features and benefits:'=>'用户体验是每个网站的一个重要方面，所有网站都可以从有效的缓存和文件大小减小中受益。我们应用了通常用于高流量网站的网站优化方法，并简化了其实施。将这些方法（%1$s和/或操作码缓存）与您选择的%2$sCDN%3$s相结合，以提供以下功能和优势：','Yes, sign me up.'=>'是的, 我要登录.','Login & Rate Us'=>'登陆 & 评价','Tell Your Friends'=>'告诉你的朋友们','Network mode:'=>'网络模式：','Enabled'=>'已启用','URL rewrite:'=>'URL重写:','Not defined'=>'未定义','WP_CACHE define:'=>'WP_CACHE 定义:','Disabled'=>'已禁用','Fancy permalinks:'=>'花式永久链接：','Error:'=>'错误：','Write-able'=>'可写的','Not write-able'=>'不可写','OK'=>'确定','WordPress Resources'=>'WordPress资源','Additional Server Modules'=>'其他服务器模块','(required for disk enhanced Page Cache and Browser Cache)'=>'(需要磁盘增强页面缓存和浏览器缓存)','Not detected'=>'没有检测到','Not available'=>'不可用','Available'=>'可用','set_time_limit:'=>'set_time_limit:','zlib output compression:'=>'zlib 输出压缩:','On:'=>'打开：','Open basedir:'=>'打开 basedir:','Off'=>'关闭','On'=>'开启','Installed (mhash)'=>'安装（mhash）','Installed (hash)'=>'已安装 (hash)','Hash function:'=>'Hash函数：','Installed (mime_content_type)'=>'已安装 (mime_content_type)','Installed (Fileinfo)'=>'已安装（Fileinfo）','Mime type detection:'=>'MIME类型检测:','(required for HTML Tidy minifier support)'=>'（所需的HTML精简minifier支持）','HTML Tidy extension:'=>'HTML Tidy扩展:','Memcache extension:'=>'Memcache扩展:','PHP6'=>'PHP6','Installed (XCache)'=>'已安装 (XCache)','Installed (eAccelerator)'=>'已安装 (eAccelerator)','Installed (APC)'=>'已安装 (APC)','(required for brotli compression support)'=>'(需要 brotli 压缩支持)','(required for Amazon S3, Amazon CloudFront, Rackspace CloudFiles support)'=>'(需要 Amazon S3, Amazon CloudFront, Rackspace CloudFiles 支持)','cURL extension:'=>'cURL扩展：','(required for Rackspace Cloud Files support)'=>'(需要 Rackspace CloudFiles 支持)','Not installed'=>'未安装','Installed'=>'已安装','Multibyte String support:'=>'多字节字符串支持:','PHP Version:'=>'PHP版本：','Plugin Version:'=>'插件版本:','Server Modules &amp; Resources:'=>'服务器模块 &amp; 资源:','Legend'=>'图例','Compatibility Test'=>'兼容性测试','Typically minification of advertiser code, analytics/statistics or any other types of tracking code is not recommended.'=>'通常不推荐缩小广告客户代码，分析/统计信息或任何其他类型的跟踪代码。','Notes'=>'备注','Apply &amp; close'=>'应用 &amp; 关闭','Cascading Style Sheets:'=>'层叠样式表：','No files found.'=>'找不到文件.','Check / Uncheck All'=>'勾选 / 取消勾选所有','Verify URI'=>'验证URI','Embed before &lt;/body&gt;'=>'&lt;/body&gt; 之前嵌入;','Embed after &lt;body&gt;'=>'&lt;body&gt; 之后嵌入;','Embed in &lt;head&gt;'=>'嵌入在 &lt;head&gt;','Embed Location:'=>'嵌入位置:','Template:'=>'模板:','File URI:'=>'文件URI：','Add:'=>'添加:','theme. Select "add" the files you wish to minify, then click "apply &amp; close" to save the settings.'=>'主题。 选择“添加”要缩小的文件，然后单击“应用并关闭”保存设置。','To get started with minify, we\'ve identified the following external CSS and JS objects in the'=>'要开始使用minify，我们在中确定了以下外部CSS和JS对象','Name:'=>'名称：','Close'=>'关闭','Submit'=>'提交','Configure'=>'配置','View all'=>'查看全部','All Templates'=>'所有模板','Edit file <strong>%s</strong> and remove all lines between and including <strong>%s</strong>
				and <strong>%s</strong> markers.'=>'编辑文件<strong>%s</strong>并删除<strong>%s之间（包括）的所有行</strong>
				和<strong>%s</strong>标记。','Hide this message'=>'隐藏此消息','Activating the <a href="%s">Yoast SEO</a> extension for W3 Total Cache may be helpful for your site. <a class="button" href="%s">Click here</a> to try it. %s'=>'激活 W3 Total Cache 的 <a href="%s">Yoast SEO</a> 扩展可能对您的网站有帮助。<a class="button" href="%s">单击此处</a>试用。%s','Take a minute to update, here\'s why:'=>'花点时间更新, 原因如下:','Cancel'=>'取消','Update via FTP'=>'通过FTP更新','View required changes'=>'查看所需的更改','Execute next commands in a shell:'=>'在shell中执行下一个命令：','Technical info'=>'技术信息','FTP credentials don\'t allow to delete '=>'FTP 凭证不允许删除 ','FTP credentials don\'t allow to chmod '=>'FTP 凭证不允许 chmod ','FTP credentials don\'t allow to delete folder '=>'FTP凭证不允许删除的文件夹 ','<strong>%s</strong> could not be created, please run following command:<br />%s'=>'<strong>%s</strong> 无法创建，请运行以下命令:<br />%s','<li><strong style="color: #f00;">chmod 777 %s</strong></li>'=>'<li><strong style="color: #f00;">chmod 777 %s</strong></li>','<strong>%s</strong> could not be created, <strong>open_basedir</strong> restriction in effect, please check your php.ini settings:<br /><strong style="color: #f00;">open_basedir = "%s"</strong>'=>'<strong>%s</strong> 无法创建 <strong>，open_basedir限制</strong> ，请检查您的 php .ini设置：<br /><strong style="color: #f00;">open_basedir %s "</strong>','%s<br />then %s.'=>'%s<br />然后 %s.','Preview'=>'预览','Configures W3 Total Cache to comply with Yoast SEO requirements automatically.'=>'配置W3 Total Cache，自动遵守Yoast搜索引擎优化的要求。','Select user roles that should not use the fragment cache.'=>'选择不应使用片段缓存的用户角色。','Select roles:'=>'选择角色：','Select hooks from the list that should not be cached if user belongs to any of the roles selected below.'=>'从列表中选择不应缓存的钩子，如果用户属于下面选择的任何角色。','Select hooks'=>'选择钩子','Disable fragment cache:'=>'禁用碎片缓存:','Caches wp_footer loop.'=>'缓存wp_footer循环。','Caches footer loop.'=>'缓存页脚循环.','Cache footer:'=>'缓存页脚:','List of pages that should not have sidebar cached. Specify one page / post per line. This area supports regular expressions.'=>'不应包含边栏缓存的页面列表。 每行指定一个页面/帖子。 此区域支持正则表达式。','Exclude pages:'=>'排除页面:','Caches sidebar loop, the widget area.'=>'缓存边栏循环，小部件区域。','Cache sidebar:'=>'缓存侧边栏:','Caches the ping loop, pagination is supported. One per line.'=>'缓存ping循环，支持分页。 每行一个。','Cache pings:'=>'缓存ping：','Caches the comments loop, pagination is supported.'=>'缓存注释循环，支持分页。','Cache comments:'=>'缓存评论：','List of pages / posts that should not have the single post / post loop cached. Specify one page / post per line. This area supports regular expressions.'=>'不应该有单个post / post循环缓存的页面/帖子列表。 每行指定一个页面/帖子。 此区域支持正则表达式。','Excluded single pages / posts:'=>'排除单个页面/帖子：','Caches the single post / page loop, pagination is supported.'=>'缓存单个post / page循环，支持分页。','Cache single post / page:'=>'缓存单个帖子/页面：','Flushes the posts loop cache on post updates. See setting above for affected loops.'=>'在帖子更新时刷新帖子循环缓存。 有关受影响的环路，请参阅上述设置。','Flush posts loop:'=>'冲洗柱循环：','Caches the posts listed on tag, categories, author and other term pages, pagination is supported.'=>'缓存标签, 分类, 作者和其他术语页面上列出的文章, 支持分页.','Cache author/tag/categories/term post loop:'=>'缓存作者/标签/分类/术语文章循环:','Caches the front page post loop, pagination is supported.'=>'缓存前端页面文章循环, 支持分页.','Cache front page post loop:'=>'缓存前页后循环：','Caches secondary navigation filter; per page.'=>'缓存辅助导航过滤器; 每个页面.','Cache secondary navigation:'=>'缓存二级导航:','Caches the navigation filter; per page.'=>'缓存导航过滤器; 每个页面.','Cache primary navigation:'=>'缓存主导航：','Cache header loop. This is the area where the logo is located.'=>'缓存头循环。这是logo标识所在的区域。','Cache header:'=>'缓存标头:','Cache wp_head. This includes the embedded CSS, JS etc.'=>'缓存wp_head。这包括嵌入式CSS、JS等。','Cache wp_head loop:'=>'缓存 wp_head 循环:','disabled'=>'禁用','enabled'=>'启用','%1$sCloudFlare plugin detected. We recommend removing the plugin as it offers no additional capabilities when W3 Total Cache is installed. This message will disappear when CloudFlare is removed. %2$s%3$s'=>'%1$s检测到CloudFlare插件。我们建议删除该插件，因为它在安装 W3 Total Cache 时不提供其他功能。当CloudFlare被删除时，此消息将消失。%2$s%3$s','Empty All Caches Except CloudFlare'=>'清空除CloudFlare之外的所有缓存','Settings'=>'设置','Account'=>'账户','Analytics'=>'统计','My Websites'=>'我的网站','CloudFlare'=>'CloudFlare','Save all settings'=>'保存所有设置','Development mode:'=>'开发模式：','Minification'=>'缩小','Rocket Loader:'=>'火箭装载机：','Security level:'=>'安全级别：','Enable'=>'启用','Network Performance &amp; Security powered by CloudFlare'=>'网络性能,AMP,和安全由 CloudFlare 提供','Domain '=>'域名 ']];