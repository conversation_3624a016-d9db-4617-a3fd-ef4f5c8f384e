/* =========================================================================
 *   Admin bar styling
 * ========================================================================= */

#wpadminbar #wp-admin-bar-debug-bar {
	float: right;
}

#wpadminbar #wp-admin-bar-debug-bar.active {
	background: #555;
	background: -moz-linear-gradient(bottom,  #555,  #3e3e3e);
	background: -webkit-gradient(linear, left bottom, left top, from(#555), to(#3e3e3e));
}

#wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-notice-summary,
#wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-notice-summary .ab-item:focus {
	background-color: #ff8922;
	background-image: -moz-linear-gradient(bottom, #ee6f00, #ff8922);
	background-image: -webkit-gradient(linear, left bottom, left top, from(#ee6f00), to(#ff8922));
}

#wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-notice-summary .ab-item:hover,
.debug-bar-visible #wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-notice-summary,
.debug-bar-visible #wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-notice-summary .ab-item:focus {
	background-color: #ee6f00;
	background-image: -moz-linear-gradient(bottom, #ff8922, #ee6f00 );
	background-image: -webkit-gradient(linear, left bottom, left top, from(#ff8922), to(#ee6f00));
}
#wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-notice-summary .ab-item {
	color: #fff;
	text-shadow: 0 -1px 0 #884000;
}

#wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-warning-summary,
#wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-warning-summary .ab-item:focus {
	background-color: #f44;
	background-image: -moz-linear-gradient(bottom, #d00, #f44);
	background-image: -webkit-gradient(linear, left bottom, left top, from(#d00), to(#f44));
}

#wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-warning-summary .ab-item:hover,
.debug-bar-visible #wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-warning-summary,
.debug-bar-visible #wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-warning-summary .ab-item:focus {
	background-color: #d00;
	background-image: -moz-linear-gradient(bottom, #f44, #d00 );
	background-image: -webkit-gradient(linear, left bottom, left top, from(#f44), to(#d00));
}
#wpadminbar #wp-admin-bar-debug-bar.debug-bar-php-warning-summary .ab-item {
	color: #fff;
	text-shadow: 0 -1px 0 #700;
}

#wpadminbar #wp-admin-bar-debug-bar ul {
	right: 0;
}

/**
 * Debug bar styling
 */

#querylist {
	direction: ltr;
	display: none;
	position: fixed;
	height: 33%;
	min-height: 350px;
	font-family: "Helvetica Neue", sans-serif;
	left: 0;
	right: 0;
	bottom: 0;
	background: #f1f1f1;
	z-index: 99000;
	color: #000;
	line-height: 150% !important;
	text-align: left;
	font-size: 12px;
}

/* reset debug bar links */
#querylist a {
	border: 0;
}

.debug-bar-visible #querylist {
	display: block;
}

.debug-bar-maximized #querylist {
	position: fixed;
	top: 28px;
	height: auto;
}

body.debug-bar-maximized.debug-bar-visible {
	overflow: hidden;
}

#debug-bar-info {
	height: 60px;
	width: 100%;
	position: absolute;
	z-index: 200;

	background: #f1f1f1;
	border-bottom: 1px solid #fff;
	box-shadow: 0 0 6px rgba( 0, 0, 0, 0.4 );

	background-color: #f1f1f1; /* Fallback */
	background-image: -ms-linear-gradient(top, #f9f9f9, #dfdfdf); /* IE10 */
	background-image: -moz-linear-gradient(top, #f9f9f9, #dfdfdf); /* Firefox */
	background-image: -o-linear-gradient(top, #f9f9f9, #dfdfdf); /* Opera */
	background-image: -webkit-gradient(linear, left top, left bottom, from(#f9f9f9), to(#dfdfdf)); /* old Webkit */
	background-image: -webkit-linear-gradient(top, #f9f9f9, #dfdfdf); /* new Webkit */
	background-image: linear-gradient(top, #f9f9f9, #dfdfdf); /* proposed W3C Markup */
}

#debug-bar-menu {
	position: absolute;
	top: 61px;
	left: 0;
	width: 250px;
	bottom: 0;
	z-index: 100;

	overflow: auto;
	text-shadow: 0 1px 1px rgba( 0, 0, 0, 0.9 );
	background: #333;
	-webkit-box-shadow: inset -3px 0 6px rgba( 0, 0, 0, 0.4 );
	-moz-box-shadow:    inset -3px 0 6px rgba( 0, 0, 0, 0.4 );
	box-shadow:         inset -3px 0 6px rgba( 0, 0, 0, 0.4 );
}

#debug-menu-targets {
	overflow: auto;
	position: absolute;
	top: 61px;
	left: 250px;
	right: 0;
	bottom: 0;
	z-index: 150;
}

#debug-status {
	float: left;
	padding-left: 20px;
	color: #bbb;
	font-size: 14px;
	line-height: 60px;
}

.debug-status {
	float: left;
	margin-right: 40px;
}

#debug-status .debug-status-title {
	color: #555;
	font-weight: 500;
	line-height: 18px;
	font-size: 12px;
	margin-top: 10px;
}
#debug-status .debug-status-data {
	color: #999;
	font-weight: 200;
	line-height: 20px;
	font-size: 18px;
}
#debug-status #debug-status-warning {
	font-weight: bold;
	color: red;
}
#debug-status #debug-status-site {
	font-weight: bold;
}

#querylist pre {
	text-align: left;
	font-size: 12px;
	padding: 10px;
}

#querylist .left {
	float: left;
}

#querylist .right {
	float: right;
}

#querylist h1, #querylist h2, #querylist h3 {
	font-weight: normal;
}

#debug-menu-links {
	overflow: hidden;
	list-style: none;
	margin: 0;
	padding: 0;
}

#debug-menu-links li {
	float: left;
	margin-bottom: 0 !important;
}

#debug-menu-links li a {
	box-sizing: content-box;
	padding: 0 20px;
	width: 210px;
	line-height: 32px;
	outline: none;
	display: block;
	margin: 0;
	color: #fff !important;
	text-decoration: none !important;
	font-weight: 500 !important;
	font-size: 14px;
}

#debug-menu-links li a.current {
	background: rgba( 0, 0, 0, 0.2 );
}

#querylist h2 {
	float: left;
	min-width: 150px;
	border: 1px solid #eee;
	padding: 5px 10px 15px;
	clear: none;
	text-align: center;
	color: #000;
	font-family: georgia, times, serif;
	font-size: 28px;
	margin: 15px 10px 15px 0 !important;
}

#querylist h2 span {
	font-size: 12px;
	color: #888;
	text-transform: uppercase;
	white-space: nowrap;
	display: block;
	margin-bottom: 5px;
}

#querylist h2 span#debug-bar-js-error-count {
	font-size: inherit;
	color: inherit;
	text-transform: inherit;
	white-space: inherit;
	display: inherit;
	margin-bottom: inherit;
}

/* =========================================================================
 *   Object cache styling
 * ========================================================================= */
#object-cache-stats h2 {
	border: none;
	float: left;
	text-align: left;
	font-size: 22px;
	margin-bottom: 0;
}


#object-cache-stats ul.debug-menu-links,
#object-cache-stats-menu-targets {
       clear: both;
}

.object-cache-stats-menu-target pre {
       white-space: pre-wrap;
       margin: 1rem 0;
}

#object-cache-stats ul.debug-menu-links li {
       float: left;
       margin: 0 10px 10px 0;
       background: none;
       border: 1px solid #eee;
       color: #4b5259;
       list-style: none;
}

#object-cache-stats ul.debug-menu-links li:hover {
       border-color: #ddd;
}

#object-cache-stats ul.debug-menu-links li a {
       background: none;
       color: #4b5259;
       overflow: hidden;
       display: block;
       padding: 5px 9px;
       text-decoration: none;
}

#querylist h3 {
	margin-bottom: 15px;
}

#querylist ol.wpd-queries {
	padding: 0 !important;
	margin: 0 !important;
	list-style: none;
	clear: left;
}

#querylist ol.wpd-queries li {
	padding: 10px;
	background: #f0f0f0;
	margin: 0 0 10px 0;
}

#querylist ol.wpd-queries li div.qdebug {
	background: #e8e8e8;
	margin: 10px -10px -10px -10px;
	padding: 5px 150px 5px 5px;
	font-size: 11px;
	position: relative;
	min-height: 20px;
}

#querylist ol.wpd-queries li div.qdebug span {
	position: absolute;
	right: 10px;
	top: 5px;
	white-space: nowrap;
}

#querylist a {
	text-decoration: underline !important;
	color: blue !important;
}

#querylist a:hover {
	text-decoration: none !important;
}

#querylist .debug-menu-target {
	margin: 20px;
	display: none;
}

#querylist ol {
	font: 12px Monaco, "Courier New", Courier, Fixed !important;
	line-height: 180% !important;
}

#querylist table {
	table-layout: auto;
}

#querylist td, #querylist th {
	text-transform: none;
}

#querylist table,
#querylist table th,
#querylist table td {
	border: 0px none;
	vertical-align: top;
}

#debug-bar-php ol.debug-bar-php-list {
	padding: 0 !important;
	margin: 0 !important;
	list-style: none;
	clear: left;
}

#debug-bar-php ol.debug-bar-php-list li {
	padding: 10px;
	margin: 0 0 10px 0;
}

#debug-bar-php .debug-bar-php-warning {
	background-color: #ffebe8;
	border: 1px solid #c00;
}

#debug-bar-php .debug-bar-php-notice {
	background-color: #ffffe0;
	border: 1px solid #e6db55;
}

#debug-bar-deprecated ol.debug-bar-deprecated-list {
	padding: 0 !important;
	margin: 0 !important;
	list-style: none;
	clear: left;
}

#debug-bar-deprecated ol.debug-bar-deprecated-list li {
	padding: 10px;
	margin: 0 0 10px 0;
	background: #f0f0f0;
}

#debug-bar-wp-query table.debug-bar-wp-query-list {
	width: 100%;
	border-collapse: collapse;
	cell-padding: 1em;
	clear: both;
}

#debug-bar-wp-query table.debug-bar-wp-query-list td {
	padding: 0.5em 5px;
	border-bottom: 1px solid #ccc;
}

#debug-bar-wp-query table.debug-bar-wp-query-list th {
	padding: 0.5em 5px;
	font-weight: bold;
	border-bottom: 1px solid #ccc;
}

#debug-bar-wp-query table.debug-bar-wp-query-list tr:nth-child(2n+1) {
	background-color: #E8E8E8;
}

#debug-bar-wp-query table.debug-bar-wp-query-list tr td:first-of-type {
	font-size: 140%;
	padding: 0.3em 0.5em;
}

#debug-bar-wp-query h3, #debug-bar-request h3 {
	float: none;
	clear: both;
	font-family: georgia, times, serif;
	font-size: 22px;
	margin: 15px 10px 15px 0 !important;
}

#debug-bar-request p {
	padding: 10px;
	margin: 0 0 10px 0;
	background: #f0f0f0;
}

/* =========================================================================
 *   Actions: Maximize / restore
 * ========================================================================= */

#debug-bar-actions {
	position: absolute;
	top: 21px;
	right: 20px;
	z-index: 300;
}
#debug-bar-actions span {
	-moz-border-radius: 20px;
	-webkit-border-radius: 20px;
	border-radius: 20px;
	display: block;
	float: left;
	height: 19px;
	width: 19px;
	margin-left: 5px;

	font-size: 18px;
	line-height: 14px;
	text-align: center;
	font-weight: bold;

	color: #eee;
	background: #aaa;

	cursor: pointer;
}
#debug-bar-actions span:hover {
	background: #888;
}

#debug-bar-actions span.restore {
	line-height: 15px;
}

.debug-bar-maximized #debug-bar-actions span.restore {
	display: block;
}
.debug-bar-maximized #debug-bar-actions span.maximize {
	display: none;
}

.debug-bar-partial #debug-bar-actions span.restore {
	display: none;
}
.debug-bar-partial #debug-bar-actions span.maximize {
	display: block;
}

/* =========================================================================
 *   JS Error item styling
 * ========================================================================= */

#debug-menu-links li a#debug-menu-link-Debug_Bar_JS,
#wp-admin-bar-debug-bar-Debug_Bar_JS {
	display: none;
}

#debug-bar-js ol.debug-bar-js-list {
	padding: 0 !important;
	margin: 0 !important;
	list-style: none;
	clear: left;
}

#debug-bar-js ol.debug-bar-js-list li {
	padding: 10px;
	margin: 0 0 10px 0;
}

#debug-bar-js .debug-bar-js-error {
	background-color: #ffebe8;
	border: 1px solid #c00;
}

#debug-bar-js .debug-bar-js-error span {
	color: #666;
	font-size: 0.8em;
	display: block;
}

#debug-bar-js .debug-bar-js-error:hover span {
	color: #000;
}
