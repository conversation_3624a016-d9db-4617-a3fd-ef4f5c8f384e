<?php

/**
 * The plugin bootstrap file
 *
 * This file is read by WordPress to generate the plugin information in the plugin
 * Dashboard. This file also includes all of the dependencies used by the plugin,
 * registers the activation and deactivation functions, and defines a function
 * this starts the plugin.
 *
 * @link              http://example.com
 * @since             1.0.0
 * @package           Hcylsoft
 *
 * @wordpress-plugin
 * Plugin Name:       华创云联官网插件（勿删）
 * Plugin URI:        https://laofucode.com/
 * Description:       网站基本配置
 * Version:           1.0.0
 * Author:            张晓红
 * Author URI:        https://laofucode.com/
 * License:           GPL-2.0+
 * License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain:       hcylsoft
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}



/**
 * The code that runs during plugin activation.
 */
require_once plugin_dir_path( __FILE__ ) . 'includes/class-hcylsoft-activator.php';

/**
 * The code that runs during plugin deactivation.
 */
require_once plugin_dir_path( __FILE__ ) . 'includes/class-hcylsoft-deactivator.php';


add_filter( 'cron_schedules', array('Hcylsoft_Activator','my_custom_cron_schedules')  );

add_action( 'my_plugin_cron_job', array('Hcylsoft_Activator','my_plugin_cron_function'));

/** This action is documented in includes/class-hcylsoft-activator.php */
register_activation_hook( __FILE__, array( 'Hcylsoft_Activator', 'activate' ) );

/** This action is documented in includes/class-hcylsoft-deactivator.php */
register_deactivation_hook( __FILE__, array( 'Hcylsoft_Deactivator', 'deactivate' ) );

/**
 * The core plugin class that is used to define internationalization,
 * dashboard-specific hooks, and public-facing site hooks.
 */
require_once plugin_dir_path( __FILE__ ) . 'includes/class-hcylsoft.php';





/**
 * Begins execution of the plugin.
 *
 * Since everything within the plugin is registered via hooks,
 * then kicking off the plugin from this point in the file does
 * not affect the page life cycle.
 *
 * @since    1.0.0
 */
function run_Hcylsoft() {

	$plugin = new Hcylsoft();
	$plugin->run();

}
run_Hcylsoft();
