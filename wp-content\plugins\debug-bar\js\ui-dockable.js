(function(a){a.widget("db.dockable",a.ui.mouse,{options:{handle:false,axis:"y",resize:function(){},resized:function(){}},_create:function(){if(this.options.axis=="x"){this.page="pageX";this.dimension="width"}else{this.page="pageY";this.dimension="height"}if(!this.options.handle){return}this.handle=a(this.options.handle);this._mouseInit()},_handoff:function(){return{element:this.element,handle:this.handle,axis:this.options.axis}},_mouseStart:function(b){this._trigger("start",b,this._handoff());this.d0=this.element[this.dimension]()+b[this.page]},_mouseDrag:function(c){var b=this._trigger("resize",c,this._handoff());if(b===false){return}this.element[this.dimension](this.d0-c[this.page]);this._trigger("resized",c,this._handoff())},_mouseCapture:function(b){return !this.options.disabled&&b.target==this.handle[0]},_mouseStop:function(b){this._trigger("stop",b,this._handoff())}})})(jQuery);