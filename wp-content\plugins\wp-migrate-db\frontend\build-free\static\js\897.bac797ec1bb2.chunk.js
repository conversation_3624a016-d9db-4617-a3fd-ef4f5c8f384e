"use strict";(self.webpackJSONPwpmdb=self.webpackJSONPwpmdb||[]).push([[897],{80897:function(e,t,a){a.r(t),a.d(t,{default:function(){return f}});var r,n=a(4665),l=a(42233),i=a(75338),m=(a(9030),a.p+"static/media/check-circular.02c190e9.svg"),c=a(29942);function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},o.apply(this,arguments)}var s=function(e){return n.createElement("svg",o({xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 16 16"},e),r||(r=n.createElement("path",{fill:"#A5DDF1",d:"m8.58 1.376 1.663 3.528c.094.199.276.337.486.369l3.719.566a.654.654 0 0 1 .52.46.697.697 0 0 1-.163.691l-2.692 2.746a.696.696 0 0 0-.185.598l.635 3.877a.69.69 0 0 1-.257.66.621.621 0 0 1-.679.051L8.3 13.092a.62.62 0 0 0-.6 0l-3.327 1.83c-.217.12-.48.1-.68-.05a.69.69 0 0 1-.256-.661l.635-3.877a.694.694 0 0 0-.185-.598L1.195 6.99a.697.697 0 0 1-.163-.692.654.654 0 0 1 .52-.46l3.72-.565a.648.648 0 0 0 .485-.37L7.42 1.377a.634.634 0 0 1 1.16 0Z"})))},u=(a.p,a.p+"static/media/testimonial-avatar.309cd834.png"),d=function(e){var t=e.videoID,a=e.height;return n.createElement("iframe",{height:a||360,width:"100%",title:(0,l.__)("WP Migrate Help Videos","wp-migrate-db"),src:"//fast.wistia.net/embed/iframe/".concat(t,"?embedType=iframe&videoFoam=true&fullscreenButton=true&qualityMin=1080"),frameBorder:"0",scrolling:"no",className:"wistia_embed",name:"wistia_embed",allowFullScreen:!0})},p=(0,c.Nh)(u),g=(0,c.Nh)(m),h={backgroundImage:"url(".concat(g,")")},E=function(e){return n.createElement("li",{style:h},e.children)},f=function(e){return n.createElement("div",{className:"upgrade-to-pro"},n.createElement("div",{className:"col-left"},n.createElement("h3",null,(0,i.ZP)((0,l.__)("Upgrade for <span>PRO</span> Features","wp-migrate-db"))),n.createElement("ul",null,n.createElement(E,null,(0,l.__)("Push and pull your database","wp-migrate-db")),n.createElement(E,null,(0,l.__)("Sync the media libraries of two sites","wp-migrate-db")),n.createElement(E,null,(0,l.__)("Run push/pull migrations from the command line","wp-migrate-db")),n.createElement(E,null,(0,l.__)("Migrate from multisite to single site and back again","wp-migrate-db")),n.createElement(E,null,(0,l.__)("Priority email support","wp-migrate-db"))),n.createElement("a",{href:"https://deliciousbrains.com/wp-migrate-db-pro/upgrade/",target:"_blank",rel:"noopener noreferrer",className:"btn"},(0,l.__)("Find out more","wp-migrate-db"))),n.createElement("div",{className:"col-right"},n.createElement("div",{style:{marginBottom:5}},n.createElement(d,{videoID:"5co63n4jqq",height:270})),n.createElement("div",{className:"testimonial"},n.createElement("figure",null,n.createElement("a",{href:"https://twitter.com/mor10/status/568514947241488384",target:"_blank",rel:"noopener noreferrer"},n.createElement("img",{src:p,alt:"Testimonial Avatar"}))),n.createElement("div",null,n.createElement("div",{className:"testimonial-header"},n.createElement("h4",null,"Morten Rand-Hendriksen"),n.createElement("span",null,n.createElement("a",{href:"https://twitter.com/mor10/",target:"_blank",rel:"noopener noreferrer"},"@mor10")),n.createElement("b",null,n.createElement(s,null),n.createElement(s,null),n.createElement(s,null),n.createElement(s,null),n.createElement(s,null))),n.createElement("p",null,"\"Even though I've been using it for a long time the push/pull functionality in"," ",n.createElement("strong",null,"@dliciousbrains [WP\xa0Migrate]"),' continues to impress me."')))))}}}]);