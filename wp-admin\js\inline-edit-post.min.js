/*! This file is auto-generated */
window.wp=window.wp||{},function(u,h){window.inlineEditPost={init:function(){var i=this,t=u("#inline-edit"),e=u("#bulk-edit");i.type=u("table.widefat").hasClass("pages")?"page":"post",i.what="#post-",t.on("keyup",function(t){if(27===t.which)return inlineEditPost.revert()}),e.on("keyup",function(t){if(27===t.which)return inlineEditPost.revert()}),u(".cancel",t).on("click",function(){return inlineEditPost.revert()}),u(".save",t).on("click",function(){return inlineEditPost.save(this)}),u("td",t).on("keydown",function(t){if(13===t.which&&!u(t.target).hasClass("cancel"))return inlineEditPost.save(this)}),u(".cancel",e).on("click",function(){return inlineEditPost.revert()}),u('#inline-edit .inline-edit-private input[value="private"]').on("click",function(){var t=u("input.inline-edit-password-input");u(this).prop("checked")?t.val("").prop("disabled",!0):t.prop("disabled",!1)}),u("#the-list").on("click",".editinline",function(){u(this).attr("aria-expanded","true"),inlineEditPost.edit(this)}),u("#bulk-edit").find("fieldset:first").after(u("#inline-edit fieldset.inline-edit-categories").clone()).siblings("fieldset:last").prepend(u("#inline-edit .inline-edit-tags-wrap").clone()),u('select[name="_status"] option[value="future"]',e).remove(),u("#doaction").on("click",function(t){var e;i.whichBulkButtonId=u(this).attr("id"),e=i.whichBulkButtonId.substr(2),"edit"===u('select[name="'+e+'"]').val()?(t.preventDefault(),i.setBulk()):0<u("form#posts-filter tr.inline-editor").length&&i.revert()})},toggle:function(t){var e=this;"none"===u(e.what+e.getId(t)).css("display")?e.revert():e.edit(t)},setBulk:function(){var n="",t=this.type,a=!0;if(this.revert(),u("#bulk-edit td").attr("colspan",u("th:visible, td:visible",".widefat:first thead").length),u("table.widefat tbody").prepend(u("#bulk-edit")).prepend('<tr class="hidden"></tr>'),u("#bulk-edit").addClass("inline-editor").show(),u('tbody th.check-column input[type="checkbox"]').each(function(){var t,e,i;u(this).prop("checked")&&(a=!1,t=u(this).val(),e=u("#inline_"+t+" .post_title").html()||h.i18n.__("(no title)"),i=h.i18n.sprintf(h.i18n.__("Remove &#8220;%s&#8221; from Bulk Edit"),e),n+='<li class="ntdelitem"><button type="button" id="_'+t+'" class="button-link ntdelbutton"><span class="screen-reader-text">'+i+'</span></button><span class="ntdeltitle" aria-hidden="true">'+e+"</span></li>")}),a)return this.revert();u("#bulk-titles").html('<ul id="bulk-titles-list" role="list">'+n+"</ul>"),u("#bulk-titles .ntdelbutton").click(function(){var t=u(this),e=t.attr("id").substr(1),i=t.parent().prev().children(".ntdelbutton"),t=t.parent().next().children(".ntdelbutton");u('table.widefat input[value="'+e+'"]').prop("checked",!1),u("#_"+e).parent().remove(),h.a11y.speak(h.i18n.__("Item removed."),"assertive"),t.length?t.focus():i.length?i.focus():(u("#bulk-titles-list").remove(),inlineEditPost.revert(),h.a11y.speak(h.i18n.__("All selected items have been removed. Select new items to use Bulk Actions.")))}),"post"===t&&u("tr.inline-editor textarea[data-wp-taxonomy]").each(function(t,e){u(e).autocomplete("instance")||u(e).wpTagsSuggest()}),u("#bulk-edit .inline-edit-wrapper").attr("tabindex","-1").focus(),u("html, body").animate({scrollTop:0},"fast")},edit:function(n){var t,a,e,i,s,r,l,o,d=this,p=!0;for(d.revert(),"object"==typeof n&&(n=d.getId(n)),t=["post_title","post_name","post_author","_status","jj","mm","aa","hh","mn","ss","post_password","post_format","menu_order","page_template"],"page"===d.type&&t.push("post_parent"),a=u("#inline-edit").clone(!0),u("td",a).attr("colspan",u("th:visible, td:visible",".widefat:first thead").length),u("td",a).find("#quick-edit-legend").removeAttr("id"),u("td",a).find('p[id^="quick-edit-"]').removeAttr("id"),u(d.what+n).removeClass("is-expanded").hide().after(a).after('<tr class="hidden"></tr>'),e=u("#inline_"+n),u(':input[name="post_author"] option[value="'+u(".post_author",e).text()+'"]',a).val()||u(':input[name="post_author"]',a).prepend('<option value="'+u(".post_author",e).text()+'">'+u("#post-"+n+" .author").text()+"</option>"),1===u(':input[name="post_author"] option',a).length&&u("label.inline-edit-author",a).hide(),l=0;l<t.length;l++)(o=u("."+t[l],e)).find("img").replaceWith(function(){return this.alt}),o=o.text(),u(':input[name="'+t[l]+'"]',a).val(o);"open"===u(".comment_status",e).text()&&u('input[name="comment_status"]',a).prop("checked",!0),"open"===u(".ping_status",e).text()&&u('input[name="ping_status"]',a).prop("checked",!0),"sticky"===u(".sticky",e).text()&&u('input[name="sticky"]',a).prop("checked",!0),u(".post_category",e).each(function(){var t,e=u(this).text();e&&(t=u(this).attr("id").replace("_"+n,""),u("ul."+t+"-checklist :checkbox",a).val(e.split(",")))}),u(".tags_input",e).each(function(){var t=u(this),e=u(this).attr("id").replace("_"+n,""),e=u("textarea.tax_input_"+e,a),i=h.i18n._x(",","tag delimiter").trim();e.length&&(t.find("img").replaceWith(function(){return this.alt}),(t=t.text())&&(","!==i&&(t=t.replace(/,/g,i)),e.val(t)),e.wpTagsSuggest())});var c,d=u(':input[name="aa"]').val()+"-"+u(':input[name="mm"]').val()+"-"+u(':input[name="jj"]').val(),d=(d+=" "+u(':input[name="hh"]').val()+":"+u(':input[name="mn"]').val()+":"+u(':input[name="ss"]').val(),new Date(d));if(("future"!==(c=u("._status",e).text())&&Date.now()>d?u('select[name="_status"] option[value="future"]',a):u('select[name="_status"] option[value="publish"]',a)).remove(),d=u(".inline-edit-password-input").prop("disabled",!1),"private"===c&&(u('input[name="keep_private"]',a).prop("checked",!0),d.val("").prop("disabled",!0)),0<(i=u('select[name="post_parent"] option[value="'+n+'"]',a)).length){for(s=i[0].className.split("-")[1],r=i;p&&0!==(r=r.next("option")).length;)r[0].className.split("-")[1]<=s?p=!1:(r.remove(),r=i);i.remove()}return u(a).attr("id","edit-"+n).addClass("inline-editor").show(),u(".ptitle",a).trigger("focus"),!1},save:function(n){var t=u(".post_status_page").val()||"";return"object"==typeof n&&(n=this.getId(n)),u("table.widefat .spinner").addClass("is-active"),t={action:"inline-save",post_type:typenow,post_ID:n,edit_date:"true",post_status:t},t=u("#edit-"+n).find(":input").serialize()+"&"+u.param(t),u.post(ajaxurl,t,function(t){var e=u("#edit-"+n+" .inline-edit-save .notice-error"),i=e.find(".error");u("table.widefat .spinner").removeClass("is-active"),t?-1!==t.indexOf("<tr")?(u(inlineEditPost.what+n).siblings("tr.hidden").addBack().remove(),u("#edit-"+n).before(t).remove(),u(inlineEditPost.what+n).hide().fadeIn(400,function(){u(this).find(".editinline").attr("aria-expanded","false").trigger("focus"),h.a11y.speak(h.i18n.__("Changes saved."))})):(t=t.replace(/<.[^<>]*?>/g,""),e.removeClass("hidden"),i.html(t),h.a11y.speak(i.text())):(e.removeClass("hidden"),i.text(h.i18n.__("Error while saving the changes.")),h.a11y.speak(h.i18n.__("Error while saving the changes.")))},"html"),!1},revert:function(){var t=u(".widefat"),e=u(".inline-editor",t).attr("id");return e&&(u(".spinner",t).removeClass("is-active"),("bulk-edit"===e?(u("#bulk-edit",t).removeClass("inline-editor").hide().siblings(".hidden").remove(),u("#bulk-titles").empty(),u("#inlineedit").append(u("#bulk-edit")),u("#"+inlineEditPost.whichBulkButtonId)):(u("#"+e).siblings("tr.hidden").addBack().remove(),e=e.substr(e.lastIndexOf("-")+1),u(this.what+e).show().find(".editinline").attr("aria-expanded","false"))).trigger("focus")),!1},getId:function(t){t=u(t).closest("tr").attr("id").split("-");return t[t.length-1]}},u(function(){inlineEditPost.init()}),u(function(){void 0!==h&&h.heartbeat&&h.heartbeat.interval(15)}).on("heartbeat-tick.wp-check-locked-posts",function(t,e){var n=e["wp-check-locked-posts"]||{};u("#the-list tr").each(function(t,e){var i=e.id,e=u(e);n.hasOwnProperty(i)?e.hasClass("wp-locked")||(i=n[i],e.find(".column-title .locked-text").text(i.text),e.find(".check-column checkbox").prop("checked",!1),i.avatar_src&&(i=u("<img />",{class:"avatar avatar-18 photo",width:18,height:18,alt:"",src:i.avatar_src,srcset:i.avatar_src_2x?i.avatar_src_2x+" 2x":void 0}),e.find(".column-title .locked-avatar").empty().append(i)),e.addClass("wp-locked")):e.hasClass("wp-locked")&&e.removeClass("wp-locked").find(".locked-info span").empty()})}).on("heartbeat-send.wp-check-locked-posts",function(t,e){var i=[];u("#the-list tr").each(function(t,e){e.id&&i.push(e.id)}),i.length&&(e["wp-check-locked-posts"]=i)})}(jQuery,window.wp);