# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the wp-migrate-db-pro package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: wp-migrate-db-pro\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-05-30 13:39-0400\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/wp-migrate-db-pro/class/WPMDBDI_Config.php:38
#: src/wp-migrate-db-pro/class/WPMDBDI_Config.php:37
msgid "Classmap could not be generated."
msgstr ""

#: src/wp-migrate-db-pro/template/options-page-outdated-wp.php:14
#, php-format
msgid ""
"This version of %1$s requires WordPress %2$s+. We recommend updating "
"WordPress, but if that's not an option you can download version 1.9.x of WP "
"Migrate from <a href=\"%3$s\">My Account</a>."
msgstr ""

#: src/wp-migrate-db-pro/template/options-tools-subsite.php:11
#, php-format
msgid ""
"%1$s only runs at the Network Admin level. As there is no Tools menu in the "
"Network Admin, the %2$s menu item is located under Settings."
msgstr ""

#: src/wp-migrate-db-pro/template/options.php:12
msgid ""
"<strong>PHP Function Disabled</strong> &mdash; The <code>set_time_limit()</"
"code> function is currently disabled on your server. We use this function to "
"ensure that the migration doesn't time out. We haven't disabled the plugin "
"however, so you're free to cross your fingers and hope for the best. You may "
"want to contact your web host to enable this function."
msgstr ""

#: src/wp-migrate-db-pro/template/options.php:14
#, php-format
msgid "Your current PHP run time limit is set to %s seconds."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackupExport.php:87
#: src/wp-migrate-db-pro/class/Common/BackupExport.php:93
#: src/wp-migrate-db-pro/class/Common/BackupExport.php:107
msgid "MySQL export file not found."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackupExport.php:91
#: src/wp-migrate-db-pro/class/Common/BackupExport.php:97
#: src/wp-migrate-db-pro/class/Common/BackupExport.php:113
msgid "Could not delete the MySQL export file."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Replace.php:656
#: src/wp-migrate-db-pro/class/Common/Replace.php:661
#: src/wp-migrate-db-pro/class/Common/Replace.php:665
#: src/wp-migrate-db-pro/class/Common/Replace.php:672
#: src/wp-migrate-db-pro/class/Common/Replace.php:677
#: src/wp-migrate-db-pro/class/Common/Replace.php:679
#: src/wp-migrate-db-pro/class/Common/Replace.php:690
#: src/wp-migrate-db-pro/class/Common/Replace.php:714
#, php-format
msgid ""
"WP Migrate - Failed to instantiate object for replacement. If the serialized "
"object's class is defined by a plugin, you should enable that plugin for "
"migration requests. \n"
"Class Name: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Replace.php:714
#: src/wp-migrate-db-pro/class/Common/Replace.php:719
#: src/wp-migrate-db-pro/class/Common/Replace.php:723
#: src/wp-migrate-db-pro/class/Common/Replace.php:730
#: src/wp-migrate-db-pro/class/Common/Replace.php:735
#: src/wp-migrate-db-pro/class/Common/Replace.php:737
#: src/wp-migrate-db-pro/class/Common/Replace.php:748
#: src/wp-migrate-db-pro/class/Common/Replace.php:772
#: src/wp-migrate-db-pro/class/Common/Replace.php:733
#: src/wp-migrate-db-pro/class/Common/Replace.php:768
msgid ""
"Failed attempting to do the recursive unserialize replace. Please contact "
"support."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sanitize.php:49
#, php-format
msgid ""
"Sanitization Error: `%1$s` method was expecting %2$s for the `%3$s` field, "
"but got something else: \"%4$s\""
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sanitize.php:72
#, php-format
msgid "%1$s was not expecting data to be an array."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sanitize.php:181
#, php-format
msgid "Unknown sanitization rule \"%1$s\" supplied by %2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Api.php:136
#: src/wp-migrate-db-pro/class/Pro/License.php:479
#: src/wp-migrate-db-pro/class/Pro/License.php:482
#: src/wp-migrate-db-pro/class/Pro/License.php:478
#: src/wp-migrate-db-pro/class/Pro/License.php:480
#: src/wp-migrate-db-pro/class/Pro/License.php:483
#: src/wp-migrate-db-pro/class/Pro/License.php:492
#: src/wp-migrate-db-pro/class/Pro/License.php:552
#: src/wp-migrate-db-pro/class/Pro/License.php:496
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:526
#: src/wp-migrate-db-pro/class/Pro/License.php:553
#: src/wp-migrate-db-pro/class/Pro/Api.php:130
#: src/wp-migrate-db-pro/class/Pro/License.php:546
#, php-format
msgid ""
"<strong>Could not connect to api.deliciousbrains.com</strong> &mdash; You "
"will not receive update notifications or be able to activate your license "
"until this is fixed. This issue is often caused by an improperly configured "
"SSL server (https). We recommend <a href=\"%1$s\" target=\"_blank\">fixing "
"the SSL configuration on your server</a>, but if you need a quick fix you "
"can:%2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Api.php:136
#: src/wp-migrate-db-pro/class/Pro/License.php:480
#: src/wp-migrate-db-pro/class/Pro/License.php:483
#: src/wp-migrate-db-pro/class/Pro/License.php:479
#: src/wp-migrate-db-pro/class/Pro/License.php:481
#: src/wp-migrate-db-pro/class/Pro/License.php:484
#: src/wp-migrate-db-pro/class/Pro/License.php:493
#: src/wp-migrate-db-pro/class/Pro/License.php:553
#: src/wp-migrate-db-pro/class/Pro/License.php:497
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:527
#: src/wp-migrate-db-pro/class/Pro/License.php:554
#: src/wp-migrate-db-pro/class/Pro/Api.php:130
#: src/wp-migrate-db-pro/class/Pro/License.php:547
msgid "Temporarily disable SSL for connections to api.deliciousbrains.com"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Api.php:144
#: src/wp-migrate-db-pro/class/Pro/License.php:487
#: src/wp-migrate-db-pro/class/Pro/License.php:490
#: src/wp-migrate-db-pro/class/Pro/License.php:486
#: src/wp-migrate-db-pro/class/Pro/License.php:488
#: src/wp-migrate-db-pro/class/Pro/License.php:491
#: src/wp-migrate-db-pro/class/Pro/License.php:500
#: src/wp-migrate-db-pro/class/Pro/License.php:560
#: src/wp-migrate-db-pro/class/Pro/License.php:504
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:534
#: src/wp-migrate-db-pro/class/Pro/License.php:561
#: src/wp-migrate-db-pro/class/Pro/Api.php:138
#: src/wp-migrate-db-pro/class/Pro/License.php:554
#, php-format
msgid ""
"We've detected that <code>WP_HTTP_BLOCK_EXTERNAL</code> is enabled and the "
"host <strong>%1$s</strong> has not been added to <code>WP_ACCESSIBLE_HOSTS</"
"code>. Please disable <code>WP_HTTP_BLOCK_EXTERNAL</code> or add "
"<strong>%1$s</strong> to <code>WP_ACCESSIBLE_HOSTS</code> to continue. <a "
"href=\"%2$s\" target=\"_blank\">More information</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Api.php:189
#: src/wp-migrate-db-pro/class/Pro/Api.php:183
msgid ""
"<strong>Delicious Brains API is Down — </strong>Unfortunately we're "
"experiencing some problems with our server."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Api.php:193
#: src/wp-migrate-db-pro/class/Pro/Api.php:187
#, php-format
msgctxt "ex. 2 hours ago"
msgid "%s ago"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Api.php:198
#: src/wp-migrate-db-pro/class/Pro/Api.php:192
msgid "Here's the most recent update on its status"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Download.php:84
#, php-format
msgid ""
"Error retrieving download from deliciousbrains.com. Please try again or "
"download manually from <a href=\"%1$s\">%2$s</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Download.php:84
msgctxt "Delicious Brains account"
msgid "My Account"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Import.php:177
msgid ""
"The server is not compatible with gzip, please decompress the import file "
"and try again."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Import.php:189
#: src/wp-migrate-db-pro/class/Pro/Import.php:191
msgid "Unable to read data from the import file"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Import.php:273
#: src/wp-migrate-db-pro/class/Pro/Import.php:275
msgid "An error occurred while uploading the file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Import.php:304
#: src/wp-migrate-db-pro/class/Pro/Import.php:314
msgid "An error occurred while decompressing the import file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Import.php:420
#: src/wp-migrate-db-pro/class/Pro/Import.php:434
msgid "The import file could not be read."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Import.php:515
#: src/wp-migrate-db-pro/class/Pro/Import.php:541
#: src/wp-migrate-db-pro/class/Pro/Import.php:555
#, php-format
msgid "Failed to import the SQL query: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:232
#: src/wp-migrate-db-pro/class/Pro/License.php:236
#: src/wp-migrate-db-pro/class/Pro/License.php:269
#: src/wp-migrate-db-pro/class/Pro/License.php:240
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:243
#: src/wp-migrate-db-pro/class/Pro/License.php:270
msgid ""
"If you have an <strong>active license</strong>, you may send an email to the "
"following address."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:234
#: src/wp-migrate-db-pro/class/Pro/License.php:238
#: src/wp-migrate-db-pro/class/Pro/License.php:271
#: src/wp-migrate-db-pro/class/Pro/License.php:242
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:245
#: src/wp-migrate-db-pro/class/Pro/License.php:272
msgid ""
"Please copy the Diagnostic Info &amp; Error Log info below into a text file "
"and attach it to your email. Do the same for any other site involved in your "
"email."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:266
#: src/wp-migrate-db-pro/class/Pro/License.php:270
#: src/wp-migrate-db-pro/class/Pro/License.php:303
#: src/wp-migrate-db-pro/class/Pro/License.php:274
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:277
#: src/wp-migrate-db-pro/class/Pro/License.php:304
#, php-format
msgid ""
"<strong>Addons Unavailable</strong> &mdash; Addons are not included with the "
"Personal license. Visit <a href=\"%s\" target=\"_blank\">My Account</a> to "
"upgrade in just a few clicks."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:481
#: src/wp-migrate-db-pro/class/Pro/License.php:484
#: src/wp-migrate-db-pro/class/Pro/License.php:480
#: src/wp-migrate-db-pro/class/Pro/License.php:482
#: src/wp-migrate-db-pro/class/Pro/License.php:485
#: src/wp-migrate-db-pro/class/Pro/License.php:494
#: src/wp-migrate-db-pro/class/Pro/License.php:554
#: src/wp-migrate-db-pro/class/Pro/License.php:498
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:528
#: src/wp-migrate-db-pro/class/Pro/License.php:555
#: src/wp-migrate-db-pro/class/Pro/License.php:548
#, php-format
msgid ""
"<strong>Could not connect to api.deliciousbrains.com</strong> &mdash; You "
"will not receive update notifications or be able to activate your license "
"until this is fixed. This issue is often caused by an improperly configured "
"SSL server (https). We recommend <a href=\"%1$s\" target=\"_blank\">fixing "
"the SSL configuration on your server</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:483
#: src/wp-migrate-db-pro/class/Pro/License.php:486
#: src/wp-migrate-db-pro/class/Pro/License.php:482
#: src/wp-migrate-db-pro/class/Pro/License.php:484
#: src/wp-migrate-db-pro/class/Pro/License.php:487
#: src/wp-migrate-db-pro/class/Pro/License.php:496
#: src/wp-migrate-db-pro/class/Pro/License.php:556
#: src/wp-migrate-db-pro/class/Pro/License.php:500
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:530
#: src/wp-migrate-db-pro/class/Pro/License.php:557
#: src/wp-migrate-db-pro/class/Pro/License.php:550
msgid ""
"Could not connect to api.deliciousbrains.com - You will not receive update "
"notifications or be able to activate your license until this is fixed. This "
"issue is often caused by an improperly configured SSL server (https). We "
"recommend fixing the SSL configuration on your server, but if you need a "
"quick fix you can temporarily disable SSL for connections to api."
"deliciousbrains.com by adding `define( 'DBRAINS_API_BASE', 'http://api."
"deliciousbrains.com' );` to your wp-config.php file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:488
#: src/wp-migrate-db-pro/class/Pro/License.php:491
#: src/wp-migrate-db-pro/class/Pro/License.php:487
#: src/wp-migrate-db-pro/class/Pro/License.php:489
#: src/wp-migrate-db-pro/class/Pro/License.php:492
#: src/wp-migrate-db-pro/class/Pro/License.php:501
#: src/wp-migrate-db-pro/class/Pro/License.php:561
#: src/wp-migrate-db-pro/class/Pro/License.php:505
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:535
#: src/wp-migrate-db-pro/class/Pro/License.php:562
#: src/wp-migrate-db-pro/class/Pro/License.php:555
#, php-format
msgid ""
"We've detected that WP_HTTP_BLOCK_EXTERNAL is enabled and the host %1$s has "
"not been added to WP_ACCESSIBLE_HOSTS. Please disable WP_HTTP_BLOCK_EXTERNAL "
"or add %1$s to WP_ACCESSIBLE_HOSTS to continue."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:491
#: src/wp-migrate-db-pro/class/Pro/License.php:493
#: src/wp-migrate-db-pro/class/Pro/License.php:494
#: src/wp-migrate-db-pro/class/Pro/License.php:496
#: src/wp-migrate-db-pro/class/Pro/License.php:490
#: src/wp-migrate-db-pro/class/Pro/License.php:492
#: src/wp-migrate-db-pro/class/Pro/License.php:495
#: src/wp-migrate-db-pro/class/Pro/License.php:497
#, php-format
msgid ""
"<strong>License Cancelled</strong> &mdash; The license key below has been "
"cancelled. Please remove it and enter a valid license key. <br /><br /> Your "
"license key can be found in <a href=\"%s\" target=\"_blank\">My Account</a>. "
"If you don't have an account yet, <a href=\"%s\" target=\"_blank\">purchase "
"a new license</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:495
#: src/wp-migrate-db-pro/class/Pro/License.php:498
#: src/wp-migrate-db-pro/class/Pro/License.php:494
#: src/wp-migrate-db-pro/class/Pro/License.php:496
#: src/wp-migrate-db-pro/class/Pro/License.php:499
#: src/wp-migrate-db-pro/class/Pro/License.php:508
#: src/wp-migrate-db-pro/class/Pro/License.php:568
#: src/wp-migrate-db-pro/class/Pro/License.php:512
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:542
#: src/wp-migrate-db-pro/class/Pro/License.php:569
#: src/wp-migrate-db-pro/class/Pro/License.php:562
#, php-format
msgid ""
"License Cancelled - Please login to your account (%s) to renew or upgrade "
"your license and enable push and pull."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:498
#: src/wp-migrate-db-pro/class/Pro/License.php:499
#: src/wp-migrate-db-pro/class/Pro/License.php:501
#: src/wp-migrate-db-pro/class/Pro/License.php:502
#: src/wp-migrate-db-pro/class/Pro/License.php:497
#: src/wp-migrate-db-pro/class/Pro/License.php:500
#: src/wp-migrate-db-pro/class/Pro/License.php:503
#: src/wp-migrate-db-pro/class/Pro/License.php:511
#: src/wp-migrate-db-pro/class/Pro/License.php:512
#: src/wp-migrate-db-pro/class/Pro/License.php:571
#: src/wp-migrate-db-pro/class/Pro/License.php:572
#: src/wp-migrate-db-pro/class/Pro/License.php:515
#: src/wp-migrate-db-pro/class/Pro/License.php:516
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:545
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:546
#: src/wp-migrate-db-pro/class/Pro/License.php:573
#: src/wp-migrate-db-pro/class/Pro/License.php:565
#: src/wp-migrate-db-pro/class/Pro/License.php:566
msgid "Your License Has Expired"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:502
#: src/wp-migrate-db-pro/class/Pro/License.php:503
#: src/wp-migrate-db-pro/class/Pro/License.php:505
#: src/wp-migrate-db-pro/class/Pro/License.php:506
#: src/wp-migrate-db-pro/class/Pro/License.php:501
#: src/wp-migrate-db-pro/class/Pro/License.php:504
#: src/wp-migrate-db-pro/class/Pro/License.php:507
#: src/wp-migrate-db-pro/class/Pro/License.php:515
#: src/wp-migrate-db-pro/class/Pro/License.php:516
#: src/wp-migrate-db-pro/class/Pro/License.php:575
#: src/wp-migrate-db-pro/class/Pro/License.php:576
#: src/wp-migrate-db-pro/class/Pro/License.php:519
#: src/wp-migrate-db-pro/class/Pro/License.php:520
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:549
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:550
#: src/wp-migrate-db-pro/class/Pro/License.php:577
#: src/wp-migrate-db-pro/class/Pro/License.php:569
#: src/wp-migrate-db-pro/class/Pro/License.php:570
#, php-format
msgid "Login to <a href=\"%s\">My Account</a> to renew."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:504
#: src/wp-migrate-db-pro/class/Pro/License.php:507
#: src/wp-migrate-db-pro/class/Pro/License.php:503
#: src/wp-migrate-db-pro/class/Pro/License.php:505
#: src/wp-migrate-db-pro/class/Pro/License.php:508
#: src/wp-migrate-db-pro/class/Pro/License.php:517
#: src/wp-migrate-db-pro/class/Pro/License.php:577
#: src/wp-migrate-db-pro/class/Pro/License.php:521
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:551
#: src/wp-migrate-db-pro/class/Pro/License.php:578
#: src/wp-migrate-db-pro/class/Pro/License.php:571
#, php-format
msgid "Login to your account to renew (%s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:507
#: src/wp-migrate-db-pro/class/Pro/License.php:508
#: src/wp-migrate-db-pro/class/Pro/License.php:510
#: src/wp-migrate-db-pro/class/Pro/License.php:511
#: src/wp-migrate-db-pro/class/Pro/License.php:506
#: src/wp-migrate-db-pro/class/Pro/License.php:509
#: src/wp-migrate-db-pro/class/Pro/License.php:512
#: src/wp-migrate-db-pro/class/Pro/License.php:520
#: src/wp-migrate-db-pro/class/Pro/License.php:521
#: src/wp-migrate-db-pro/class/Pro/License.php:580
#: src/wp-migrate-db-pro/class/Pro/License.php:581
#: src/wp-migrate-db-pro/class/Pro/License.php:524
#: src/wp-migrate-db-pro/class/Pro/License.php:525
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:554
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:555
#: src/wp-migrate-db-pro/class/Pro/License.php:582
#: src/wp-migrate-db-pro/class/Pro/License.php:574
#: src/wp-migrate-db-pro/class/Pro/License.php:575
#, php-format
msgid ""
"<strong>No Activations Left</strong> &mdash; Please visit <a href=\"%s\" "
"target=\"_blank\">My Account</a> to upgrade your license and enable push and "
"pull."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:509
#: src/wp-migrate-db-pro/class/Pro/License.php:512
#: src/wp-migrate-db-pro/class/Pro/License.php:508
#: src/wp-migrate-db-pro/class/Pro/License.php:510
#: src/wp-migrate-db-pro/class/Pro/License.php:513
#: src/wp-migrate-db-pro/class/Pro/License.php:522
#: src/wp-migrate-db-pro/class/Pro/License.php:582
#: src/wp-migrate-db-pro/class/Pro/License.php:526
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:556
#: src/wp-migrate-db-pro/class/Pro/License.php:583
#: src/wp-migrate-db-pro/class/Pro/License.php:576
#, php-format
msgid ""
"No Activations Left - Please visit your account (%s) to upgrade your license "
"and enable push and pull."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:512
#: src/wp-migrate-db-pro/class/Pro/License.php:514
#: src/wp-migrate-db-pro/class/Pro/License.php:515
#: src/wp-migrate-db-pro/class/Pro/License.php:517
#: src/wp-migrate-db-pro/class/Pro/License.php:511
#: src/wp-migrate-db-pro/class/Pro/License.php:513
#: src/wp-migrate-db-pro/class/Pro/License.php:516
#: src/wp-migrate-db-pro/class/Pro/License.php:518
#: src/wp-migrate-db-pro/class/Pro/License.php:525
#: src/wp-migrate-db-pro/class/Pro/License.php:527
#: src/wp-migrate-db-pro/class/Pro/License.php:585
#: src/wp-migrate-db-pro/class/Pro/License.php:587
#: src/wp-migrate-db-pro/class/Pro/License.php:529
#: src/wp-migrate-db-pro/class/Pro/License.php:531
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:559
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:561
#: src/wp-migrate-db-pro/class/Pro/License.php:586
#: src/wp-migrate-db-pro/class/Pro/License.php:588
#: src/wp-migrate-db-pro/class/Pro/License.php:579
#: src/wp-migrate-db-pro/class/Pro/License.php:581
#, php-format
msgid ""
"<strong>License Not Found</strong> &mdash; The license key below cannot be "
"found in our database. Please remove it and enter a valid license key.  <br /"
"><br />Your license key can be found in <a href=\"%s\" target=\"_blank\">My "
"Account</a> . If you don't have an account yet, <a href=\"%s\" "
"target=\"_blank\">purchase a new license</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:516
#: src/wp-migrate-db-pro/class/Pro/License.php:519
#: src/wp-migrate-db-pro/class/Pro/License.php:515
#: src/wp-migrate-db-pro/class/Pro/License.php:517
#: src/wp-migrate-db-pro/class/Pro/License.php:520
#: src/wp-migrate-db-pro/class/Pro/License.php:529
#: src/wp-migrate-db-pro/class/Pro/License.php:589
#: src/wp-migrate-db-pro/class/Pro/License.php:533
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:563
#: src/wp-migrate-db-pro/class/Pro/License.php:590
#: src/wp-migrate-db-pro/class/Pro/License.php:583
#, php-format
msgid ""
"Your License Was Not Found - The license key below cannot be found in our "
"database. Please remove it and enter a valid license key. Please visit your "
"account (%s) to double check your license key."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:519
#: src/wp-migrate-db-pro/class/Pro/License.php:522
#: src/wp-migrate-db-pro/class/Pro/License.php:518
#: src/wp-migrate-db-pro/class/Pro/License.php:520
#: src/wp-migrate-db-pro/class/Pro/License.php:523
#: src/wp-migrate-db-pro/class/Pro/License.php:532
#: src/wp-migrate-db-pro/class/Pro/License.php:592
#: src/wp-migrate-db-pro/class/Pro/License.php:536
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:566
#: src/wp-migrate-db-pro/class/Pro/License.php:593
#: src/wp-migrate-db-pro/class/Pro/License.php:586
#, php-format
msgid "<strong>License Not Found</strong> &mdash; %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:520
#: src/wp-migrate-db-pro/class/Pro/License.php:523
#: src/wp-migrate-db-pro/class/Pro/License.php:519
#: src/wp-migrate-db-pro/class/Pro/License.php:521
#: src/wp-migrate-db-pro/class/Pro/License.php:524
#: src/wp-migrate-db-pro/class/Pro/License.php:533
#: src/wp-migrate-db-pro/class/Pro/License.php:593
#: src/wp-migrate-db-pro/class/Pro/License.php:537
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:567
#: src/wp-migrate-db-pro/class/Pro/License.php:594
#: src/wp-migrate-db-pro/class/Pro/License.php:587
#, php-format
msgid "License Not Found - %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:523
#: src/wp-migrate-db-pro/class/Pro/License.php:524
#: src/wp-migrate-db-pro/class/Pro/License.php:526
#: src/wp-migrate-db-pro/class/Pro/License.php:527
#: src/wp-migrate-db-pro/class/Pro/License.php:522
#: src/wp-migrate-db-pro/class/Pro/License.php:525
#: src/wp-migrate-db-pro/class/Pro/License.php:528
msgid "Your License Is Inactive"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:523
#: src/wp-migrate-db-pro/class/Pro/License.php:524
#: src/wp-migrate-db-pro/class/Pro/License.php:526
#: src/wp-migrate-db-pro/class/Pro/License.php:527
#: src/wp-migrate-db-pro/class/Pro/License.php:522
#: src/wp-migrate-db-pro/class/Pro/License.php:525
#: src/wp-migrate-db-pro/class/Pro/License.php:528
msgid "Your license has been deactivated for this install."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:523
#: src/wp-migrate-db-pro/class/Pro/License.php:524
#: src/wp-migrate-db-pro/class/Pro/License.php:526
#: src/wp-migrate-db-pro/class/Pro/License.php:527
#: src/wp-migrate-db-pro/class/Pro/License.php:522
#: src/wp-migrate-db-pro/class/Pro/License.php:525
#: src/wp-migrate-db-pro/class/Pro/License.php:528
msgid "Reactivate your license"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:527
#: src/wp-migrate-db-pro/class/Pro/License.php:530
#: src/wp-migrate-db-pro/class/Pro/License.php:526
#: src/wp-migrate-db-pro/class/Pro/License.php:528
#: src/wp-migrate-db-pro/class/Pro/License.php:531
#: src/wp-migrate-db-pro/class/Pro/License.php:540
#: src/wp-migrate-db-pro/class/Pro/License.php:600
#: src/wp-migrate-db-pro/class/Pro/License.php:544
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:574
#: src/wp-migrate-db-pro/class/Pro/License.php:601
#: src/wp-migrate-db-pro/class/Pro/License.php:594
#, php-format
msgid ""
"<strong>An Unexpected Error Occurred</strong> &mdash; Please contact us at "
"<a href=\"%1$s\">%2$s</a> and quote the following: <p>%3$s</p>"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:528
#: src/wp-migrate-db-pro/class/Pro/License.php:531
#: src/wp-migrate-db-pro/class/Pro/License.php:527
#: src/wp-migrate-db-pro/class/Pro/License.php:529
#: src/wp-migrate-db-pro/class/Pro/License.php:532
#: src/wp-migrate-db-pro/class/Pro/License.php:541
#: src/wp-migrate-db-pro/class/Pro/License.php:601
#: src/wp-migrate-db-pro/class/Pro/License.php:545
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:575
#: src/wp-migrate-db-pro/class/Pro/License.php:602
#: src/wp-migrate-db-pro/class/Pro/License.php:595
#, php-format
msgid ""
"An Unexpected Error Occurred - Please contact us at %2$s and quote the "
"following: %3$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:646
#: src/wp-migrate-db-pro/class/Common/UI/TemplateBase.php:84
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:81
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:239
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:543
#: src/wp-migrate-db-pro/class/Pro/License.php:649
#: src/wp-migrate-db-pro/class/Pro/License.php:645
#: src/wp-migrate-db-pro/class/Pro/License.php:647
#: src/wp-migrate-db-pro/class/Pro/License.php:650
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:242
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:546
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:48
#: src/wp-migrate-db-pro/class/Pro/License.php:663
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:254
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:558
#: src/wp-migrate-db-pro/class/Pro/License.php:723
#: src/wp-migrate-db-pro/class/Pro/License.php:667
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:697
#: src/wp-migrate-db-pro/class/Pro/License.php:724
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:50
#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:27
#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:28
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:52
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:258
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:562
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:54
#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:54
#: src/wp-migrate-db-pro/class/Pro/License.php:717
msgctxt "Plugin configuration and preferences"
msgid "Settings"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:647
#: src/wp-migrate-db-pro/class/Pro/License.php:650
#: src/wp-migrate-db-pro/class/Pro/License.php:646
#: src/wp-migrate-db-pro/class/Pro/License.php:648
#: src/wp-migrate-db-pro/class/Pro/License.php:651
#: src/wp-migrate-db-pro/class/Pro/License.php:664
#: src/wp-migrate-db-pro/class/Pro/License.php:724
#: src/wp-migrate-db-pro/class/Pro/License.php:668
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:698
#: src/wp-migrate-db-pro/class/Pro/License.php:725
#: src/wp-migrate-db-pro/class/Pro/License.php:718
#, php-format
msgid ""
"To finish activating WP Migrate, please go to %1$s and enter your license "
"key. If you don't have a license key, you may <a href=\"%2$s\">purchase one</"
"a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:791
#: src/wp-migrate-db-pro/class/Pro/License.php:794
#: src/wp-migrate-db-pro/class/Pro/License.php:790
#: src/wp-migrate-db-pro/class/Pro/License.php:792
#: src/wp-migrate-db-pro/class/Pro/License.php:795
#: src/wp-migrate-db-pro/class/Pro/License.php:817
#: src/wp-migrate-db-pro/class/Pro/License.php:877
#: src/wp-migrate-db-pro/class/Pro/License.php:821
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:851
#: src/wp-migrate-db-pro/class/Pro/License.php:878
#: src/wp-migrate-db-pro/class/Pro/License.php:867
#, php-format
msgid ""
"<strong>Activate Your License</strong> &mdash; Please <a href=\"%s\" "
"class=\"%s\">enter your license key</a> to enable push and pull "
"functionality, priority support and plugin updates."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:792
#: src/wp-migrate-db-pro/class/Pro/License.php:795
#: src/wp-migrate-db-pro/class/Pro/License.php:791
#: src/wp-migrate-db-pro/class/Pro/License.php:793
#: src/wp-migrate-db-pro/class/Pro/License.php:796
#: src/wp-migrate-db-pro/class/Pro/License.php:818
#: src/wp-migrate-db-pro/class/Pro/License.php:878
#: src/wp-migrate-db-pro/class/Pro/License.php:822
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:852
#: src/wp-migrate-db-pro/class/Pro/License.php:879
#: src/wp-migrate-db-pro/class/Pro/License.php:868
#, php-format
msgid ""
"<strong>Activate Your License</strong> &mdash; Please <a href=\"%s\">enter "
"your license key</a> to activate any upgrades associated with your license."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:813
#: src/wp-migrate-db-pro/class/Pro/License.php:816
#: src/wp-migrate-db-pro/class/Pro/License.php:812
#: src/wp-migrate-db-pro/class/Pro/License.php:814
#: src/wp-migrate-db-pro/class/Pro/License.php:817
#: src/wp-migrate-db-pro/class/Pro/License.php:839
#: src/wp-migrate-db-pro/class/Pro/License.php:899
#: src/wp-migrate-db-pro/class/Pro/License.php:843
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:873
#: src/wp-migrate-db-pro/class/Pro/License.php:900
#: src/wp-migrate-db-pro/class/Pro/License.php:889
msgid ""
"<strong>We've temporarily activated your license and will complete the "
"activation once the Delicious Brains API is available again.</strong>"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:839
#: src/wp-migrate-db-pro/class/Pro/License.php:842
#: src/wp-migrate-db-pro/class/Pro/License.php:838
#: src/wp-migrate-db-pro/class/Pro/License.php:840
#: src/wp-migrate-db-pro/class/Pro/License.php:843
#: src/wp-migrate-db-pro/class/Pro/License.php:865
#: src/wp-migrate-db-pro/class/Pro/License.php:925
#: src/wp-migrate-db-pro/class/Pro/License.php:869
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:899
#: src/wp-migrate-db-pro/class/Pro/License.php:926
#: src/wp-migrate-db-pro/class/Pro/License.php:915
msgid "Updates are only available to those with an active license. "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:840
#: src/wp-migrate-db-pro/class/Pro/License.php:843
#: src/wp-migrate-db-pro/class/Pro/License.php:839
#: src/wp-migrate-db-pro/class/Pro/License.php:841
#: src/wp-migrate-db-pro/class/Pro/License.php:844
#: src/wp-migrate-db-pro/class/Pro/License.php:866
#: src/wp-migrate-db-pro/class/Pro/License.php:926
#: src/wp-migrate-db-pro/class/Pro/License.php:870
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:900
#: src/wp-migrate-db-pro/class/Pro/License.php:927
#: src/wp-migrate-db-pro/class/Pro/License.php:916
msgid "Only active licenses can download and install addons. "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:841
#: src/wp-migrate-db-pro/class/Pro/License.php:844
#: src/wp-migrate-db-pro/class/Pro/License.php:840
#: src/wp-migrate-db-pro/class/Pro/License.php:842
#: src/wp-migrate-db-pro/class/Pro/License.php:845
#: src/wp-migrate-db-pro/class/Pro/License.php:867
#: src/wp-migrate-db-pro/class/Pro/License.php:927
#: src/wp-migrate-db-pro/class/Pro/License.php:871
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:901
#: src/wp-migrate-db-pro/class/Pro/License.php:928
#: src/wp-migrate-db-pro/class/Pro/License.php:917
msgid "Only active licenses can submit support requests. "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:842
#: src/wp-migrate-db-pro/class/Pro/License.php:845
#: src/wp-migrate-db-pro/class/Pro/License.php:841
#: src/wp-migrate-db-pro/class/Pro/License.php:843
#: src/wp-migrate-db-pro/class/Pro/License.php:846
#: src/wp-migrate-db-pro/class/Pro/License.php:868
#: src/wp-migrate-db-pro/class/Pro/License.php:928
#: src/wp-migrate-db-pro/class/Pro/License.php:872
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:902
#: src/wp-migrate-db-pro/class/Pro/License.php:929
#: src/wp-migrate-db-pro/class/Pro/License.php:918
msgid ""
"All features will continue to work, but you won't be able to receive updates "
"or email support. "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:935
#: src/wp-migrate-db-pro/class/Pro/License.php:938
#: src/wp-migrate-db-pro/class/Pro/License.php:934
#: src/wp-migrate-db-pro/class/Pro/License.php:936
#: src/wp-migrate-db-pro/class/Pro/License.php:939
#: src/wp-migrate-db-pro/class/Pro/License.php:961
#: src/wp-migrate-db-pro/class/Pro/License.php:1021
#: src/wp-migrate-db-pro/class/Pro/License.php:965
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:995
#: src/wp-migrate-db-pro/class/Pro/License.php:1022
#: src/wp-migrate-db-pro/class/Pro/License.php:1011
msgctxt "Delete license"
msgid "Remove"
msgstr ""

#: src/wp-migrate-db-pro/template/common/muplugin-failed-update-warning.php:3
msgid "<strong>Compatibility Plugin Update Failed</strong> &mdash; "
msgstr ""

#: src/wp-migrate-db-pro/template/common/muplugin-failed-update-warning.php:4
msgid ""
"We could not update the Compatibility Mode plugin because the mu-plugins "
"folder is not writable. Please update the permissions of the mu-plugins "
"folder to enable Compatibility Mode. "
msgstr ""

#: src/wp-migrate-db-pro/template/pro/block-external-warning.php:4
#, php-format
msgid ""
"We've detected that <code>WP_HTTP_BLOCK_EXTERNAL</code> is enabled which "
"will prevent WP Migrate from functioning properly. You should either disable "
"<code>WP_HTTP_BLOCK_EXTERNAL</code> or add any sites that you'd like to "
"migrate to or from with WP Migrate to <code>WP_ACCESSIBLE_HOSTS</code> (api."
"deliciousbrains.com must be added to <code>WP_ACCESSIBLE_HOSTS</code> for "
"the API to work). More information on this can be found <a href=\"%s\" "
"target=\"_blank\">here</a>."
msgstr ""

#: src/wp-migrate-db-pro/template/pro/notice-enable-usage-tracking.php:2
msgid "Help Us Improve WP Migrate"
msgstr ""

#: src/wp-migrate-db-pro/template/pro/notice-enable-usage-tracking.php:4
#, php-format
msgid ""
"We are constantly working to make WP Migrate the best that it can be, but "
"that's hard to do if we don't know how you're using it. Please enable data "
"sharing so that we will receive <a href=\"%s\" target=\"_blank\">some "
"information</a> about each migration that you run. We will use this data to "
"improve WP Migrate and provide you with better support. This setting can be "
"changed at any time from the Settings tab."
msgstr ""

#: src/wp-migrate-db-pro/template/pro/secret-key-warning.php:3
#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:79
msgid "Improve Security"
msgstr ""

#: src/wp-migrate-db-pro/template/pro/secret-key-warning.php:4
#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:82
#, php-format
msgid ""
"We have implemented a more secure method of secret key generation since your "
"key was generated. We recommend you <a href=\"%s\">visit the Settings tab</"
"a> and reset your secret key."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:174
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:191
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:193
msgid "Profile not found or unable to be generated from params."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:201
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:222
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:231
msgid "Missing path to import file. Use --import-file=/path/to/import.sql.gz"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:238
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:270
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:762
#, php-format
msgid "The following table(s) do not exist in the %1$s database: %2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:306
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:371
#, php-format
msgid ""
"We were expecting a JSON response, instead we received: %2$s (function name: "
"%1$s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:365
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:430
msgid "Initiating migration..."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:421
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:481
msgid "Exporting tables"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:424
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:970
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:978
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:977
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:484
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:984
msgid "Running find & replace"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:427
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:965
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:973
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:972
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:487
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:979
msgid "Performing backup"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:506
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:509
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:569
msgid "No tables selected for migration."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:613
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:616
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:682
msgid "Cleaning up..."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:710
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:713
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:783
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:139
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:127
msgid "Unable to move exported file."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:780
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:783
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:844
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:426
msgid "Parameter errors: "
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:782
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:785
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:846
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:428
#, php-format
msgid "unknown %s parameter"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:791
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:794
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:854
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:436
msgid "--"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:796
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:799
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:859
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:441
msgid "Missing action parameter"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:815
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:818
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:878
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:460
msgid "Please make sure Regular Expression find & replace pattern is valid"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:819
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:829
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:822
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:832
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:882
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:892
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:464
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:474
msgid "Missing Regex find and replace values."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:837
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:853
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:840
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:856
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:900
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:916
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:482
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:498
msgid "Missing case sensitive find and replace values."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:863
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:866
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:926
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:508
msgid "Missing find and replace values."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:866
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:869
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:929
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:511
msgid "Find value is required."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:873
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:876
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:936
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:518
msgid "Replace value is required."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:878
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:881
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:941
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:523
#, php-format
msgid "%1$s and %2$s must contain the same number of values"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:924
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:927
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:987
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:569
#, php-format
msgid ""
"Cannot write to file \"%1$s\". Please ensure that the specified directory "
"exists and is writable."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:81
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:83
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:92
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:95
msgid "You must provide a destination filename."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:205
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:651
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:685
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:207
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:244
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:717
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:238
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:720
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:726
msgid "WP Migrate CLI class not available."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:213
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:661
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:695
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:215
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:210
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:736
#, php-format
msgid "Export saved to: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:216
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:218
#: src/wp-migrate-db-pro/class/Common/Cli/Command.php:213
msgid "Find & Replace complete"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Compatibility/CompatibilityManager.php:197
#: src/wp-migrate-db-pro/class/Common/Compatibility/CompatibilityManager.php:202
#, php-format
msgid ""
"The compatibility plugin could not be activated because your mu-plugin "
"directory is currently not writable.  Please update the permissions of the "
"mu-plugins folder:  %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Compatibility/CompatibilityManager.php:230
#: src/wp-migrate-db-pro/class/Common/Compatibility/CompatibilityManager.php:235
#, php-format
msgid ""
"The compatibility plugin could not be deactivated because your mu-plugin "
"directory is currently not writable.  Please update the permissions of the "
"mu-plugins folder: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:801
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:806
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:815
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:816
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:821
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:823
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:824
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:829
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:827
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:844
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:845
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:855
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:849
#, php-format
msgid "<h3>Output prevented download. </h3> %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:804
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:809
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:818
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:819
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:824
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:826
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:827
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:832
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:830
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:847
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:848
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:858
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:852
msgid "Could not find the file to download:"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/Helper.php:106
#: src/wp-migrate-db-pro/class/Common/Http/Helper.php:102
msgid "Invalid Request. Did you pass the correct nonce?"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/Http.php:90
#: src/wp-migrate-db-pro/class/Common/Http/Http.php:96
#: src/wp-migrate-db-pro/class/Common/Http/Http.php:99
#, php-format
msgid "An error occurred - JSON response: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/Http.php:132
#: src/wp-migrate-db-pro/class/Common/Http/Http.php:128
#: src/wp-migrate-db-pro/class/Common/Http/Http.php:130
#, php-format
msgid "Invalid nonce for: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/Http.php:139
#: src/wp-migrate-db-pro/class/Common/Http/Http.php:135
#: src/wp-migrate-db-pro/class/Common/Http/Http.php:137
#, php-format
msgid "Access denied for: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:282
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:283
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:254
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:256
#, php-format
msgid ""
"We've detected that <code>WP_HTTP_BLOCK_EXTERNAL</code> is enabled and the "
"host <strong>%1$s</strong> has not been added to <code>WP_ACCESSIBLE_HOSTS</"
"code>. Please disable <code>WP_HTTP_BLOCK_EXTERNAL</code> or add "
"<strong>%1$s</strong> to <code>WP_ACCESSIBLE_HOSTS</code> to continue. <a "
"href=\"%2$s\" target=\"_blank\">More information</a>. (#147 - scope: %3$s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:293
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:294
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:265
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:267
#, php-format
msgid ""
"The connection failed, an unexpected error occurred, please contact support. "
"(#121 - scope: %s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:298
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:299
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:270
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:272
#, php-format
msgid ""
"The connection to the remote server has timed out, no changes have been "
"committed. (#134 - scope: %s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:306
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:307
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:278
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:280
#, php-format
msgid ""
"Couldn't connect over HTTPS. You might want to try regular HTTP instead. "
"(#121 - scope: %s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:312
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:313
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:284
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:286
#, php-format
msgid ""
"<strong>HTTPS Connection Error:</strong>  (#121 - scope: %s) This typically "
"means that the version of OpenSSL that your local site is using to connect "
"to the remote is incompatible or, more likely, being rejected by the remote "
"server because it's insecure. <a href=\"%s\" target=\"_blank\">See our "
"documentation</a> for possible solutions."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:320
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:321
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:292
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:294
msgid ""
"The remote site is protected with Basic Authentication. Please enter the "
"username and password above to continue. (401 Unauthorized)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:325
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:326
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:297
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:299
#, php-format
msgid ""
"Unable to connect to the remote server, the remote server responded with: %s "
"%s (scope: %s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:330
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:331
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:302
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:304
#, php-format
msgid ""
"Unable to connect to the remote server, please check the connection details "
"- %1$s %2$s (#129 - scope: %3$s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:335
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:336
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:307
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:309
#, php-format
msgid ""
"WP Migrate does not seem to be installed or active on the remote site. (#131 "
"- scope: %s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:340
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:341
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:312
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:314
#, php-format
msgid ""
"A response was expected from the remote, instead we got nothing. (#146 - "
"scope: %1$s) Please review %2$s for possible solutions."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:340
#: src/wp-migrate-db-pro/class/Common/Sql/TableHelper.php:140
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:341
#: src/wp-migrate-db-pro/class/Common/Sql/TableHelper.php:143
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:312
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:314
#: src/wp-migrate-db-pro/class/Common/Sql/TableHelper.php:168
#: src/wp-migrate-db-pro/class/Common/Sql/TableHelper.php:149
msgid "our documentation"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:504
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:502
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:472
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:500
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:471
#, php-format
msgid "We could not find: %s. Are you sure this is the correct URL?"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:511
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:509
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:479
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:507
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:478
msgid ""
"It appears that you might be trying to pull from a local environment. This "
"will not work if <u>this</u> website happens to be located on a remote "
"server, it would be impossible for this server to contact your local "
"environment."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:513
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:511
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:481
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:509
#: src/wp-migrate-db-pro/class/Common/Http/RemotePost.php:480
msgid ""
"It appears that you might be trying to push to a local environment. This "
"will not work if <u>this</u> website happens to be located on a remote "
"server, it would be impossible for this server to contact your local "
"environment."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Http/WPMDBRestAPIServer.php:26
msgid "Only authenticated users can access endpoint."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:108
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:115
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:129
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:130
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:132
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:173
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:161
msgid "Unable to finalize the migration, migration state empty."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:316
#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:325
#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:332
#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:333
#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:369
#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:381
#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:393
#, php-format
msgid ""
"<p><strong>Export Failed</strong> — We can't save your export to the "
"following folder:<br><strong>%s</strong></p><p>Please adjust the permissions "
"on this folder. <a href=\"%s\" target=\"_blank\">See our documentation for "
"more information »</a></p>"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/MigrationState/MigrationStateManager.php:90
#: src/wp-migrate-db-pro/class/Common/MigrationState/MigrationStateManager.php:94
#: src/wp-migrate-db-pro/class/Common/MigrationState/MigrationStateManager.php:93
msgid "Failed to save migration state. Please contact support."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/MigrationState/MigrationStateManager.php:124
#: src/wp-migrate-db-pro/class/Common/MigrationState/MigrationStateManager.php:129
#: src/wp-migrate-db-pro/class/Common/MigrationState/MigrationStateManager.php:128
msgid "Failed to retrieve migration state. Please contact support."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:147
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:164
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:170
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:151
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:174
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:155
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:178
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:159
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:182
msgid "single site"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:148
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:163
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:169
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:152
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:173
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:156
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:177
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:160
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:181
msgid "multisite"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:155
msgid "Multisite tools addon"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:157
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:158
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:162
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:166
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:170
#, php-format
msgid ""
"It looks like the file you are trying to import is from a multisite install "
"and this install is a single site. To run this type of import you'll need to "
"use the %s to export a subsite as a single site. <a href=\"%s\" "
"target=\"_blank\">Learn more »</a>"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:165
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:171
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:175
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:179
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:183
#, php-format
msgid ""
"It looks like the file you are trying to import is from a single site "
"install and this install is a multisite. This type of migration isn't "
"currently supported. <a href=\"%s\" target=\"_blank\">Learn more »</a>"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:173
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:180
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:184
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:188
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:192
msgid "Activate"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:175
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:182
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:186
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:190
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:194
#, php-format
msgid ""
"It looks like the remote site is a %s install and this install is a %s. To "
"run this type of migration you'll need the %s activated on the "
"<strong>remote</strong> site."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:189
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:196
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:200
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:204
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:208
msgid "Install"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:192
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:199
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:203
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:207
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:211
msgid "Upgrade your license"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:198
#, php-format
msgid ""
"It looks like the remote site is a %s install and this install is a %s. To "
"run this type of migration you'll need the %s. %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:209
msgid "Multisite Tools Addon Needed"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:71
msgid ""
"The connection information appears to be missing, please enter it to "
"continue."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:72
msgid ""
"The URL on the first line appears to be invalid, please check it and try "
"again."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:73
msgid ""
"The secret key on the second line appears to be invalid. It should be a 40 "
"character string that consists of letters, numbers and special characters "
"only."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:74
msgid ""
"It appears you've entered the URL for this website, you need to provide the "
"URL of the remote website instead."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:75
msgid ""
"Looks like your remote secret key is the same as the secret key for this "
"site. To fix this, go to the <a href=\"#settings\">Settings tab</a> and "
"click \"Reset Secret Key\""
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:76
msgctxt "The migration has been cancelled"
msgid "Migration cancelled"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:77
msgid ""
"The migration has been stopped and all temporary files and data have been "
"cleaned up."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:78
msgid "Welcome to WP Migrate! &#127881;"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:79
#, php-format
msgid ""
"Hey, this is the first time activating your license, nice! Your migrations "
"are about to get awesome! If you haven’t already, you should check out our "
"<a href=\"%1$s\" target=\"_blank\">Quick Start Guide</a> and <a "
"href=\"%2$s\" target=\"_blank\">Videos</a>. If you run into any trouble at "
"all, use the <strong>Help tab</strong> above to submit a support request."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:80
msgid ""
"The selected file does not have a recognized file type. Please upload a "
"valid SQL file to continue."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:81
msgid "Please select an SQL export file above to continue."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:82
#, php-format
msgid "Importing data from %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:95
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:134
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:374
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:621
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:385
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:624
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:636
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:96
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:135
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:122
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:161
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:119
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:158
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:386
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:124
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:163
#: src/wp-migrate-db-pro/php-checker.php:37
#: src/wp-migrate-db-pro/php-checker.php:66
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:396
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:388
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:645
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:127
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:166
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:389
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:641
msgid "WP Migrate"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:176
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:184
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:185
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:187
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:178
#, php-format
msgid ""
"<strong>Uploads directory not writable</strong> &mdash; the following "
"directory is not writable: <code>%s</code>. Update the file permissions for "
"this folder to enable backups and export migrations. <a href=\"https://"
"deliciousbrains.com/wp-migrate-db-pro/doc/uploads-folder-permissions/\" "
"target=\"_blank\">More information</a>.<br><br>"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:264
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:275
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:276
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:277
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:278
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:270
msgid ""
"WP Migrate Lite and WP Migrate cannot both be active. We've automatically "
"deactivated WP Migrate Lite."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:266
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:277
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:278
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:286
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:280
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:279
msgid ""
"WP Migrate Lite and WP Migrate cannot both be active. We've automatically "
"deactivated WP Migrate."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:355
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:366
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:367
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:377
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:369
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:370
msgid "AJAX request failed"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:354
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:370
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:367
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:391
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:375
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:392
msgid "No recent migrations"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:380
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:413
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:447
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:658
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:670
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:260
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:259
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:401
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:439
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:478
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:705
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:717
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:268
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:393
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:426
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:460
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:671
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:683
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:422
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:499
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:726
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:738
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:434
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:468
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:679
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:691
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:222
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:423
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:461
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:500
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:727
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:739
msgid "Profile not found."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:393
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:416
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:406
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:437
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:414
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:438
msgid "Profile removed"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:427
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:468
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:455
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:501
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:440
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:481
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:476
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:522
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:448
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:489
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:477
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:523
msgid "Profile saved"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:510
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:550
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:523
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:571
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:531
#: src/wp-migrate-db-pro/class/Common/Profile/ProfileManager.php:572
#, php-format
msgid "Failed to %s profile, state data is empty."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Properties/Properties.php:70
msgid ""
"Invalid content verification signature, please verify the connection "
"information on the remote site and try again."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Properties/Properties.php:70
#, php-format
msgid " Remote URL: %s "
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:46
#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:55
msgid "Setting does not exist"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:730
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:737
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:740
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:742
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:835
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:871
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:872
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:744
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:969
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:970
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:971
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:965
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:981
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:980
#, php-format
msgid ""
"Failed to retrieve table structure for table '%s', please ensure your "
"database is online. (#125)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:803
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:810
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:813
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:815
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:912
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:948
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:949
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:817
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1046
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1047
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1048
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1042
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1058
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1057
#, php-format
msgid "Error creating temporary table. Table \"%s\" does not exist."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:854
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:861
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:864
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:866
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:967
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1003
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1004
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:868
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1101
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1102
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1103
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1097
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1113
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1112
#, php-format
msgid "Delete any existing table %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:865
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:872
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:875
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:877
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:978
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1014
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1015
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:879
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1112
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1113
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1114
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1108
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1124
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1123
#, php-format
msgid "Table structure of table %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:873
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:880
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:883
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:885
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:986
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1022
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1023
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:887
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1120
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1121
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1122
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1116
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1132
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1131
msgid ""
"Failed to generate the create table query, please ensure your database is "
"online. (#126)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:931
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:938
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:941
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:943
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1045
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1081
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1082
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:945
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1179
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1180
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1181
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1175
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1191
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1190
#, php-format
msgid "Data contents of table %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1057
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1064
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1065
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1068
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1070
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1176
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1212
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1213
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1072
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1310
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1311
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1312
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1306
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1322
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1321
msgid "Failed to write the gzipped SQL data to the file. (#127)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1064
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1071
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1072
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1075
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1077
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1183
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1219
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1220
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1079
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1317
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1318
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1319
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1313
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1329
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1328
msgid "Failed to write the SQL data to the file. (#128)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1136
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1143
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1144
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1147
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1149
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1267
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1303
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1304
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1151
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1400
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1401
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1406
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1418
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1420
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1421
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1396
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1402
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1412
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1411
#, php-format
msgid ""
"The imported table `%1s` contains characters which are invalid in the target "
"schema.<br><br>If this is a WP Migrate export file, ensure that the "
"`Compatible with older versions of MySQL` setting under `Advanced Options` "
"is unchecked and try exporting again.<br><br> See&nbsp;<a href=\"%2s\">our "
"documentation</a> for more information."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1139
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1146
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1147
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1150
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1152
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1277
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1313
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1314
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1154
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1410
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1411
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1416
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1428
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1430
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1431
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1406
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1412
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1422
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1421
#, php-format
msgid ""
"The table `%1s` contains characters which are invalid in the target "
"database. See&nbsp;<a href=\"%2s\" target=\"_blank\">our documentation</a> "
"for more information."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1777
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1789
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1790
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1799
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1802
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1804
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1958
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1994
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2000
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2001
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2002
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1806
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2109
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2116
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2117
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2118
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2130
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2132
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2146
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2148
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2149
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2112
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2140
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2142
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2143
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2179
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2193
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2200
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2199
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2208
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2246
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2256
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2255
#, php-format
msgid "End of data contents of table %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1902
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1914
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1915
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1924
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1927
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1929
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2110
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2146
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2152
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2153
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2154
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1931
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2267
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2274
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2275
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2276
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2288
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2290
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2304
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2306
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2307
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2270
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2298
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2300
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2301
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2337
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2351
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2358
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2357
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2366
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2404
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2414
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2413
msgid "WordPress MySQL database migration"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1904
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1916
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1917
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1926
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1929
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1931
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2112
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2148
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2154
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2155
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2156
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1933
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2269
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2276
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2277
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2278
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2290
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2292
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2306
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2308
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2309
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2272
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2300
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2302
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2303
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2339
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2353
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2360
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2359
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2368
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2406
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2416
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2415
#, php-format
msgid "Generated: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1905
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1917
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1918
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1927
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1930
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1932
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2113
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2149
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2155
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2156
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2157
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1934
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2270
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2277
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2278
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2279
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2291
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2293
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2307
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2309
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2310
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2273
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2301
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2303
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2304
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2340
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2354
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2361
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2360
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2369
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2407
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2417
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2416
#, php-format
msgid "Hostname: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1906
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1918
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1919
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1928
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1931
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1933
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2114
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2150
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2156
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2157
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2158
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1935
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2271
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2278
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2279
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2280
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2292
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2294
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2308
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2310
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2311
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2274
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2302
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2304
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2305
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2341
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2355
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2362
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2361
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2370
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2408
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2418
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2417
#, php-format
msgid "Database: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/TableHelper.php:140
#: src/wp-migrate-db-pro/class/Common/Sql/TableHelper.php:143
#: src/wp-migrate-db-pro/class/Common/Sql/TableHelper.php:168
#: src/wp-migrate-db-pro/class/Common/Sql/TableHelper.php:149
#, php-format
msgid ""
"The source site supports utf8mb4 data but the target does not, aborting "
"migration to avoid possible data corruption. Please see %1$s for more "
"information. (#148)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/UI/TemplateBase.php:80
msgctxt "Configure a migration or export"
msgid "Migrate"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/UI/TemplateBase.php:88
msgctxt "Plugin extensions"
msgid "Addons"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/UI/TemplateBase.php:92
msgctxt "Get help or contact support"
msgid "Help"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/UI/TemplateBase.php:237
msgid "Warning: Mixed Case Table Names"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/UI/TemplateBase.php:240
msgid ""
"Whoa! We've detected that your <b>local</b> site has the MySQL setting "
"<code>lower_case_table_names</code> set to <code>1</code>."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/UI/TemplateBase.php:242
msgid ""
"Whoa! We've detected that your <b>remote</b> site has the MySQL setting "
"<code>lower_case_table_names</code> set to <code>1</code>."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/UI/TemplateBase.php:245
msgid ""
"As a result, uppercase characters in table names will be converted to "
"lowercase during the migration."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/UI/TemplateBase.php:247
#, php-format
msgid ""
"You can read more about this in <a href=\"%s\">our documentation</a>, "
"proceed with caution."
msgstr ""

#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:80
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:542
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:545
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:47
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:557
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:49
#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:26
#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:27
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:51
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:561
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:53
#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:49
msgid "Migrate"
msgstr ""

#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:84
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:51
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:53
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:55
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:57
msgid "Upgrade"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Util/Util.php:240
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:253
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:261
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:289
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:255
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:294
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:290
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:295
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:256
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:259
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:296
#, php-format
msgid "Scope: %s()."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Util/Util.php:241
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:254
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:262
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:290
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:256
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:295
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:291
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:296
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:257
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:260
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:297
#, php-format
msgid "WPMDB Error: Data cannot be unserialized. %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:140
#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:65
#: src/wp-migrate-db-pro/class/Common/Addon/Addon.php:144
msgctxt "A new version of the plugin is available"
msgid "Update Available"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:141
#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:66
#: src/wp-migrate-db-pro/class/Common/Addon/Addon.php:145
#, php-format
msgid "A new version of %1$s is now available. %2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:141
#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:66
#: src/wp-migrate-db-pro/class/Common/Addon/Addon.php:145
msgctxt "Download and install a new version of the plugin"
msgid "Update Now"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:150
#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:75
#: src/wp-migrate-db-pro/class/Common/Addon/Addon.php:154
msgid "Beta Update Available"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:151
#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:76
#: src/wp-migrate-db-pro/class/Common/Addon/Addon.php:155
msgid "Update Available"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:153
#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:78
#: src/wp-migrate-db-pro/class/Common/Addon/Addon.php:157
#, php-format
msgid ""
"%1$s %2$s is now available. You currently have %3$s installed. <a "
"href=\"%4$s\">%5$s</a>"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:153
#: src/wp-migrate-db-pro/class/Pro/Addon/Addon.php:78
#: src/wp-migrate-db-pro/class/Common/Addon/Addon.php:157
msgid "Update Now"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Addon/AddonAbstract.php:117
#: src/wp-migrate-db-pro/class/Pro/Addon/AddonAbstract.php:122
#: src/wp-migrate-db-pro/class/Common/Addon/AddonAbstract.php:122
#, php-format
msgid ""
"The version of %1$s you have installed requires version %2$s of WP Migrate. "
"You currently have %3$s installed. <strong><a href=\"%4$s\">Update Now</a></"
"strong>"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Addon/AddonsFacade.php:114
#: src/wp-migrate-db-pro/class/Pro/Addon/AddonsFacade.php:115
#: src/wp-migrate-db-pro/class/Pro/Addon/AddonsFacade.php:121
#: src/wp-migrate-db-pro/class/Pro/Addon/AddonsFacade.php:122
#: src/wp-migrate-db-pro/class/Pro/Addon/AddonsFacade.php:123
#: src/wp-migrate-db-pro/class/Common/Addon/AddonsFacade.php:106
#: src/wp-migrate-db-pro/class/Pro/Addon/AddonsFacade.php:92
#: src/wp-migrate-db-pro/class/Common/Addon/AddonsFacade.php:123
msgid ""
"Legacy addons cannot be activated alongside WP Migrate version 2.3.0 or "
"above. These features have been moved to WP Migrate."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:81
#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:95
msgid "Backup not found."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:95
#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:117
msgid "Could not find backup file to download:"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:123
#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:147
#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:155
#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:186
#, php-format
msgid "File does not exist — %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:154
#: src/wp-migrate-db-pro/class/Pro/Backups/BackupsManager.php:195
#, php-format
msgid "Unable to delete file — %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Beta/BetaManager.php:116
#: src/wp-migrate-db-pro/class/Pro/Beta/BetaManager.php:124
msgid "Would you like to rollback WP Migrate to the latest stable release now?"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MF/MediaFilesAddon.php:102
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:182
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:186
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesAddon.php:96
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:184
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:185
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesAddon.php:93
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:161
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:163
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesAddon.php:88
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesAddon.php:89
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:162
msgid "Downloading files"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MF/MediaFilesAddon.php:103
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:181
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:185
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesAddon.php:97
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:183
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:184
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesAddon.php:94
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:160
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:162
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesAddon.php:89
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesAddon.php:90
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:161
msgid "Uploading files"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MF/MediaFilesLocal.php:190
#: src/wp-migrate-db-pro/class/Pro/MF/MediaFilesLocal.php:191
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesLocal.php:191
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesLocal.php:215
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesLocal.php:212
#: src/wp-migrate-db-pro/class/Common/MF/MediaFilesLocal.php:171
msgid "Invalid folder path supplied."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:424
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:425
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:426
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:427
msgid ""
"Please only enter letters, numbers or underscores for the new table prefix."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:892
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:893
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:894
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:898
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:905
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:899
msgid "Expected local subsite \"short_basedir\" missing from `state_data`."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:898
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:899
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:900
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:904
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:911
#: src/wp-migrate-db-pro/class/Pro/MST/MultisiteToolsAddon.php:905
msgid "Expected remote subsite \"short_basedir\" missing from `state_data`."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:229
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:232
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:244
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:248
#, php-format
msgid "There is a new version of %s available."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:231
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:234
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:246
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:250
#, php-format
msgid "View version %s details"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:241
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:244
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:256
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:260
#, php-format
msgid ""
"To update, go to %1$s and enter your license key. If you don't have a "
"license key, you may <a href=\"%2$s\">purchase one</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:243
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:246
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:258
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:262
#, php-format
msgid ""
"To finish activating %1$s, please go to %2$s and enter your license key. If "
"you don't have a license key, you may <a href=\"%3$s\">purchase one</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:246
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:584
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:249
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:587
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:261
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:599
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:265
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:603
msgid "Check my license again"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:310
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:313
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:325
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:329
#, php-format
msgid ""
"<strong>Addon No Longer Required</strong> - As of WP Migrate version 2.3.0, "
"this addon is <a href=\"%s\" target=\"_blank\">no longer required</a> and "
"can be safely deleted."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:344
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:355
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:347
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:358
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:359
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:370
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:363
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:374
msgid "Could not retrieve version details. Please try again."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:584
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:587
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:599
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:603
msgid "A problem occurred when trying to check the license, please try again."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Queue/Cron.php:86
#: src/wp-migrate-db-pro/class/Common/Queue/Cron.php:86
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:385
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:431
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:436
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:440
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:439
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:445
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:448
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:522
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:525
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:527
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:528
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:538
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:547
#, php-format
msgid "Every %d Minutes"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Queue/QueueHelper.php:61
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:60
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:58
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:62
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:71
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:68
msgid "File list empty or incomplete. Please contact support."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Queue/QueueHelper.php:182
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesLocal.php:172
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesLocal.php:178
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:181
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:179
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:178
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:175
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:209
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:199
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:241
#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:246
msgid "Error: empty folder list supplied."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/RemoteUpdates/RemoteUpdatesManager.php:114
#: src/wp-migrate-db-pro/class/Pro/RemoteUpdates/RemoteUpdatesManager.php:130
msgid "Please activate your license before updating."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:169
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:173
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:171
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:172
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:148
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:150
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:149
msgid "Themes"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:170
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:174
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:172
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:173
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:149
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:151
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:150
msgid "Plugins"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:171
msgid "Theme & Plugin Files"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:172
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:176
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:174
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:175
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:151
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:153
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:152
msgid "(active)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:173
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:177
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:175
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:176
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:152
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:154
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:153
msgid "Please select themes for migration."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:174
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:178
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:176
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:177
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:153
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:155
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:154
msgid "Please select plugins for migration."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:175
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:179
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:177
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:178
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:154
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:156
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:155
msgid "remote"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:176
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:180
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:178
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:179
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:155
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:157
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:156
msgid "local"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:177
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:181
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:179
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:180
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:156
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:158
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:157
msgid "Failed to transfer file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:178
msgid "Theme & Plugin Files Transfer Error"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:179
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:183
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:181
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:182
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:158
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:160
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:159
msgid "Loading transfer queue"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:180
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:184
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:182
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:183
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:159
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:161
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:160
msgid "Transferring: "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:126
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:142
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:139
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:138
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:155
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:157
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:151
#, php-format
msgid "Unable to determine folder name for theme %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:162
#, php-format
msgid ""
"Temporary file not found when finalizing Theme & Plugin Files migration: %s "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:170
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:220
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:217
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:216
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:241
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:252
#, php-format
msgid ""
"Unable to overwrite destination file when finalizing Theme & Plugin Files "
"migration: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:186
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:236
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:233
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:232
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:257
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:268
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:287
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:281
#, php-format
msgid ""
"Unable to move file into place when finalizing Theme & Plugin Files "
"migration. Source: %s | Destination: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:265
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:323
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:320
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:319
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:340
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:353
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:372
#, php-format
msgid "Unable to verify file migration, %s does not exist."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesLocal.php:188
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesLocal.php:194
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:194
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:191
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:220
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:216
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:215
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:175
msgid "Invalid folder list supplied (invalid array)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/TransferCheck.php:59
msgid "A problem occurred starting the Theme & Plugin files migration."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/TransferCheck.php:74
msgid ""
"Unfortunately it looks like we can't migrate your theme or plugin files. "
"However, running a migration without theme and plugin files should work. "
"Please uncheck the Theme Files checkbox, uncheck the Plugin Files checkbox, "
"and try your migration again."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/TransferCheck.php:76
#: src/wp-migrate-db-pro/class/Pro/TPF/TransferCheck.php:75
#: src/wp-migrate-db-pro/class/Common/TPF/TransferCheck.php:75
#: src/wp-migrate-db-pro/class/Common/TPF/TransferCheck.php:85
#: src/wp-migrate-db-pro/class/Common/TPF/TransferCheck.php:89
msgid "More Details »"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:103
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:104
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:105
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:107
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:113
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:123
#, php-format
msgid "Remote server responded with %s and body of %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:135
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:151
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:157
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:158
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:717
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:712
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:711
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:716
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:756
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:719
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:814
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:629
#, php-format
msgid "File transfer error - Unable to create a temporary folder. (%s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:149
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:165
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:171
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:172
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:731
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:726
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:725
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:730
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:770
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:733
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:828
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:643
#, php-format
msgid "File transfer error - Unable to create a PHP file on the server. (%s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:159
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:175
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:181
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:182
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:741
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:736
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:735
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:740
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:780
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:743
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:838
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:653
#, php-format
msgid ""
"File transfer error - Unable to update file contents using using PHP's "
"file_put_contents() function. (%s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:169
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:185
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:191
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:192
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:751
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:746
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:745
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:750
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:790
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:753
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:848
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:663
#, php-format
msgid ""
"File transfer error - Unable to move file to the correct location using "
"PHP's rename() function. (%s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:180
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:196
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:202
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:203
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:762
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:757
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:756
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:761
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:801
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:764
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:859
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:674
#, php-format
msgid ""
"File transfer error - Unable to delete file using PHP's unlink() function. "
"(%s)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:232
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:248
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:244
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:254
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:250
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:251
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:147
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:150
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:159
#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:171
msgid "Unable to process payload."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:56
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:75
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:85
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:87
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:88
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:83
msgid "$_POST['batch'] is empty."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:63
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:82
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:92
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:94
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:95
#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:90
msgid "Request for batch of files failed."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:78
#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:73
#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:60
#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:52
msgctxt "Get backups"
msgid "Backups"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:129
#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:120
#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:106
#, php-format
msgid ""
"The version of the %1$s addon you have installed%2$s is out-of-date and will "
"not work with this beta version WP Migrate. There may be a <a "
"href=\"%3$s\">beta update available</a>, otherwise please <a "
"href=\"%4$s\">deactivate this addon</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:132
#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:123
#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:109
#, php-format
msgid ""
"The version of the %1$s addon you have installed%2$s is out-of-date and will "
"not work with this version WP Migrate. <a href=\"%3$s\">Update Now</a>"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:227
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:236
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:190
msgid "Profile ID not found."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:249
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:258
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:212
msgid ""
"There is more than one profile with that name, please use the profile ID "
"instead. See wp migratedb profiles for help."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:272
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:271
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:280
msgid "Verifying connection..."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:359
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:358
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:366
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:291
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:292
msgid "URL and secret-key are required"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:434
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:433
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:441
msgid "Invalid backup option or non-existent table selected for backup."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:616
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:615
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:622
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:509
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:514
msgid "Please update WP Migrate."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:637
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:636
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:643
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:530
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:535
msgid "Profile ID missing."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:691
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:690
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:697
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:584
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:589
msgid ""
"This profile is from an older version of WP Migrate and some settings have "
"changed."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:694
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:693
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:700
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:587
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:592
#, php-format
msgid "Please visit %s to update the profile."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:723
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:722
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:729
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:646
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:651
msgid ""
"The profile is set to migrate media files, however migrating media files is "
"not supported with the current license."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:746
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:745
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:752
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:669
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:674
msgid ""
"The profile is set to migrate media files, however migrating media files is "
"not supported with the current license of the remote site."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:779
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:778
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:785
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:702
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:707
msgid ""
"The profile is set to migrate between a single site and a multisite, however "
"this type of multisite migration is not supported with the current license."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:819
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:818
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:825
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:742
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:747
msgid ""
"The profile is set to migrate a subsite, however subsite migrations are not "
"supported with the current license of the remote site."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:828
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:209
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:218
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:247
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:211
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:220
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:249
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:827
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:834
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:216
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:225
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:254
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:217
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:226
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:255
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:212
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:221
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:250
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:751
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:214
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:223
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:252
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:756
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:227
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:256
msgid ""
"A valid Blog ID or Subsite URL must be supplied to make use of the subsite "
"option"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:833
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:832
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:839
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:756
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:761
msgid ""
"The profile is set to perform a subsite to subsite migration, however the "
"remote website is a single site installation."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:838
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:837
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:844
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:761
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:766
msgid ""
"A valid Blog ID or Subsite URL must be supplied to subsite-destination to "
"make use of the subsite option"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:887
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:895
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:894
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:901
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:818
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:823
msgid ""
"The profile is set to migrate theme or plugin files, however migrating theme "
"and plugin files is not supported with the current license."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:910
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:918
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:917
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:926
msgctxt "The caches and rewrite rules for the target are being flushed"
msgid "Flushing caches and rewrite rules..."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:967
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:975
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:974
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:981
msgid "Migrating tables"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:1043
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:1051
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:1050
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:1057
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:954
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:959
msgid "Importing file"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:173
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:188
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:191
msgid "You must provide an import file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:565
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:599
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:621
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:624
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:630
msgid "There are no saved profiles for this site."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:581
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:615
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:637
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:640
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:646
msgctxt "Export data to remote database"
msgid "push"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:582
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:616
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:638
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:641
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:647
msgctxt "Import data from remote database"
msgid "pull"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:583
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:617
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:639
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:642
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:648
msgctxt "Export file from local database"
msgid "export"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:584
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:618
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:640
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:643
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:649
msgctxt "Run a find & replace on local database"
msgid "find & replace"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:585
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:619
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:641
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:644
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:650
msgctxt "Import data from SQL file"
msgid "import"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:586
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:620
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:642
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:645
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:651
msgctxt "Backup the local database"
msgid "backup"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:604
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:638
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:660
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:663
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:669
msgctxt "Profile list column heading"
msgid "ID"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:605
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:639
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:661
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:664
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:670
msgctxt "Profile list column heading"
msgid "NAME"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:606
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:640
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:662
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:665
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:671
msgctxt "Profile list column heading"
msgid "ACTION"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:607
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:641
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:663
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:666
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:672
msgctxt "Profile list column heading"
msgid "REMOTE"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:659
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:693
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Command.php:733
msgid "Migration successful."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:107
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:112
#, php-format
msgid "Invalid action parameter - `%s`"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:113
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:118
#, php-format
msgid "Invalid setting parameter - `%s`"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:122
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:127
msgid "Please pass a value to update."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:128
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:133
msgid "Invalid parameter for push/push settings. Value must be `on` or `off`."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:137
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:142
#, php-format
msgid "%s setting updated."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:139
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:144
msgid "Setting unchanged."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:142
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:147
msgid "The connection-key cannot be set via the CLI."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:153
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:158
#, php-format
msgid "Too many positional arguments: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:171
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:176
#, php-format
msgid "No setting `%s` currently saved in the database."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:195
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:200
msgid "License updated."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:231
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:236
msgid "License requires specifying a user via --user=<id|login|email>"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:234
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:239
msgid "User must be an Admin"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:280
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:285
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Setting.php:282
msgid "Checking license key..."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:148
#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:150
#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:124
msgid "--media must be set to an acceptable value, see: wp help migratedb "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:159
#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:161
#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:135
msgid ""
"--media-date required when using --media=since-date, see: wp help migratedb "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:173
#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:175
#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:149
msgid ""
"--media-date parameter received an invalid date format, see wp help "
"migratedb "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:239
#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:241
msgid "Initiating media migration..."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:310
#: src/wp-migrate-db-pro/class/Pro/MF/CliCommand/MediaFilesCli.php:312
msgid ""
"WP Migrate Media Files does not seem to be installed/active on the remote "
"website."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:206
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:208
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:213
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:214
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:209
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:211
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:215
msgid ""
"The installation must be a Multisite network to make use of the export "
"subsite option"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:213
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:215
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:220
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:221
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:216
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:218
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:222
msgid ""
"For subsite to subsite migrations subsite-source and subsite-destination are "
"both required"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:230
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:232
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:237
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:238
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:233
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:235
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:239
msgid ""
"subsite-source must also be used to make use of the subsite to subsite option"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:234
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:236
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:241
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:242
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:237
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:239
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:243
msgid ""
"subsite-destination must also be used to make use of the subsite to subsite "
"option"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:238
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:240
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:245
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:246
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:241
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:243
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:247
msgid ""
"A valid Blog ID or Subsite URL must be supplied for both networks to make "
"use of the subsite to subsite option"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:242
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:244
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:249
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:250
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:245
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:247
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:251
msgid ""
"Both source and destination must be networks to make use of the subsite to "
"subsite option"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:260
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:262
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:267
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:268
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:263
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:265
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:269
msgid "A new table name prefix may only be specified for subsite exports."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:263
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:265
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:270
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:271
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:266
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:268
#: src/wp-migrate-db-pro/class/Pro/MST/CliCommand/MultisiteToolsAddonCli.php:272
msgid "A valid prefix must be supplied to make use of the prefix option"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:114
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:129
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:133
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:128
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:132
msgid ""
"Please activate your license before attempting a pull or push migration."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:149
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:151
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:164
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:168
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:163
msgid ""
"Failed attempting to unserialize the response from the remote server. Please "
"contact support."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:198
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:181
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:185
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:180
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:178
#, php-format
msgid ""
"<b>Version Mismatch</b> &mdash; We've detected you have version %1$s of WP "
"Migrate at %2$s but are using an outdated version here. Please go to the "
"Plugins page on both installs and check for updates."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:201
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:191
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:195
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:190
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:188
#, php-format
msgid ""
"<b>Version Mismatch</b> &mdash; We've detected you have version %1$s of WP "
"Migrate at %2$s but are using %3$s here. (#196)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:203
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:201
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:205
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:200
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:198
#, php-format
msgid ""
"<b>Version Mismatch</b> &mdash; We've detected you have version %1$s of WP "
"Migrate at %2$s but are using %3$s here. Please go to the Plugins page on "
"both installs and check for updates."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:232
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:242
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:246
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:241
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:239
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:243
#, php-format
msgid ""
"<b>Activate Remote License</b> &mdash; Looks like you don't have a WP "
"Migrate license active at %s (#195)."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:243
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:260
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:264
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:259
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:257
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:261
msgid ""
"The connection succeeded but the remote site is configured to reject pull "
"connections. You can change this in the \"settings\" tab on the remote site. "
"(#122)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:245
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:265
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:269
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:264
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:262
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:266
msgid ""
"The connection succeeded but the remote site is configured to reject push "
"connections. You can change this in the \"settings\" tab on the remote site. "
"(#122)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:384
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:388
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:389
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:385
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:415
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:419
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:414
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:412
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:421
msgid ""
"The connection succeeded but the remote site is configured to reject pull "
"connections. You can change this in the \"settings\" tab on the remote site. "
"(#110)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:386
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:390
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:391
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:387
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:418
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:422
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:417
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:415
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:424
msgid ""
"The connection succeeded but the remote site is configured to reject push "
"connections. You can change this in the \"settings\" tab on the remote site. "
"(#110)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:174
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:205
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:226
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:211
msgid ""
"The connection succeeded but the remote site is configured to reject pull "
"connections. You can change this in the \"settings\" tab on the remote site. "
"(#141)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:235
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:286
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:287
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:295
msgid "Could not upload the SQL to the server. (#135)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:241
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:295
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:293
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:304
msgid "Could not read the SQL file we uploaded to the server. (#136)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:259
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:317
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:311
#: src/wp-migrate-db-pro/class/Pro/Migration/Tables/Remote.php:328
msgid ""
"The connection succeeded but the remote site is configured to reject push "
"connections. You can change this in the \"settings\" tab on the remote site. "
"(#139)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:291
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:347
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:353
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:351
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:663
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:355
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:665
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:666
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:668
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:730
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:582
#, php-format
msgid "Theme not found on source server: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:370
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:426
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:438
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:436
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:437
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:751
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:441
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:754
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:755
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:757
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:819
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:671
#, php-format
msgid "Plugin not found on source server: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:426
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:628
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:640
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:638
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:657
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:226
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:661
msgid "Initiating themes migration..."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:433
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:656
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:658
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:670
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:668
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:687
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:256
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:691
msgid "Initiating plugins migration..."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/FileProcessor.php:328
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:328
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:329
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:333
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:336
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:340
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:371
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:365
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:369
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:390
#, php-format
msgid "File %s does not exist"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:73
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:74
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:76
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:82
#, php-format
msgid "File does not exist - %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:328
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:322
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:344
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:348
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:349
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:369
#, php-format
msgid ""
"File size of source and destination do not match: <br>%s<br>Destination "
"size: %s, Local size: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:335
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:329
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:351
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:355
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:356
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:383
#, php-format
msgid ""
"File MD5's do not match for file: %s \n"
"Local MD5: %s Remote MD5: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:355
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:349
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:371
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:375
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:376
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:409
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:525
#, php-format
msgid "Could not write line to file. File name: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:401
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:391
msgid "Failed to unpack payload."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:407
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:397
msgid "Failed to create stream from payload."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:428
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:418
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:420
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:424
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:425
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:458
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:575
#, php-format
msgid "Could not create directory: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:445
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:435
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:437
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:441
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:442
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:475
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:592
#, php-format
msgid ""
"The `%s` file is not writable, please check the file permissions of the "
"parent folder and ensure the web server can read from and write to this file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:490
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:480
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:482
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:486
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:487
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:309
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:527
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:407
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:660
#, php-format
msgid "Unable to rename part file %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:164
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:165
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:153
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:150
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:154
msgid "Could not validate $_POST data."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:242
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:250
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:251
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:239
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:236
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:275
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:280
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:274
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:279
msgid "Saving queue status to remote failed."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:337
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:349
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:355
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:356
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:100
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:120
msgid "Failed to respond to payload post."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:132
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:136
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:141
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:144
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:145
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:180
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:171
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:174
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:128
#, php-format
msgid "Payload transfer failed with code %s: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:268
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:277
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:277
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:272
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:289
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:297
#, php-format
msgid "The following files failed to transfer: <br> %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:308
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:317
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:317
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:312
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:314
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:331
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:342
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:313
#, php-format
msgid "Unable to create folder for file transfers: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:315
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:324
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:324
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:319
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:321
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:338
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:354
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:325
#, php-format
msgid ""
"Unable to create the transfer manifest file. Verify the web server can write "
"to this file/folder: `%s`"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:607
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:613
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:613
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:608
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:607
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:612
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:652
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:615
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:710
msgid "Failed to load manifest file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:613
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Util.php:619
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:619
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:614
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:613
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:618
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:658
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:621
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:716
msgid "Failed to parse manifest file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:175
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:173
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:174
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:150
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:152
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:151
msgid "Themes & Plugins"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesAddon.php:182
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:180
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:181
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:157
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:159
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:158
msgid "Themes & Plugins Transfer Error"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:212
#: src/wp-migrate-db-pro/class/Pro/TPF/ThemePluginFilesFinalize.php:209
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:208
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:233
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:237
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:231
#, php-format
msgid "Temporary file not found when finalizing %s Files migration: %s "
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/TransferCheck.php:59
#: src/wp-migrate-db-pro/class/Pro/TPF/TransferCheck.php:58
#: src/wp-migrate-db-pro/class/Common/TPF/TransferCheck.php:58
#: src/wp-migrate-db-pro/class/Common/TPF/TransferCheck.php:65
#: src/wp-migrate-db-pro/class/Common/TPF/TransferCheck.php:69
msgid "A problem occurred starting the Themes & Plugins migration."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/TransferCheck.php:74
#: src/wp-migrate-db-pro/class/Pro/TPF/TransferCheck.php:73
#: src/wp-migrate-db-pro/class/Common/TPF/TransferCheck.php:73
#: src/wp-migrate-db-pro/class/Common/TPF/TransferCheck.php:81
#: src/wp-migrate-db-pro/class/Common/TPF/TransferCheck.php:85
msgid ""
"Unfortunately it looks like we can't migrate your themes or plugins. "
"However, running a migration without themes and plugins should work. Please "
"uncheck the Themes checkbox, uncheck the Plugins checkbox, and try your "
"migration again."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:485
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:497
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:495
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:514
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:830
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:518
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:813
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:814
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:816
#, php-format
msgid "Must-Use Plugin not found on source server: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:545
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:557
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:555
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:574
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:889
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:578
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:872
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:873
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:875
#, php-format
msgid "Other file not found on source server: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:636
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:638
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:650
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:648
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:667
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:236
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:671
msgid "Initiating other files migration..."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:646
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:648
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:660
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:658
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:677
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:246
#: src/wp-migrate-db-pro/class/Pro/TPF/Cli/ThemePluginFilesCli.php:681
msgid "Initiating must-use files migration..."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:259
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:260
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:263
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:341
#, php-format
msgid "Could not copy stream data to file. File name: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:334
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/PluginHelper.php:335
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:79
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:94
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/PluginHelper.php:99
msgid "Failed to respond to payload post, empty state data."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:267
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:268
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:271
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:351
#, php-format
msgid "Could not determine payload filesize. File name: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:28
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:30
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:31
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:34
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:44
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:45
msgid "Could not create ZIP Archive"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:56
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:58
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:64
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:66
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:67
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:71
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:86
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:87
#, php-format
msgid "Could not add %s to ZIP Archive"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1704
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1707
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1709
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1856
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1892
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1898
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1899
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1900
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1711
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2006
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2014
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2015
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2020
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2032
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2034
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2035
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2010
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2016
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2038
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2040
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2041
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2077
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2091
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2098
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2097
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2106
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2144
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2154
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2153
msgid "Error moving SQL file into ZIP archive"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:171
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:175
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:189
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:174
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:176
#, php-format
msgid " ZIP Archive %s does not exist"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:176
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:180
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:194
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:179
#: src/wp-migrate-db-pro/class/Common/FullSite/FullSiteExport.php:181
#, php-format
msgid " ZIP Archive %s could not be deleted"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:504
#: src/wp-migrate-db-pro/class/Pro/License.php:564
#: src/wp-migrate-db-pro/class/Pro/License.php:508
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:538
#: src/wp-migrate-db-pro/class/Pro/License.php:565
#: src/wp-migrate-db-pro/class/Pro/License.php:558
#, php-format
msgid ""
"<strong>License Cancelled</strong> &mdash; The license key has been "
"cancelled. Please <a href=\"%1$s\">remove it and enter a valid license key</"
"a>. <br /><br /> Your license key can be found in <a href=\"%2$s\" "
"target=\"_blank\">My Account</a>. If you don't have an account yet, <a "
"href=\"%3$s\" target=\"_blank\">purchase a new license</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:506
#: src/wp-migrate-db-pro/class/Pro/License.php:566
#: src/wp-migrate-db-pro/class/Pro/License.php:510
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:540
#: src/wp-migrate-db-pro/class/Pro/License.php:567
#: src/wp-migrate-db-pro/class/Pro/License.php:560
#, php-format
msgid ""
"<strong>License Cancelled</strong> &mdash; The license key below has been "
"cancelled. Please remove it and enter a valid license key. <br /><br /> Your "
"license key can be found in <a href=\"%1$s\" target=\"_blank\">My Account</"
"a>. If you don't have an account yet, <a href=\"%2$s\" "
"target=\"_blank\">purchase a new license</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:536
#: src/wp-migrate-db-pro/class/Pro/License.php:537
#: src/wp-migrate-db-pro/class/Pro/License.php:596
#: src/wp-migrate-db-pro/class/Pro/License.php:597
#: src/wp-migrate-db-pro/class/Pro/License.php:540
#: src/wp-migrate-db-pro/class/Pro/License.php:541
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:570
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:571
#: src/wp-migrate-db-pro/class/Pro/License.php:598
#: src/wp-migrate-db-pro/class/Pro/License.php:590
#: src/wp-migrate-db-pro/class/Pro/License.php:591
msgid "License Inactive"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:536
#: src/wp-migrate-db-pro/class/Pro/License.php:537
#: src/wp-migrate-db-pro/class/Pro/License.php:596
#: src/wp-migrate-db-pro/class/Pro/License.php:597
#: src/wp-migrate-db-pro/class/Pro/License.php:540
#: src/wp-migrate-db-pro/class/Pro/License.php:541
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:570
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:571
#: src/wp-migrate-db-pro/class/Pro/License.php:598
#: src/wp-migrate-db-pro/class/Pro/License.php:590
#: src/wp-migrate-db-pro/class/Pro/License.php:591
msgid ""
"The license was deactivated after 30 days of not using WP Migrate. "
"Reactivate to access plugin updates, support, and premium features."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/License.php:537
#: src/wp-migrate-db-pro/class/Pro/License.php:597
#: src/wp-migrate-db-pro/class/Pro/License.php:541
#: src/wp-migrate-db-pro/class/SiteMigration/License.php:571
#: src/wp-migrate-db-pro/class/Pro/License.php:598
#: src/wp-migrate-db-pro/class/Pro/License.php:591
msgid "Reactivate license"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:156
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:160
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:164
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:168
msgid "Multisite Tools upgrade"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:164
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:168
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:172
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:176
msgid "Learn more about replacing a single site with a multisite network"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:165
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:169
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:173
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:177
msgid "Learn more about replacing a multisite network with a single site"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:215
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:219
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:223
#: src/wp-migrate-db-pro/class/Common/Multisite/Multisite.php:227
#, php-format
msgid ""
"It looks like the remote site is a %s install and this install is a %s. %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:156
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:157
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:183
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:184
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:178
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:188
msgid "WP Engine Migration"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:120
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:125
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:130
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:134
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:208
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:210
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:212
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:213
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:216
msgid "The action to perform, one of \"start\", \"cancel\", \"pause_resume\"."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:128
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:133
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:138
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:142
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:216
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:218
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:220
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:221
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:224
msgid ""
"The type of migration to perform the action on, e.g. \"export\", "
"\"find_replace\" etc."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:136
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:141
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:146
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:150
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:224
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:226
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:228
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:229
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:232
msgid ""
"The ID of the current migration, only required when starting a migration."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:191
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:198
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:203
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:207
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:281
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:283
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:285
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:286
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:296
msgid "Action not supplied."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:202
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:209
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:214
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:218
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:292
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:294
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:296
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:297
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:307
msgid "Type not supplied."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:312
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:358
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:363
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:367
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:366
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:370
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:444
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:446
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:448
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:449
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:459
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:468
#, php-format
msgid "Invalid migration type \"%s\" supplied."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:322
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:368
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:373
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:377
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:376
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:380
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:454
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:456
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:458
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:459
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:469
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:478
msgid "Invalid action supplied."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:344
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:267
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:390
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:272
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:395
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:276
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:399
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:398
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:402
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:350
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:476
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:352
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:478
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:354
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:480
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:355
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:481
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:491
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:365
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:500
msgid "Migration ID not supplied."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:352
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:398
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:403
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:407
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:406
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:410
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:484
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:486
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:488
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:489
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:499
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:508
msgid "Data for Migration ID could not be loaded."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:383
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:429
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:434
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:438
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:437
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:443
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:446
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:520
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:523
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:525
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:526
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:536
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:545
msgid "Every Minute"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:198
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:204
msgid "Unexpected data set for current migration state."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:296
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:199
#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:281
msgid "Could not find generated migration ID."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Cli/Cli.php:305
msgid "Could not load current migration"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:801
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:802
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:812
#: src/wp-migrate-db-pro/class/Common/Filesystem/Filesystem.php:806
#, php-format
msgid "<h3>Could not get filename to download. </h3> %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:242
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:243
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:247
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:245
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:291
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:279
#, php-format
msgid "Table \"%s\" missing."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/MigrationState/ApplicationStateController.php:59
msgid "Array of application state branches."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:948
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:931
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:932
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesAddon.php:934
#, php-format
msgid "Core file not found on source server: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:456
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:453
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:449
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:462
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:314
msgid "Missing current migration details."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:462
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:459
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:455
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:468
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:320
msgid "Missing theme and plugin details."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:470
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:467
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:463
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:476
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:328
msgid "Missing remote site details."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:551
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:548
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:544
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:573
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:425
#, php-format
msgid "Invalid stage \"%s\" supplied to file transfer initialization."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Command.php:238
#: src/wp-migrate-db-pro/class/Pro/Cli/Command.php:236
msgid "WP Migrate CLI Settings class not available."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:317
#, php-format
msgid "Could not verify file is from payload. File name: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:141
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:144
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:167
#, php-format
msgid "Payload transfer failed with code %1$s: %2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:441
#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:453
#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:465
#, php-format
msgid "Unexpected enqueuing result returned for %s stage."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:652
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:653
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:675
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:678
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:656
#, php-format
msgid "Unexpected processing result returned for %s stage."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:160
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:165
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:169
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:243
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:245
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:247
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:248
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:251
msgid "The ID of the migration to be dismissed."
msgstr ""

#: src/wp-migrate-db-pro/php-checker.php:26
#, php-format
msgid ""
"%s requires PHP version %s or higher and cannot be activated. You are "
"currently running version %s. <a href=\"%s\">Learn&nbsp;More&nbsp;»</a>"
msgstr ""

#: src/wp-migrate-db-pro/php-checker.php:33
#: src/wp-migrate-db-pro/php-checker.php:66
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:68
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:70
msgid "WP Migrate Lite"
msgstr ""

#: src/wp-migrate-db-pro/php-checker.php:67
#, php-format
msgid ""
"requires PHP version %s or higher to run and has been deactivated. You are "
"currently running version %s. <a href=\"%s\">Learn More »</a>"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:188
#: src/wp-migrate-db-pro/class/Common/Plugin/Menu.php:191
msgid "Site Migration"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:280
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:273
msgid ""
"WP Engine Site Migration is powered by WP Migrate and shares some of the "
"same functionality. In order to prevent conflicts between the two plugins, "
"WP Migrate has been deactivated."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:283
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:276
msgid ""
"WP Engine Site Migration is powered by WP Migrate and shares some of the "
"same functionality. In order to prevent conflicts between the two plugins, "
"WP Engine Site Migration has been deactivated."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:277
#: src/wp-migrate-db-pro/class/Common/Migration/InitiateMigration.php:291
msgid "Invalid response from remote site."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:74
msgid ""
"It appears you've entered connection information for this site. Instead you "
"need to provide connection information from the Settings tab of the remote "
"site to which you are trying to connect."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Alerts/Email/EmailAlert.php:141
#: src/wp-migrate-db-pro/class/Common/Alerts/Email/EmailAlert.php:143
msgid "🚀 Site migration started"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Alerts/Email/EmailAlert.php:155
#: src/wp-migrate-db-pro/class/Common/Alerts/Email/EmailAlert.php:157
msgid "✅ Site migration completed"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Alerts/Email/EmailAlert.php:169
#: src/wp-migrate-db-pro/class/Common/Alerts/Email/EmailAlert.php:171
msgid "🚨 Site migration failed"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Alerts/Email/EmailAlert.php:194
#: src/wp-migrate-db-pro/class/Common/Alerts/Email/EmailAlert.php:196
msgid "WP Engine Site Migration"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:491
#: src/wp-migrate-db-pro/class/Common/Plugin/PluginManagerBase.php:492
#, php-format
msgid "%1$s is developed and maintained by %2$s."
msgstr ""

#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:157
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:750
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:159
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:752
#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:163
msgid "Documentation"
msgstr ""

#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:160
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:753
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:162
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:755
#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:168
msgid "Support"
msgstr ""

#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:167
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:760
#: src/wp-migrate-db-pro/class/Free/Plugin/PluginManager.php:169
#: src/wp-migrate-db-pro/class/Pro/Plugin/ProPluginManager.php:762
#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:173
msgid "Feedback"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Queue/Connection.php:72
msgid "An invalid item was found in the queue of files to be transfered."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:151
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:236
#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:244
msgid ""
"Failed attempting to decode the response from the remote server. Please "
"contact support."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Queue/Connection.php:72
#: src/wp-migrate-db-pro/class/Common/Queue/Connection.php:78
msgid "An invalid item was found in the queue of files to be transferred."
msgstr ""

#: src/wp-migrate-db-pro/requirements-checker.php:123
#, php-format
msgid ""
"%s requires PHP version %s or higher to run and has been deactivated. You "
"are currently running version %s."
msgstr ""

#: src/wp-migrate-db-pro/requirements-checker.php:137
#, php-format
msgid ""
"%s requires WordPress version %s or higher to run and has been deactivated. "
"You are currently running version %s."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackupExport.php:225
#, php-format
msgid ""
"Could not add table \"%1$s\" to queue for backup.<br>Database Error: %2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:866
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:867
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:881
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:883
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:884
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:894
#: src/wp-migrate-db-pro/class/Common/BackgroundMigration/BackgroundMigrationManager.php:905
#, php-format
msgid "The migration was terminated after %d minutes without progress."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:234
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:487
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:646
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:244
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:497
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:664
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:252
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:505
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:672
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:253
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:504
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:671
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:257
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:516
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:683
#, php-format
msgid "Scan manifest file has unexpected format at \"%s\"."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:296
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:306
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:314
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:315
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:319
#, php-format
msgid "Could not read directory \"%s\"."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:543
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:553
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:561
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:560
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:572
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:910
#, php-format
msgid "Scan manifest file could not be opened at \"%s\"."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:553
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:563
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:571
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:570
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:582
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:920
#, php-format
msgid "Scan manifest file is empty at \"%s\"."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:575
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:590
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:598
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:597
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:609
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:860
#, php-format
msgid "Scan manifest file could not be saved at \"%s\""
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:122
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:110
msgid "Could not find temporary file path for exported data."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:129
#: src/wp-migrate-db-pro/class/Common/Migration/FinalizeMigration.php:117
msgid "Could not find temporary file with exported data."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:492
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:495
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:473
msgid "Could not cleanly cancel due to state data missing stage."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:76
msgctxt "The migration has been canceled"
msgid "Migration canceled"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Plugin/Assets.php:77
msgid ""
"The migration has been canceled. Any temporary database tables or files "
"created by the migration have been cleaned up."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Properties/Properties.php:70
msgid ""
"The connection information could not be authenticated because it is "
"incorrect or expired. Copy new connection information from the Settings tab "
"of the remote site and try again."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:197
msgid "Never"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:198
#: src/wp-migrate-db-pro/class/SiteMigration/Settings/Settings.php:137
msgid "2 hours"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:199
#: src/wp-migrate-db-pro/class/SiteMigration/Settings/Settings.php:138
msgid "8 hours"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:200
#: src/wp-migrate-db-pro/class/SiteMigration/Settings/Settings.php:139
msgid "1 day"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:201
#: src/wp-migrate-db-pro/class/SiteMigration/Settings/Settings.php:140
msgid "3 days"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:202
#: src/wp-migrate-db-pro/class/SiteMigration/Settings/Settings.php:141
msgid "5 days"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:203
#: src/wp-migrate-db-pro/class/SiteMigration/Settings/Settings.php:142
msgid "1 week"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:204
msgid "2 weeks"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:205
msgid "4 weeks"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:206
msgid "90 days"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:207
msgid "180 days"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Settings/Settings.php:208
msgid "365 days"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2194
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2201
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2202
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2203
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2215
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2217
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2231
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2233
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2234
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2197
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2225
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2227
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2228
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2264
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2278
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2285
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2284
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2293
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2331
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2341
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2340
msgid ""
"Temporary table prefix not supplied when trying to delete temporary tables."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2456
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2463
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2464
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2465
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2477
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2479
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2493
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2495
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2496
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2459
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2498
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2499
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2500
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2536
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2540
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2547
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2546
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2555
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2593
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2603
#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2602
#, php-format
msgid "Could not add table \"%1$s\" to queue.<br>Database Error: %2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Queue/Manager.php:242
#, php-format
msgid "Could not create queue jobs table, error: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Queue/Manager.php:263
#, php-format
msgid "Could not create queue failures table, error: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:653
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesLocal.php:505
#, php-format
msgid "File not found on source server: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Util/Util.php:1696
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:1697
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:1698
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:1701
msgid "(opens in a new tab)"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Util/Util.php:1720
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:1733
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:1734
#: src/wp-migrate-db-pro/class/Common/Util/Util.php:1737
msgid "Unknown"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Receiver.php:60
msgid "Unable to create payload temporary file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Sender.php:63
msgid "Unable to JSON encode state data for payload transport."
msgstr ""

#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:80
#, php-format
msgid ""
"It looks like you're trying to migrate a multisite to a single site, which "
"isn't currently supported by this plugin. To continue, <a href=\"%s\" "
"target=\"_blank\">convert the destination to a multisite</a> and try again."
msgstr ""

#: src/wp-migrate-db-pro/class/SiteMigration/Plugin/PluginManager.php:97
#, php-format
msgid ""
"It looks like you're trying to migrate a single site to a multisite, which "
"isn't currently supported by this plugin. To continue, <a href=\"%s\" "
"target=\"_blank\">convert the destination to a single site</a> and try again."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/UI/Template.php:105
#, php-format
msgid ""
"We've detected that <code>WP_HTTP_BLOCK_EXTERNAL</code> is enabled, which "
"will prevent WP Migrate from functioning properly. You should either disable "
"<code>WP_HTTP_BLOCK_EXTERNAL</code> or add any sites that you'd like to "
"migrate to or from with WP Migrate to <code>WP_ACCESSIBLE_HOSTS</code> (api."
"deliciousbrains.com must be added to <code>WP_ACCESSIBLE_HOSTS</code> for "
"the API to work). More information on this can be found <a href=\"%s\" "
"target=\"_blank\">here</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/SiteMigration/Settings/Settings.php:102
#, php-format
msgid ""
"We've detected that <code>WP_HTTP_BLOCK_EXTERNAL</code> is enabled, which "
"will prevent WP Engine Site Migration from functioning properly. You should "
"either disable <code>WP_HTTP_BLOCK_EXTERNAL</code> or add any sites that "
"you'd like to migrate to <code>WP_ACCESSIBLE_HOSTS</code>. More information "
"on this can be found <a href=\"%s\" target=\"_blank\">here</a>."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Chunker.php:66
#, php-format
msgid "Unable to open chunk file: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Chunker.php:72
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:599
#, php-format
msgid "Unable to open file: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:459
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:430
#, php-format
msgid "File does not exist: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:488
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:459
#, php-format
msgid "Could not add file \"%1$s\" to queue.<br>Database Error: %2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:376
#: src/wp-migrate-db-pro/class/Pro/Cli/Extra/Cli.php:381
msgid "missing-migration-id"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:179
#, php-format
msgid ""
"<b>Version Mismatch</b> &mdash; We've detected you have version %1$s of %2$s "
"at %3$s but are using an outdated version here. Please go to the Plugins "
"page on both installs and check for updates."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:190
#, php-format
msgid ""
"<b>Version Mismatch</b> &mdash; We've detected you have version %1$s of %2$s "
"at %3$s but are using %4$s here. (#196)"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Remote.php:201
#, php-format
msgid ""
"<b>Version Mismatch</b> &mdash; We've detected you have version %1$s of %2$s "
"at %3$s but are using %4$s here. Please go to the Plugins page on both "
"installs and check for updates."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:104
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:120
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:222
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:623
msgid "Unable to write to payload file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:110
#, php-format
msgid "Unable to open file %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:149
msgid "Unable to create payload file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:394
#, php-format
msgid "Unable to rename part file %1$s<br>%2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:418
#, php-format
msgid "Could not verify file is from payload. File name: %1$s<br>%2$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:476
#, php-format
msgid ""
"File size of source and destination do not match: <br>%1$s<br>Destination "
"size: %2$s, Local size: %3$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:494
#, php-format
msgid ""
"File MD5's do not match for file: %1$s<br>Local MD5: %2$s Remote MD5: %3$s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Payload.php:644
#, php-format
msgid "Part file missing expected suffix %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/TransportFactory.php:37
#, php-format
msgid "Unknown transport method %s passed to factory."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/TransportManager.php:116
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/TransportManager.php:141
msgid "Unable to determine transport method, method is empty."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/CURLFileTransport.php:55
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:51
msgid "File transport URL is empty."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/CURLFileTransport.php:62
msgid "File transport request options is not a valid array."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/CURLFileTransport.php:69
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:58
msgid "Could not transport payload, no payload provided."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/CURLFileTransport.php:76
#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:65
msgid "Provided file is not a valid resource."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/CURLFileTransport.php:88
msgid "Resource URI is empty."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/CURLFileTransport.php:129
msgid "File transport payload is empty"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:72
msgid "Unable to rewind the payload file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:81
msgid "Unable to get stream contents."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:90
msgid "Unable to get gzencode file contents."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:104
msgid "Unable to transport the payload file to the destination."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:129
msgid "Unable to create temporary file to receive payload."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:138
msgid "Unable to decode payload contents."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:147
msgid "Unable to decompress payload contents."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:154
msgid "Unable to write payload contents to a tmp file."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/Transport/HTTP/FileInBodyTransport.php:161
msgid "Unable to rewind payload tmp file."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:176
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:184
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:185
#: src/wp-migrate-db-pro/class/Common/Filesystem/RecursiveScanner.php:189
#, php-format
msgid "Unable to read the contents of directory \"%s\"."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:892
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:870
msgid "Loopback request failed"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:894
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:872
msgid ""
"A loopback is when your own site tries to connect to itself. This plugin "
"relies on loopback requests to perform migrations. Therefore a new migration "
"cannot be started while loopback requests are failing."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:909
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:887
msgid "how to troubleshoot failed loopback requests"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:915
#: src/wp-migrate-db-pro/class/Common/Migration/MigrationManager.php:893
#, php-format
msgid ""
"Visit the <a class=\"underline\" href=\"%1$s\">Site Health</a> page to check "
"whether your site can perform loopback requests, and learn %2$s."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:257
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:251
#, php-format
msgid ""
"Unable to create backup directory when finalizing Theme & Plugin Files "
"migration: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:274
#: src/wp-migrate-db-pro/class/Common/TPF/ThemePluginFilesFinalize.php:268
#, php-format
msgid ""
"Unable to backup destination file when finalizing Theme & Plugin Files "
"migration: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:1410
#, php-format
msgid ""
"The table `%1s` contains characters which are invalid in the target "
"database. See&nbsp;<a href=\"%2s\" target=\"_blank\">our documentation</a> "
"for more information. Query: %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:170
msgid ""
"Could not scan invalid directory path, please check debug log for details."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:458
#, php-format
msgid "Could not scan the directory at %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:474
#, php-format
msgid "Could not resume scan of the directory at %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:842
msgid "Scan manifest file data not an array."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:851
#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:893
msgid "Scan manifest file name could not be retrieved."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/FileProcessor.php:900
msgid "Scan manifest file does not exist."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Transfers/Files/Util.php:289
#, php-format
msgid "The following directory failed to transfer: <br> %s"
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Migration/Connection/Local.php:139
#, php-format
msgid "Required parameter \"%s\" not supplied."
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Queue/QueueHelper.php:145
#, php-format
msgid "Unable to store remote manifest, response returned code: %d"
msgstr ""

#: src/wp-migrate-db-pro/class/Common/Sql/Table.php:2195
#, php-format
msgid "Transfer failed while migrating table %s."
msgstr ""

#: src/wp-migrate-db-pro/class/Pro/Transfers/Files/TransferManager.php:322
#, php-format
msgid "File transfer failed with response code: %s"
msgstr ""
