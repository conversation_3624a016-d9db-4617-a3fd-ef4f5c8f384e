# Translation of Themes - Twenty Twenty-Two in Chinese (China)
# This file is distributed under the same license as the Themes - Twenty Twenty-Two package.
msgid ""
msgstr ""
"PO-Revision-Date: 2022-08-11 18:08:50+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.0-beta.2\n"
"Language: zh_CN\n"
"Project-Id-Version: Themes - Twenty Twenty-Two\n"

#. Theme Name of the theme
#, gp-priority: high
msgid "Twenty Twenty-Two"
msgstr "Twenty Twenty-Two"

#: styles/blue.json
msgctxt "Style variation name"
msgid "Blue"
msgstr "蓝色"

#: styles/blue.json
msgctxt "Font family name"
msgid "DM Sans"
msgstr "DM Sans"

#: styles/pink.json
msgctxt "Style variation name"
msgid "Pink"
msgstr "粉红色"

#: styles/pink.json
msgctxt "Font family name"
msgid "IBM Plex Sans"
msgstr "IBM Plex Sans"

#: styles/pink.json
msgctxt "Font family name"
msgid "IBM Plex Mono"
msgstr "IBM Plex Mono"

#: styles/swiss.json
msgctxt "Style variation name"
msgid "Swiss"
msgstr "瑞士"

#: styles/swiss.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: styles/swiss.json
msgctxt "Duotone name"
msgid "Default filter"
msgstr "默认筛选"

#: inc/patterns/hidden-404.php:14
msgid "Search"
msgstr "搜索"

#: inc/patterns/hidden-404.php:14
msgctxt "label"
msgid "Search"
msgstr "搜索"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "页脚"

#: theme.json
msgctxt "Custom template name"
msgid "Page (No Separators)"
msgstr "页面（无分隔号）"

#: theme.json
msgctxt "Custom template name"
msgid "Single Post (No Separators)"
msgstr "单篇文章（无分隔号）"

#: theme.json
msgctxt "Custom template name"
msgid "Blank"
msgstr "空白页面"

#: theme.json
msgctxt "Custom template name"
msgid "Page (Large Header)"
msgstr "页面（大号页眉）"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "页眉"

#: theme.json
msgctxt "Template part name"
msgid "Header (Dark, large)"
msgstr "页眉（深色，大号）"

#: theme.json
msgctxt "Template part name"
msgid "Header (Dark, small)"
msgstr "页眉（深色，小号）"

#: theme.json
msgctxt "Font family name"
msgid "System Font"
msgstr "系统字体"

#: theme.json
msgctxt "Font family name"
msgid "Source Serif Pro"
msgstr "Source Serif Pro"

#: theme.json styles/blue.json styles/pink.json styles/swiss.json
msgctxt "Color name"
msgid "Foreground"
msgstr "前景"

#: theme.json styles/blue.json styles/pink.json styles/swiss.json
msgctxt "Color name"
msgid "Background"
msgstr "背景"

#: theme.json styles/blue.json styles/pink.json styles/swiss.json
msgctxt "Color name"
msgid "Primary"
msgstr "主要"

#: theme.json styles/blue.json styles/pink.json styles/swiss.json
msgctxt "Color name"
msgid "Secondary"
msgstr "次要"

#: theme.json styles/blue.json styles/pink.json styles/swiss.json
msgctxt "Color name"
msgid "Tertiary"
msgstr "第三"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical secondary to tertiary"
msgstr "次要垂直至第三"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical secondary to background"
msgstr "次要垂直至背景"

#: theme.json
msgctxt "Gradient name"
msgid "Vertical tertiary to background"
msgstr "第三垂直至背景"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal primary to foreground"
msgstr "主要对角至前景"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal secondary to background"
msgstr "次要对角至背景"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal background to secondary"
msgstr "背景对角至次要"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal tertiary to background"
msgstr "第三对角至背景"

#: theme.json
msgctxt "Gradient name"
msgid "Diagonal background to tertiary"
msgstr "背景对角至第三"

#: theme.json
msgctxt "Duotone name"
msgid "Foreground and background"
msgstr "前景和背景"

#: theme.json
msgctxt "Duotone name"
msgid "Foreground and secondary"
msgstr "前景和次要"

#: theme.json
msgctxt "Duotone name"
msgid "Foreground and tertiary"
msgstr "前景和第三"

#: theme.json
msgctxt "Duotone name"
msgid "Primary and background"
msgstr "主要和背景"

#: theme.json
msgctxt "Duotone name"
msgid "Primary and secondary"
msgstr "主要和次要"

#: theme.json
msgctxt "Duotone name"
msgid "Primary and tertiary"
msgstr "主要和第三"

#: inc/block-patterns.php:17
msgid "Featured"
msgstr "特色"

#: inc/block-patterns.php:18
msgid "Footers"
msgstr "页脚"

#: inc/block-patterns.php:19
msgid "Headers"
msgstr "页眉"

#: inc/block-patterns.php:20
msgid "Query"
msgstr "查询"

#: inc/block-patterns.php:21
msgid "Pages"
msgstr "分页"

#: inc/patterns/footer-about-title-logo.php:13 inc/patterns/footer-blog.php:13
msgid "About us"
msgstr "关于我们"

#: inc/patterns/footer-blog.php:6
msgid "Blog footer"
msgstr "博客页脚"

#: inc/patterns/footer-blog.php:23 inc/patterns/general-featured-posts.php:10
msgid "Latest posts"
msgstr "最新文章"

#: inc/patterns/footer-blog.php:31
#: inc/patterns/page-sidebar-blog-posts-right.php:74
msgid "Categories"
msgstr "分类"

#. Translators: WordPress link.
#: inc/patterns/footer-blog.php:49 inc/patterns/footer-dark.php:17
#: inc/patterns/footer-default.php:17 inc/patterns/footer-logo.php:17
#: inc/patterns/footer-navigation.php:19
#: inc/patterns/footer-query-images-title-citation.php:34
#: inc/patterns/footer-query-title-citation.php:32
msgid "Proudly powered by %s"
msgstr "自豪地采用 %s"

#: inc/patterns/footer-blog.php:50 inc/patterns/footer-dark.php:18
#: inc/patterns/footer-default.php:18 inc/patterns/footer-logo.php:18
#: inc/patterns/footer-navigation.php:20
#: inc/patterns/footer-query-images-title-citation.php:35
#: inc/patterns/footer-query-title-citation.php:33
msgid "https://wordpress.org"
msgstr "https://cn.wordpress.org"

#: inc/patterns/footer-default.php:6
msgid "Default footer"
msgstr "默认页脚"

#: inc/patterns/footer-navigation-copyright.php:20
#: inc/patterns/footer-social-copyright.php:24
msgid "© Site Title"
msgstr "© 站点标题"

#: inc/patterns/general-image-with-caption.php:11
msgid "Hummingbird"
msgstr "蜂鸟"

#: inc/patterns/footer-about-title-logo.php:17 inc/patterns/footer-blog.php:17
msgid "We are a rogue collective of bird watchers. We’ve been known to sneak through fences, climb perimeter walls, and generally trespass in order to observe the rarest of birds."
msgstr "我们是一群无聊的观鸟者。 众所周知，为了观察最稀有的鸟类，我们会偷偷穿过栅栏，爬上围墙。"

#: inc/patterns/page-about-media-left.php:21
msgid "Oh hello. My name’s Doug, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show on Tuesday evenings at 11PM EDT."
msgstr "大家好，我叫 Doug ， 很高兴您能找到我的网站。 我是一名狂热的鸟类观察者，同时我也会在北京时间每周二晚 11 点播出自己的广播节目。"

#: inc/patterns/page-about-media-right.php:20
msgid "Oh hello. My name’s Emery, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show on Tuesday evenings at 11PM EDT."
msgstr "大家好，我叫 Emery ， 很高兴您能找到我的网站。 我是一名狂热的鸟类观察者，同时我也会在北京时间每周二晚 11 点播出自己的广播节目。"

#: inc/patterns/page-about-simple-dark.php:22
msgid "Oh hello. My name’s Jesús, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show on Tuesday evenings at 11PM EDT."
msgstr "大家好，我叫 Jesús ， 很高兴您能找到我的网站。 我是一名狂热的鸟类观察者，同时我也会在北京时间每周二晚 11 点播出自己的广播节目。"

#: inc/patterns/page-about-solid-color.php:22
msgid "Oh hello. My name’s Edvard, and you’ve found your way to my website. I’m an avid bird watcher, and I also broadcast my own radio show every Tuesday evening at 11PM EDT. Listen in sometime!"
msgstr "大家好，我叫 Edvard ， 很高兴您能找到我的网站。 我是一名狂热的鸟类观察者，同时我也会在北京时间每周二晚 11 点播出自己的广播节目。"

#: inc/patterns/general-featured-posts.php:6
msgid "Featured posts"
msgstr "特色文章"

#: inc/patterns/general-image-with-caption.php:10
msgid "Hummingbird illustration"
msgstr "蜂鸟插图"

#: inc/patterns/general-image-with-caption.php:15
msgid "A beautiful bird featuring a surprising set of color feathers."
msgstr "一只美丽的鸟，拥有一组令人惊讶的彩色羽毛。"

#: inc/patterns/general-large-list-names.php:6
msgid "Large list of names"
msgstr "大号名单"

#: inc/patterns/general-large-list-names.php:11
#: inc/patterns/page-sidebar-poster.php:32
msgid "An icon representing binoculars."
msgstr "代表双筒望远镜的图标。"

#: inc/patterns/general-large-list-names.php:21
msgid "Jesús Rodriguez, Doug Stilton, Emery Driscoll, Megan Perry, Rowan Price, Angelo Tso, Edward Stilton, Amy Jensen, Boston Bell, Shay Ford, Lee Cunningham, Evelynn Ray, Landen Reese, Ewan Hart, Jenna Chan, Phoenix Murray, Mel Saunders, Aldo Davidson, Zain Hall."
msgstr "Jesús Rodriguez、 Doug Stilton、 Emery Driscoll、 Megan Perry、 Rowan Price、 Angelo Tso、 Edward Stilton、 Amy Jensen、 Boston Bell、 Shay Ford、 Lee Cunningham、 Evelynn Ray、 Landen Reese、 Ewan Hart、 Jenna Chan、 Phoenix Murray、 Mel Saunders、 Aldo Davidson、 Zain Hall。"

#: inc/patterns/general-large-list-names.php:30
msgid "Read more"
msgstr "详细了解"

#: inc/patterns/general-layered-images-with-duotone.php:6
msgid "Layered images with duotone"
msgstr "双色调的多层图片组合"

#: inc/patterns/general-layered-images-with-duotone.php:10
#: inc/patterns/page-sidebar-blog-posts-right.php:58
msgid "Illustration of a flying bird."
msgstr "飞翔的鸟插图"

#: inc/patterns/general-layered-images-with-duotone.php:9
#: inc/patterns/header-image-background-overlay.php:11
msgid "Painting of ducks in the water."
msgstr "水上鸭子的绘画"

#: inc/patterns/general-list-events.php:6
msgid "List of events"
msgstr "活动列表"

#: inc/patterns/general-list-events.php:11
msgid "Speaker Series"
msgstr "演讲分享系列"

#: inc/patterns/general-list-events.php:25
msgid "May 14th, 2022, 6 PM"
msgstr "2022 年 5 月 14 日下午 6:00"

#: inc/patterns/general-list-events.php:31
msgid "Jesús Rodriguez"
msgstr "Jesús Rodriguez"

#: inc/patterns/general-list-events.php:37
#: inc/patterns/general-list-events.php:85
msgid "The Vintagé Theater<br>245 Arden Rd.<br>Gardenville, NH"
msgstr "The Vintagé Theater<br>245 Arden Rd.<br>Gardenville, NH"

#: inc/patterns/general-list-events.php:49
msgid "May 16th, 2022, 6 PM"
msgstr "2022 年 5 月 16 日下午 6:00"

#: inc/patterns/general-list-events.php:55
msgid "Doug Stilton"
msgstr "Doug Stilton"

#: inc/patterns/general-list-events.php:61
#: inc/patterns/general-list-events.php:109
msgid "The Swell Theater<br>120 River Rd.<br>Rainfall, NH"
msgstr "The Swell Theater<br>120 River Rd.<br>Rainfall, NH"

#: inc/patterns/general-list-events.php:73
msgid "May 18th, 2022, 7 PM"
msgstr "2022 年 5 月 18 日晚上 7:00"

#: inc/patterns/general-list-events.php:79
msgid "Amy Jensen"
msgstr "Amy Jensen"

#: inc/patterns/general-list-events.php:97
msgid "May 20th, 2022, 6 PM"
msgstr "2022 年 5 月 20 日下午 6:00"

#: inc/patterns/general-list-events.php:103
msgid "Emery Driscoll"
msgstr "Emery Driscoll"

#: inc/patterns/general-pricing-table.php:6
msgid "Pricing table"
msgstr "价格表"

#: inc/patterns/general-pricing-table.php:15
msgctxt "First item in a numbered list."
msgid "1"
msgstr "1"

#: inc/patterns/general-pricing-table.php:19
msgid "Pigeon"
msgstr "鸽子"

#: inc/patterns/general-pricing-table.php:28
msgid "$25"
msgstr "￥25"

#: inc/patterns/general-pricing-table.php:43
msgctxt "Second item in a numbered list."
msgid "2"
msgstr "2"

#: inc/patterns/general-pricing-table.php:47
msgid "Sparrow"
msgstr "麻雀"

#: inc/patterns/general-pricing-table.php:23
msgid "Help support our growing community by joining at the Pigeon level. Your support will help pay our writers, and you’ll get access to our exclusive newsletter."
msgstr "加入鸽子计划即可支持我们的社区发展。 您的费用将帮助我们支付作者稿费，且您还可以阅读我们推出的独家电子报。"

#: inc/patterns/general-pricing-table.php:51
msgid "Join at the Sparrow level and become a member of our flock! You’ll receive our newsletter, plus a bird pin that you can wear with pride when you’re out in nature."
msgstr "加入麻雀计划，成为我们鸟群中的一员！ 您会收到我们的电子报，还有一枚鸟类别针，当您在大自然中活动时，可以自豪地佩戴它。"

#: inc/patterns/general-pricing-table.php:56
msgid "$75"
msgstr "￥75"

#: inc/patterns/general-pricing-table.php:71
msgctxt "Third item in a numbered list."
msgid "3"
msgstr "3"

#: inc/patterns/general-pricing-table.php:75
msgid "Falcon"
msgstr "猎鹰"

#: inc/patterns/general-pricing-table.php:79
msgid "Play a leading role for our community by joining at the Falcon level. This level earns you a seat on our board, where you can help plan future birdwatching expeditions."
msgstr "加入猎鹰计划，为我们的社区发挥领导作用。 此计划会让您成为我们董事会中的一元，并且可以帮助我们计划未来的观鸟活动。"

#: inc/patterns/general-pricing-table.php:84
msgid "$150"
msgstr "￥150"

#: inc/patterns/general-subscribe.php:11
msgid "Watch birds<br>from your inbox"
msgstr "在您的收件箱中<br>观赏鸟类"

#: inc/patterns/general-subscribe.php:16
msgid "Join our mailing list"
msgstr "加入我们的邮件列表"

#: inc/patterns/general-two-images-text.php:17
#: inc/patterns/general-wide-image-intro-buttons.php:10
#: inc/patterns/header-large-dark.php:29 inc/patterns/header-small-dark.php:25
#: inc/patterns/hidden-bird.php:12 inc/patterns/hidden-heading-and-bird.php:19
msgid "Illustration of a bird flying."
msgstr "飞翔的鸟插图。"

#: inc/patterns/general-two-images-text.php:25
msgid "SCREENING"
msgstr "筛选"

#: inc/patterns/general-two-images-text.php:11
msgid "Illustration of a bird sitting on a branch."
msgstr "站在树枝上的鸟插图。"

#: inc/patterns/general-subscribe.php:6
msgid "Subscribe callout"
msgstr "订阅标注"

#: inc/patterns/general-two-images-text.php:29
#: inc/patterns/page-layout-image-text-and-video.php:25
msgid "May 14th, 2022 @ 7:00PM<br>The Vintagé Theater,<br>245 Arden Rd.<br>Gardenville, NH"
msgstr "2022 年5 月 14 日晚上 7:00 <br>The Vintagé Theater,<br>245 Arden Rd.<br>Gardenville, NH"

#: inc/patterns/general-two-images-text.php:42
#: inc/patterns/general-wide-image-intro-buttons.php:35
#: inc/patterns/page-layout-image-text-and-video.php:30
msgid "Buy Tickets"
msgstr "购票"

#: inc/patterns/general-video-header-details.php:11
#: inc/patterns/page-layout-image-text-and-video.php:11
msgid "<em>Warble</em>, a film about <br>hobbyist bird watchers."
msgstr "<strong>Warble</strong>，一部关于业余观鸟者的电影。"

#: inc/patterns/general-video-header-details.php:29
msgid "Featuring"
msgstr "特色"

#: inc/patterns/general-video-header-details.php:35
msgid "Jesús Rodriguez<br>Doug Stilton<br>Emery Driscoll<br>Megan Perry<br>Rowan Price"
msgstr "Jesús Rodriguez<br>Doug Stilton<br>Emery Driscoll<br>Megan Perry<br>Rowan Price"

#: inc/patterns/general-video-header-details.php:41
msgid "Angelo Tso<br>Edward Stilton<br>Amy Jensen<br>Boston Bell<br>Shay Ford"
msgstr "Angelo Tso<br>Edward Stilton<br>Amy Jensen<br>Boston Bell<br>Shay Ford"

#: inc/patterns/general-video-trailer.php:6
msgid "Video trailer"
msgstr "视频预告片"

#: inc/patterns/general-video-trailer.php:12
#: inc/patterns/page-layout-image-text-and-video.php:49
msgid "Extended Trailer"
msgstr "加长预告片"

#: inc/patterns/general-video-trailer.php:16
#: inc/patterns/general-wide-image-intro-buttons.php:22
msgid "A film about hobbyist bird watchers, a catalog of different birds, paired with the noises they make. Each bird is listed by their scientific name so things seem more official."
msgstr "一部关于业余观鸟者的影片，展示了不同品种的鸟类，以及它们发出的声音。 每种鸟类都按其学名列出，让电影看起来更正式。"

#: inc/patterns/general-wide-image-intro-buttons.php:31
msgid "Learn More"
msgstr "详细了解"

#: inc/patterns/general-wide-image-intro-buttons.php:16
msgid "Welcome to<br>the Aviary"
msgstr "欢迎观看<br>鸟类展示"

#: inc/patterns/header-centered-logo.php:6
msgid "Header with centered logo"
msgstr "logo 居中的页眉"

#: inc/patterns/header-default.php:6
msgid "Default header"
msgstr "默认页眉"

#: inc/patterns/header-image-background.php:11
msgid "Illustration of a flying bird"
msgstr "飞翔的鸟插图"

#: inc/patterns/header-large-dark.php:6
msgid "Large header with dark background"
msgstr "深色背景的大号页眉"

#: inc/patterns/header-large-dark.php:24
#: inc/patterns/hidden-heading-and-bird.php:14
msgid "<em>The Hatchery</em>: a blog about my adventures in bird watching"
msgstr "<strong>生生不息</strong>：记录我的观鸟旅行社的博客"

#: inc/patterns/header-small-dark.php:6
msgid "Small header with dark background"
msgstr "深色背景的小号页眉"

#: inc/patterns/hidden-404.php:6
msgid "404 content"
msgstr "404 内容"

#: inc/patterns/hidden-404.php:9
msgctxt "Error code for a webpage that is not found."
msgid "404"
msgstr "404"

#: inc/patterns/hidden-404.php:12
msgid "This page could not be found. Maybe try a search?"
msgstr "找不到此页面，请尝试使用搜索功能进行查询。"

#: inc/patterns/page-sidebar-blog-posts-right.php:80
msgid "Tags"
msgstr "标签"

#: inc/patterns/hidden-bird.php:9 inc/patterns/hidden-heading-and-bird.php:10
msgid "Heading and bird image"
msgstr "标题和鸟类图片"

#: inc/patterns/page-about-large-image-and-buttons.php:17
msgid "Purchase my work"
msgstr "购买我的作品"

#: inc/patterns/page-about-large-image-and-buttons.php:25
msgid "Support my studio"
msgstr "支持我的工作室"

#: inc/patterns/page-about-large-image-and-buttons.php:43
msgid "Read about me"
msgstr "详细了解"

#: inc/patterns/page-about-large-image-and-buttons.php:51
msgid "Learn about my process"
msgstr "了解我的工序"

#: inc/patterns/page-about-large-image-and-buttons.php:59
msgid "Join my mailing list"
msgstr "加入我的邮件列表"

#: inc/patterns/page-about-links-dark.php:26
#: inc/patterns/page-about-links.php:30
msgid "Watch our videos"
msgstr "观看我们的视频"

#: inc/patterns/page-about-links-dark.php:30
#: inc/patterns/page-about-links.php:34
msgid "Listen on iTunes Podcasts"
msgstr "在 iTunes Podcasts 上收听"

#: inc/patterns/page-about-links-dark.php:34
#: inc/patterns/page-about-links.php:38
msgid "Listen on Spotify"
msgstr "在 Spotify 上收听"

#: inc/patterns/page-about-links-dark.php:38
#: inc/patterns/page-about-links.php:42
msgid "Support the show"
msgstr "支持节目"

#: inc/patterns/page-about-links.php:6
msgid "About page links"
msgstr "关于页面链接"

#: inc/patterns/page-layout-two-columns.php:21
msgid "WELCOME"
msgstr "欢迎"

#: inc/patterns/page-layout-two-columns.php:58
msgid "POSTS"
msgstr "文章"

#: inc/patterns/page-sidebar-poster.php:40
msgid "Date"
msgstr "日期"

#: inc/patterns/page-sidebar-poster.php:44
msgid "February, 12 2021"
msgstr "2021 年 2 月 12 日"

#: inc/patterns/page-sidebar-poster.php:52
msgid "Location"
msgstr "地点"

#: inc/patterns/page-sidebar-poster.php:56
msgid "The Grand Theater<br>154 Eastern Avenue<br>Maryland NY, 12345"
msgstr "The Grand Theater<br>154 Eastern Avenue<br>Maryland NY, 12345"

#: inc/patterns/query-default.php:6
msgid "Default posts"
msgstr "默认文章"

#: inc/patterns/query-grid.php:6
msgid "Grid of posts"
msgstr "网格式文章"

#: inc/patterns/page-about-large-image-and-buttons.php:6
msgid "About page with large image and buttons"
msgstr "包含大图和按钮的关于页面"

#: inc/patterns/page-about-links-dark.php:6
msgid "About page links (dark)"
msgstr "关于页面链接（深色）"

#: inc/patterns/page-about-links-dark.php:13
#: inc/patterns/page-about-links.php:10
msgid "Logo featuring a flying bird"
msgstr "飞翔的特色鸟 Logo"

#: inc/patterns/page-about-links-dark.php:17
msgid "A trouble of hummingbirds"
msgstr "蜂鸟的烦恼"

#: inc/patterns/page-about-links.php:17
msgid "Swoop"
msgstr "鸟瞰天下"

#: inc/patterns/page-about-links.php:21
msgid "A podcast about birds"
msgstr "谈论鸟类的播客"

#: inc/patterns/page-about-media-left.php:6
msgid "About page with media on the left"
msgstr "在左侧显示媒体的关于页面"

#: inc/patterns/page-about-media-left.php:9
#: inc/patterns/page-sidebar-poster.php:26
msgid "Image of a bird on a branch"
msgstr "树枝上的鸟插图"

#: inc/patterns/page-about-media-left.php:17
msgid "Doug"
msgstr "Doug"

#: inc/patterns/page-about-media-left.php:17
msgid "Stilton"
msgstr "Stilton"

#: inc/patterns/page-about-media-right.php:6
msgid "About page with media on the right"
msgstr "在右侧显示媒体的关于页面"

#: inc/patterns/page-about-media-right.php:9
msgid "An image of a bird flying"
msgstr "图片：飞翔的鸟"

#: inc/patterns/page-about-media-right.php:16
msgid "Emery<br>Driscoll"
msgstr "Emery<br>Driscoll"

#: inc/patterns/page-sidebar-grid-posts.php:6
msgid "Grid of posts with left sidebar"
msgstr "含左侧边栏的网格式文章"

#: inc/patterns/page-sidebar-poster.php:6
msgid "Poster with right sidebar"
msgstr "含右侧边栏的海报"

#: inc/patterns/page-sidebar-blog-posts.php:6
msgid "Blog posts with left sidebar"
msgstr "含左侧边栏的博客文章"

#: inc/patterns/page-sidebar-blog-posts-right.php:6
msgid "Blog posts with right sidebar"
msgstr "含右侧边栏的博客文章"

#: inc/patterns/header-with-tagline.php:6
msgid "Header with tagline"
msgstr "含站点副标题的页眉"

#: inc/patterns/header-title-navigation-social.php:6
msgid "Title, navigation, and social links header"
msgstr "含站点标题、导航栏和社交链接的页眉"

#: inc/patterns/header-title-and-button.php:6
msgid "Title and button header"
msgstr "含站点标题和按钮的页眉"

#: inc/patterns/header-stacked.php:6
msgid "Logo and navigation header"
msgstr "含 Logo和导航栏的页眉"

#: inc/patterns/header-logo-navigation-offset-tagline.php:6
msgid "Logo, navigation, and offset tagline Header"
msgstr "含 Logo、导航栏和偏移副标题的页眉"

#: inc/patterns/header-image-background.php:6
msgid "Header with image background"
msgstr "含图片背景的页眉"

#: inc/patterns/header-image-background-overlay.php:6
msgid "Header with image background and overlay"
msgstr "含图片背景和叠加层的页眉"

#: inc/patterns/general-wide-image-intro-buttons.php:6
msgid "Wide image with introduction and buttons"
msgstr "含介绍和按钮的宽幅图片"

#: inc/patterns/general-video-header-details.php:6
msgid "Video with header and details"
msgstr "含标题和详细信息的视频"

#: inc/patterns/general-two-images-text.php:6
msgid "Two images with text"
msgstr "含文字的两张图片"

#: inc/patterns/footer-social-copyright.php:6
msgid "Footer with social links and copyright"
msgstr "含社交链接和版权信息的页脚"

#: inc/patterns/footer-navigation-copyright.php:6
msgid "Footer with navigation and copyright"
msgstr "含导航栏和版权信息的页脚"

#: inc/patterns/footer-query-images-title-citation.php:6
msgid "Footer with query, featured images, title, and citation"
msgstr "含查询、特色图片、标题和引文的页脚"

#: inc/patterns/footer-query-title-citation.php:6
msgid "Footer with query, title, and citation"
msgstr "含查询、标题和引文的页脚"

#: inc/patterns/general-divider-dark.php:6
msgid "Divider with image and color (dark)"
msgstr "含图片和颜色的分隔符（深色）"

#: inc/patterns/general-image-with-caption.php:6
msgid "Image with caption"
msgstr "含字幕的图片"

#: inc/patterns/general-divider-light.php:6
msgid "Divider with image and color (light)"
msgstr "含图片的分隔符（浅色）"

#: inc/patterns/footer-title-tagline-social.php:6
msgid "Footer with title, tagline, and social links on a dark background"
msgstr "含标题、副标题和社交链接的深色背景页脚"

#: inc/patterns/footer-navigation.php:6
msgid "Footer with navigation and citation"
msgstr "含导航和引文的页脚"

#: inc/patterns/footer-logo.php:6
msgid "Footer with logo and citation"
msgstr "含 logo 和引文的页脚"

#: inc/patterns/footer-dark.php:6
msgid "Dark footer with title and citation"
msgstr "含标题和引文的深色页脚"

#: inc/patterns/footer-about-title-logo.php:6
msgid "Footer with text, title, and logo"
msgstr "含文字、标题和 logo 的页脚"

#: inc/patterns/page-about-links-dark.php:42
#: inc/patterns/page-about-links.php:46
msgid "About the hosts"
msgstr "关于主办方"

#: inc/patterns/page-about-simple-dark.php:6
msgid "Simple dark about page"
msgstr "简单的关于页面"

#: inc/patterns/page-about-simple-dark.php:18
msgid "Jesús<br>Rodriguez"
msgstr "Jesús<br>Rodriguez"

#: inc/patterns/page-about-solid-color.php:14
msgid "Edvard<br>Smith"
msgstr "Edvard<br>Smith"

#: inc/patterns/query-simple-blog.php:6
msgid "Simple blog posts"
msgstr "简单的博客文章"

#: inc/patterns/query-text-grid.php:6
msgid "Text-based grid of posts"
msgstr "文字网格式文章"

#: inc/patterns/page-about-large-image-and-buttons.php:33
msgid "Take a class"
msgstr "课程精选"

#: inc/patterns/page-about-solid-color.php:6
msgid "About page on solid color background"
msgstr "纯色背景的“关于”页面"

#: inc/patterns/page-layout-image-and-text.php:6
msgid "Page layout with image and text"
msgstr "含图片和文字的页面布局"

#: inc/patterns/page-layout-image-and-text.php:15
msgctxt "Short for to be determined"
msgid "TBD"
msgstr "待定"

#: inc/patterns/page-layout-image-and-text.php:10
msgid "<em>Watching Birds </em><br><em>in the Garden</em>"
msgstr "在花园中<br>观鸟"

#: inc/patterns/page-layout-image-and-text.php:27
#: inc/patterns/page-layout-two-columns.php:36
msgid "Oh hello. My name’s Angelo, and I operate this blog. I was born in Portland, but I currently live in upstate New York. You may recognize me from publications with names like <a href=\"#\">Eagle Beagle</a> and <a href=\"#\">Mourning Dive</a>. I write for a living.<br><br>I usually use this blog to catalog extensive lists of birds and other things that I find interesting. If you find an error with one of my lists, please keep it to yourself.<br><br>If that’s not your cup of tea, <a href=\"#\">I definitely recommend this tea</a>. It’s my favorite."
msgstr "您好，我叫 Angelo，负责此博客的运营。 我出生在波特兰，目前住在纽约州北部。 您可能是通过 <a href=\"#\">Eagle Beagle</a> 或者 <a href=\"#\">Mourning Dive</a> 等出版物认识我的。 我靠写作为生。<br><br>我通常使用此博客来分类大量鸟类和其他有趣的内容。 如果您发现我的清单之一有错误，请帮我保密。 <br><br>If that’s not your cup of tea, <a href=\"#\">I definitely recommend this tea</a>. It’s my favorite."

#: inc/patterns/page-layout-image-text-and-video.php:53
msgid "Oh hello. My name’s Angelo, and you’ve found your way to my blog. I write about a range of topics, but lately I’ve been sharing my hopes for next year."
msgstr "大家好，我叫 Angelo，这是我的博客。 我写了一系列主题的文章，最近一直在分享自己对于明年的展望。"

#: inc/patterns/page-layout-image-text-and-video.php:42
msgid "An illustration of a bird in flight"
msgstr "飞翔的鸟插图"

#: inc/patterns/page-layout-two-columns.php:6
msgid "Page layout with two columns"
msgstr "含两个栏位的页面布局"

#: inc/patterns/page-layout-two-columns.php:10
msgid "<em>Goldfinch </em><br><em>&amp; Sparrow</em>"
msgstr "金翅雀<br>& 麻雀"

#: inc/patterns/page-sidebar-poster.php:14
msgid "<em>Flutter</em>, a collection of bird-related ephemera"
msgstr "<em>Flutter</em>，鸟类相关摄影展"

#: inc/patterns/query-image-grid.php:6
msgid "Grid of image posts"
msgstr "网格式图片文章"

#: inc/patterns/query-irregular-grid.php:6
msgid "Irregular grid of posts"
msgstr "不规则网格式文章"

#: inc/patterns/page-layout-image-text-and-video.php:6
msgid "Page layout with image, text and video"
msgstr "含图片、文字和视频的页面布局"

#: inc/patterns/page-layout-image-text-and-video.php:21
msgid "Screening"
msgstr "首映"

#: inc/patterns/query-large-titles.php:6
msgid "Large post titles"
msgstr "大号文章标题"

#. Theme URI of the theme
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentytwo/"
msgstr "https://cn.wordpress.org/themes/twentytwentytwo/"

#. Author URI of the theme
#, gp-priority: low
msgid "https://wordpress.org/"
msgstr "https://cn.wordpress.org/"

#. Author of the theme
#, gp-priority: low
msgid "the WordPress team"
msgstr "WordPress 团队"
