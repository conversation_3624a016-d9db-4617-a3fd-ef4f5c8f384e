<?php

namespace DeliciousBrains\WPMDB\Container\DI\Definition\Exception;

use DeliciousBrains\WPMDB\Container\DI\Definition\Definition;
/**
 * Invalid DI definitions.
 *
 * <AUTHOR> <<EMAIL>>
 */
class DefinitionException extends \Exception
{
    public static function create(Definition $definition, $message)
    {
        return new self(\sprintf('%s' . \PHP_EOL . 'Full definition:' . \PHP_EOL . '%s', $message, (string) $definition));
    }
}
