<?php

/**
 * Fired during plugin deactivation
 *
 * @link       http://example.com
 * @since      1.0.0
 *
 * @package    Hcylsoft
 * @subpackage Hcylsoft/includes
 */

/**
 * Fired during plugin deactivation.
 *
 * This class defines all code necessary to run during the plugin's deactivation.
 *
 * @since      1.0.0
 * @package    Hcylsoft
 * @subpackage Hcylsoft/includes
 * <AUTHOR> Name <<EMAIL>>
 */
class Hcylsoft_Deactivator {

	/**
	 * Short Description. (use period)
	 *
	 * Long Description.
	 *
	 * @since    1.0.0
	 */
	public static function deactivate() {
        wp_clear_scheduled_hook( 'my_plugin_cron_job' );
	}

}
