@font-face {
  font-family: "iconfont"; /* Project id 4405527 */
  src: url('iconfont.woff2?t=1709537262821') format('woff2'),
       url('iconfont.woff?t=1709537262821') format('woff'),
       url('iconfont.ttf?t=1709537262821') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-shangchuantupian:before {
  content: "\e609";
}

.icon-zhuye:before {
  content: "\e6ae";
}

.icon-dianying:before {
  content: "\e6af";
}

.icon-wxbgongju:before {
  content: "\e61b";
}

.icon-gongju:before {
  content: "\e6ad";
}

.icon-touxiang:before {
  content: "\e62c";
}

