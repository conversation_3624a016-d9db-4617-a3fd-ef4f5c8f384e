# Translation of the WordPress plugin   by .
# Copyright (C) 2010
# This file is distributed under the same license as the  package.
# <AUTHOR> <EMAIL>, 2010.
#
msgid ""
msgstr ""
"Project-Id-Version: WP Super Cache\n"
"Report-Msgid-Bugs-To: http://wordpress.org/tag/wp-super-cache\n"
"POT-Creation-Date: 2010-09-27 13:58+0000\n"
"PO-Revision-Date: 2011-07-24 13:13+0200\n"
"Last-Translator: Alexandr <<EMAIL>>\n"
"Language-Team: Alyona Lompar <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Poedit-Language: Ukrainian\n"
"X-Poedit-Country: UKRAINE\n"

#: plugins/awaitingmoderation.php:4
msgid "Your comment is awaiting moderation."
msgstr "Ваш коментар очікує підтвердження."

#: plugins/awaitingmoderation.php:23
#: plugins/awaitingmoderation.php:38
#: plugins/badbehaviour.php:47
#: plugins/badbehaviour.php:65
#: plugins/domain-mapping.php:47
#: plugins/domain-mapping.php:62
#: plugins/searchengine.php:61
#: plugins/searchengine.php:76
#: wp-cache.php:1114
msgid "Disable"
msgstr "Відключити"

#: plugins/awaitingmoderation.php:29
#: plugins/badbehaviour.php:55
#: plugins/domain-mapping.php:53
#: plugins/searchengine.php:67
msgid "disabled"
msgstr "Відключений"

#: plugins/awaitingmoderation.php:31
#: plugins/badbehaviour.php:57
#: plugins/domain-mapping.php:55
#: plugins/searchengine.php:69
#: wp-cache.php:1460
msgid "enabled"
msgstr "Включений"

#: plugins/awaitingmoderation.php:33
#, php-format
msgid "Awaiting Moderation plugin is %s"
msgstr "Статус плагіна \"Awaiting Moderation\": %s"

#: plugins/awaitingmoderation.php:34
msgid "(Remove the text \"Your comment is awaiting moderation.\" when someone leaves a moderated comment.) "
msgstr "(Прибирає текст \" Ваш коментар очікує перевірки\" коли хтось розміщує коментар до запису)"

#: plugins/awaitingmoderation.php:36
#: plugins/badbehaviour.php:63
#: plugins/domain-mapping.php:60
#: plugins/searchengine.php:74
#: wp-cache.php:1114
msgid "Enable"
msgstr "Включити"

#: plugins/badbehaviour.php:45
msgid "Bad Behaviour not found. Please check your install."
msgstr "Плагін \" Bad Behaviour\" не був знайдений. Перевірте, чи завершили Ви його встановлення."

#: plugins/badbehaviour.php:60
#, php-format
msgid "Bad Behaviour support is %s."
msgstr "Статус плагіна \" Bad Behaviour\": %s."

#: plugins/badbehaviour.php:61
#, php-format
msgid "(Only legacy caching supported, disabled compression and requires <a href=\"http://www.bad-behavior.ioerror.us/\">Bad Behavior</a> in \"%s/plugins/bad-behavior/\") "
msgstr "(Даний плагін буде функціонувати лише в режимі середнього кешування, вимкніть стиск і встановіть плагін \"<a href=\"http://www.bad-behavior.ioerror.us/\">Bad Behavior</a>\" в \"%s/ plugins/bad-behavior/ \") "

#: plugins/badbehaviour.php:69
#: wp-cache.php:1191
#: wp-cache.php:1193
msgid "Warning!"
msgstr "Увага!"

#: plugins/domain-mapping.php:57
#, php-format
msgid "<a href=\"http://wordpress.org/extend/plugins/wordpress-mu-domain-mapping/\">Domain Mapping</a> support plugin is %s"
msgstr "Статус плагіна \"<a href=\"http://wordpress.org/extend/plugins/wordpress-mu-domain-mapping/\">Domain Mapping</a>\": %s"

#: plugins/domain-mapping.php:58
msgid "(support for multiple domains on multisite websites) "
msgstr "(Підтримка для MU - сервісу блогів)"

#: plugins/domain-mapping.php:72
msgid "Domain Mapping plugin detected! Please go to the Supercache plugins page and enable the domain mapping helper plugin."
msgstr "Виявлена ​​підтримка сервісу мультіблогов! Щоб полегшити і прискорити роботу плагіна, Вам потрібно включити підтримку мультіблогов в налаштуваннях."

#: plugins/searchengine.php:71
#, php-format
msgid "<a href=\"http://ocaoimh.ie/no-adverts-for-friends/\">No Adverts for Friends</a> plugin is %s."
msgstr "Статус плагіна \"<a href=\"http://ocaoimh.ie/no-adverts-for-friends/\">No Adverts for Friends</a>\": %s."

#: plugins/searchengine.php:72
msgid "(requires <a href=\"http://ocaoimh.ie/no-adverts-for-friends/\">friendsadverts.php</a> too) "
msgstr "(Вимагає установки <a href=\"http://ocaoimh.ie/no-adverts-for-friends/\">friendsadverts.php</a>)"

#: wp-cache.php:87
#, php-format
msgid "Please create %s /wp-cache-config.php from wp-super-cache/wp-cache-config-sample.php"
msgstr "Будь ласка, створіть файл %s /wp-cache-config.php з wp-super-cache/wp-cache-config-sample.php"

#: wp-cache.php:138
msgid "Warning! PHP Safe Mode Enabled!"
msgstr "Увага! Включений режим PHP Safe Mode!"

#: wp-cache.php:139
msgid "You may experience problems running this plugin because SAFE MODE is enabled."
msgstr "При роботі з плагіном можуть виникнути проблеми, тому що PHP Safe Mode включений на Вашому сервері."

#: wp-cache.php:143
msgid "Your server is set up to check the owner of PHP scripts before allowing them to read and write files."
msgstr "Ваш сервер налаштований так, що перевіряє власника для всіх PHP скриптів перед тим як дозволити їм читання або запис в файл."

#: wp-cache.php:144
#, php-format
msgid "You or an administrator may be able to make it work by changing the group owner of the plugin scripts to match that of the web server user. The group owner of the %s/cache/ directory must also be changed. See the <a href=\"http://php.net/features.safe-mode\">safe mode manual page</a> for further details."
msgstr "Ви або Адміністратор можете виправити помилки, змінивши групу власника скриптів плагіна через налаштування веб-сервера. Також потрібно змінити власника для папки %s/cache/. Для більш детальної інформації дивіться <a href=\"http://php.net/features.safe-mode\">сторінку допомоги по режиму safe mode</a> (на англ.)."

#: wp-cache.php:146
msgid "You or an administrator must disable this. See the <a href=\"http://php.net/features.safe-mode\">safe mode manual page</a> for further details. This cannot be disabled in a .htaccess file unfortunately. It must be done in the php.ini config file."
msgstr "Ви або Адміністратор повинні відключити це. Для більш детальної інформації дивіться <a href=\"http://php.net/features.safe-mode\">сторінку допомоги по режиму safe mode</a> (на англ.). Ці налаштування НЕ можуть бути відключені через файл. htaccess, для вирішення проблеми потрібно редагувати php.ini в настройках веб-сервера."

#: wp-cache.php:152
msgid "Permlink Structure Error"
msgstr "Помилка структури посилань"

#: wp-cache.php:153
msgid "A custom url or permalink structure is required for this plugin to work correctly. Please go to the <a href=\"options-permalink.php\">Permalinks Options Page</a> to configure your permalinks."
msgstr "Для коректної роботи плагіна необхідна коректна настройка структури посилань. Щоб налаштувати їх, відвідайте сторінку <a href=\"options-permalink.php\">Постійні посилання</a>."

#: wp-cache.php:165
#, php-format
msgid "Warning! Your hostname \"%s\" resolves to %s"
msgstr "Увага! Ваше hostname \"%s\" перетворено у %s"

#: wp-cache.php:166
#, php-format
msgid "Your server thinks your hostname resolves to %s. Some services such as garbage collection by this plugin, and WordPress scheduled posts may not operate correctly."
msgstr "Ваш веб-сервер вирішив, що hostname змінено на %s. Деякі опції плагіна (наприклад, \"Очищення сміття\") і сам WordPress можуть працювати некоректно."

#: wp-cache.php:167
#: wp-cache.php:181
#, php-format
msgid "Please see entry 16 in the <a href=\"%s\">Troubleshooting section</a> of the readme.txt"
msgstr "Щоб дізнатися більше, відвідайте розділ 16 в <a href=\"%s\">Описі проблем</a> у файлі readme.txt (на англ.)"

#: wp-cache.php:180
msgid "Unfortunately WordPress cannot find the file wp-cron.php. This script is required for the the correct operation of garbage collection by this plugin, WordPress scheduled posts as well as other critical activities."
msgstr "На жаль, файл wp-cron.php не було виявлено. Цей скрипт необхідний для коректної роботи опцііі \"Очищення сміття\" і самого WordPress."

#: wp-cache.php:195
msgid "Cannot continue... fix previous problems and retry."
msgstr "Неможливо продовжити ... перш ніж повторити спробу, виправте попередні помилки."

#: wp-cache.php:204
msgid "Zlib Output Compression Enabled!"
msgstr "Стиснення Zlib Output включено!"

#: wp-cache.php:205
msgid "PHP is compressing the data sent to the visitors of your site. Disabling this is recommended as the plugin caches the compressed output once instead of compressing the same page over and over again. Also see #21 in the Troubleshooting section. See <a href=\"http://php.net/manual/en/zlib.configuration.php\">this page</a> for instructions on modifying your php.ini."
msgstr "PHP використовує стиснення для стислих сторінки Вашого сайту. Відключення даної опції рекомендується, щоб уникнути багаторазового стиснення сторінок плагіном. Дізнатися більше, відвідавши розділ 16 в \"Опис проблем\" у файлі readme.txt (на англ.). Також ознайомтеся з <a href=\"http://php.net/manual/en/zlib.configuration.php\">цієї сторінкою</a> (на англ.) по php.ini."

#: wp-cache.php:209
msgid "Mod rewrite may not be installed!"
msgstr "Здається, модуль mod_rewrite не встановлено!"

#: wp-cache.php:210
msgid "It appears that mod_rewrite is not installed. Sometimes this check isn&#8217;t 100% reliable, especially if you are not using Apache. Please verify that the mod_rewrite module is loaded. It is required for serving Super Cache static files. You will still be able to use legacy or PHP modes."
msgstr "Виникла помилка, пов'язана з тим, що модуль mod_rewrite відсутній або не встановлений. Будь ласка, перевірте наявність модуля mod_rewrite в конфігураціях сервера. Модуль необхідний для коректної роботи плагіна з статичними файлами. Без підключеного mod_rewrite Ви можете використовувати тільки режими з підтримкою PHP або середню кешування."

#: wp-cache.php:215
msgid "Read Only Mode. Configuration cannot be changed."
msgstr "Режим \"только_чтеніе\". Настройки не можуть бути змінені."

#: wp-cache.php:216
#, php-format
msgid "The WP Super Cache configuration file is <code>%s/wp-cache-config.php</code> and cannot be modified. That file must be writeable by the webserver to make any changes."
msgstr "Конфігураційний файл плагіна WP Super Cache <code>%s/wp-cache-config.php</code> не може бути змінений. Щоб продовжити, Ви повинні встановити права на запис для файлу."

#: wp-cache.php:217
msgid "A simple way of doing that is by changing the permissions temporarily using the CHMOD command or through your ftp client. Make sure it&#8217;s globally writeable and it should be fine."
msgstr "Найпростіший спосіб виправити це - змінити права доступу, використовуючи CHMOD, через FTP-клієнт. Досить встановити глобальні права на запис."

#: wp-cache.php:218
msgid "Writeable:"
msgstr "Доступно для запису:"

#: wp-cache.php:219
msgid "Readonly:"
msgstr "Тільки читання:"

#: wp-cache.php:231
#, php-format
msgid "Warning! %s is writeable!"
msgstr "Увага! Папка %s доступна для запису!"

#: wp-cache.php:232
#, php-format
msgid "You should change the permissions on %s and make it more restrictive. Use your ftp client, or the following command to fix things:"
msgstr "Ви повинні змінити права на папку %s. Щоб зробити це - скористайтеся FTP-клієнтом або наступною командою:"

#: wp-cache.php:240
msgid "Mobile rewrite rules detected"
msgstr "Виявлена підтримка мобільних пристроїв"

#: wp-cache.php:241
msgid "For best performance you should enable \"Mobile device support\" or delete the mobile rewrite rules in your .htaccess. Look for the 2 lines with the text \"2.0\\ MMP|240x320\" and delete those."
msgstr "Для більш продуктивної роботи сайту, Вам варто включити опцію \"Підтримка мобільних пристроїв\" або видалити цей запис із правил ст. Htaccess. Знайдіть такий текст (дві строчки) \"2.0\\MMP|240x320\" і видаліть його."

#: wp-cache.php:241
msgid "This will have no affect on ordinary users but mobile users will see uncached pages."
msgstr "На Ваших звичайних відвідувачів це ніяк не відіб'ється, а ось використовують мобільні пристрої для веб-серфінгу не будуть отримувати кешовані сторінки (якщо опція відключена)."

#: wp-cache.php:247
#: wp-cache.php:258
msgid "Rewrite rules must be updated"
msgstr "Права на запис повинні бути оновлені"

#: wp-cache.php:248
#: wp-cache.php:259
msgid "The rewrite rules required by this plugin have changed or are missing. "
msgstr "Необхідні для роботи плагіна права були змінені або відсутні."

#: wp-cache.php:249
msgid "Mobile support requires extra rules in your .htaccess file, or you can set the plugin to legacy mode. Here are your options (in order of difficulty):"
msgstr "Підтримка блогом мобільних пристроїв вимагає присутності особливих правил у файлі. Htaccess, інакше Ви не зможете використовувати цю опцію. Нижче представлені можливий способи (в порядку складності):"

#: wp-cache.php:250
msgid "Set the plugin to legacy mode and enable mobile support."
msgstr "Переведіть плагін в режим середнього кешування і активуйте мобільних пристроїв."

#: wp-cache.php:251
#: wp-cache.php:260
msgid "Scroll down the Advanced Settings page and click the <strong>Update Mod_Rewrite Rules</strong> button."
msgstr "Прокрутіть сторінку вниз і натисніть кнопку <strong>Оновити правила Mod_Rewrite.</strong>."

#: wp-cache.php:252
#, php-format
msgid "Delete the plugin mod_rewrite rules in %s.htaccess enclosed by <code># BEGIN WPSuperCache</code> and <code># END WPSuperCache</code> and let the plugin regenerate them by reloading this page."
msgstr "Видаліть всі правила mod_rewrite з файлу %s.htaccess, між <code># BEGIN WPSuperCache</code> і <code># END WPSuperCache</code> і обновіть сторінку (відновлення правил запуститься автоматично)."

#: wp-cache.php:253
#, php-format
msgid "Add the rules yourself. Edit %s.htaccess and find the block of code enclosed by the lines <code># BEGIN WPSuperCache</code> and <code># END WPSuperCache</code>. There are two sections that look very similar. Just below the line <code>%%{HTTP:Cookie} !^.*(comment_author_|wordpress_logged_in|wp-postpass_).*$</code> add these lines: (do it twice, once for each section)"
msgstr "Додайте правила самостійно. Для цього відкрийте в редакторі файл %s.htaccess і знайдіть код між <code># BEGIN WPSuperCache</code> і <code># END WPSuperCache</code> . Ви побачите два схожих блоки коду. Відразу після рядка <code>%%{HTTP:Cookie} !^.*(comment_author_|wordpress_logged_in|wp-postpass_).*$</code> додайте наступне (таких рядків дві, додайте код і після другої):"

#: wp-cache.php:266
msgid "Required to serve compressed supercache files properly."
msgstr "Необхідно для правильного стиснення файлів."

#: wp-cache.php:266
msgid "Required to set caching information on supercache pages. IE7 users will see old pages without this module."
msgstr "Необхідно налаштувати кешування на сторінці плагіна. Користувачі браузера IE7 будуть бачити неоновленою сторінки з кеша без підтримки даного модуля."

#: wp-cache.php:266
msgid "Set the expiry date on supercached pages. Visitors may not see new pages when they refresh or leave comments without this module."
msgstr "Встановіть дату закінчення актуальності сторінок. Користувачі можуть не побачити нову версію кешовані сторінки без даного модуля."

#: wp-cache.php:273
msgid "Missing Apache Modules"
msgstr "Відсутні модулі Apache"

#: wp-cache.php:274
msgid "The following Apache modules are missing. The plugin will work in legacy mode without them. In full Supercache mode, your visitors may see corrupted pages or out of date content however."
msgstr "Наступні модулі Apache відсутні на Вашому сервері. Без них плагін зможе працювати тільки в режимі середнього кешування. У режимі з підтримкою Super Cache Ваші користувачі можуть бачити пошкоджені сторінки або прострочені їх копії."

#: wp-cache.php:413
msgid "<strong>Warning!</strong> You attempted to enable compression but <code>zlib.output_compression</code> is enabled. See #21 in the Troubleshooting section of the readme file."
msgstr "<strong>\"Увага!</strong> Ви намагаєтеся включити стиск, але модуль <code>zlib.output_compression</code> відключений. Дивіться пункт номер 21 розділу \"Опис проблем\" у файлі readme.txt (на англ.)"

#: wp-cache.php:501
msgid "WP Super Cache Settings"
msgstr "Установки WP Super Cache"

#: wp-cache.php:512
msgid "Notice: <em>Mod_rewrite or Legacy caching enabled</em>. Showing Advanced Settings Page by default."
msgstr "Важливо: <em>Mod_rewrite або Legacy caching відключені.</em> Типово буде завантажуватися сторінка \"Настройки\"."

#: wp-cache.php:523
msgid "Configuration file changed, some values might be wrong. Load the page again from the \"Settings\" menu to reset them."
msgstr "Файл конфігурацій був змінений, деякі настройки не відповідають вимогам плагіна. Відвідайте сторінку \"Настройки\" щоб виправити це."

#: wp-cache.php:540
#: wp-cache.php:614
msgid "Caching must be enabled to use this feature"
msgstr "Кешування повинно бути включено, якщо Ви хочете використовувати дану опцію"

#: wp-cache.php:542
msgid "Cache Tester"
msgstr "Перевірка кешування"

#: wp-cache.php:543
msgid "Test your cached website by clicking the test button below."
msgstr "Перевірте налаштування кешування свого блога натисканням однієї кнопки."

#: wp-cache.php:550
#, php-format
msgid "Fetching %s to prime cache: "
msgstr "Перевірка сторінки %s в кеші:"

#: wp-cache.php:552
#: wp-cache.php:559
#: wp-cache.php:566
msgid "OK"
msgstr "Є"

#: wp-cache.php:557
#, php-format
msgid "Fetching first copy of %s: "
msgstr "Перевірка першої копії %s:"

#: wp-cache.php:564
#, php-format
msgid "Fetching second copy of %s: "
msgstr "Перевірка другої копії %s:"

#: wp-cache.php:570
msgid "One or more page requests failed:"
msgstr "При роботі з кешем сталися помилки:"

#: wp-cache.php:589
#: wp-cache.php:590
#, php-format
msgid "Page %d: %d (%s)"
msgstr "Сторінка %d: %d (%s)"

#: wp-cache.php:596
#, php-format
msgid "Page 1: %s"
msgstr "Сторінка 1: %s"

#: wp-cache.php:597
#, php-format
msgid "Page 2: %s"
msgstr "Сторінка 2 :%s"

#: wp-cache.php:598
msgid "The timestamps on both pages match!"
msgstr "Тимчасові штампи обох варіантів сторінки збігаються!"

#: wp-cache.php:600
msgid "The pages do not match! Timestamps differ or were not found!"
msgstr "Сторінки не збігаються! Тимчасової штамп відрізняється або не було знайдено!"

#: wp-cache.php:606
msgid "Send non-secure (non https) request for homepage"
msgstr "Відправити незахищений (не по https) запит для головної сторінки"

#: wp-cache.php:607
msgid "Test Cache"
msgstr "Перевірити"

#: wp-cache.php:632
#: wp-cache.php:755
msgid "Cancel Cache Preload"
msgstr "Скасувати загальне кешування"

#: wp-cache.php:638
msgid "Scheduled preloading of cache cancelled. If a job is currently running it will not shutdown until the current 100 pages are complete."
msgstr "Загальне кешування за розкладом відключено. Якщо служба зараз запущена, вона не буде завершена поки поточні 100 сторінок Не кешувати."

#: wp-cache.php:649
msgid "Scheduled preloading of cache cancelled."
msgstr "Загальне кешування за розкладом відключено."

#: wp-cache.php:675
#: wp-cache.php:753
msgid "Preload Cache Now"
msgstr "Створити загальний кеш зараз"

#: wp-cache.php:678
msgid "Scheduled preloading of cache in 10 seconds."
msgstr "Загальне кешування відбудеться через 10 секунд."

#: wp-cache.php:682
#, php-format
msgid "Scheduled preloading of cache in %d minutes"
msgstr "Загальне кешування відбудеться через %d хвилин"

#: wp-cache.php:686
msgid "This will cache every published post and page on your site. It will create supercache static files so unknown visitors (including bots) will hit a cached page. This will probably help your Google ranking as they are using speed as a metric when judging websites now."
msgstr "При активації загального кешування, буде створено копію для кожного запису і сторінки Вашого блогу. В цьому режимі створюються статичні сторінки, які потім будуть бачити \"непізнані\" відвідувачі (наприклад, пошукові боти та інші). Це може допомогти в поліпшенні рівня PR Google, так як в основі їх алгоритму лежить і швидкість відгуку веб-сайту."

#: wp-cache.php:687
msgid "Preloading creates lots of files however. Caching is done from the newest post to the oldest so please consider only caching the newest if you have lots (10,000+) of posts. This is especially important on shared hosting."
msgstr "Проте, при використанні даного виду кешу створюється набагато більше файлів. Рекомендується використовувати такий тип, якщо у Вас дуже багато записів (10 тисяч і більше) на блозі. Особливо це важливо, якщо Ви користуєтеся послугами віртуального хостингу."

#: wp-cache.php:688
msgid "In &#8217;Preload Mode&#8217; regular garbage collection will only clean out old legacy files for known users, not the preloaded supercache files. This is a recommended setting when the cache is preloaded."
msgstr "В режимі &#8217;Загальних кешування&#8217; запланована очистка сміття буде видаляти тільки кеш записів простого типу (кеш з Super Cache не буде вилучено). Дана настройка рекомендована для використання."

#: wp-cache.php:692
#, php-format
msgid "Refresh preloaded cache files every %s minutes. (0 to disable, minimum %d minutes.)"
msgstr "Оновлювати загальний кеш кожні %s хвилин (введіть 0, щоб відключити, мінімальний допустимий кількість хвилин %d)."

#: wp-cache.php:705
msgid "all"
msgstr "Весь"

#: wp-cache.php:718
#, php-format
msgid "Preload %s posts."
msgstr "Кешувати %s записи."

#: wp-cache.php:725
msgid "Preload mode (garbage collection only on legacy cache files. Recommended.)"
msgstr "Попередній режим (очищення сміття працює не повністю, опція рекомендована до включення.)"

#: wp-cache.php:728
msgid "Send me status emails when files are refreshed."
msgstr "Відправляти мені повідомлення з повідомленнями про оновлення кешу."

#: wp-cache.php:733
msgid "Many emails, 2 emails per 100 posts."
msgstr "Високий рівень: 2 повідомлення на 100 записів."

#: wp-cache.php:736
msgid "Medium, 1 email per 100 posts."
msgstr "Середній рівень: 1 повідомлення на 100 записів."

#: wp-cache.php:739
msgid "Less emails, 1 at the start and 1 at the end of preloading all posts."
msgstr "Низький рівень: 1 e-mail на початку і 1 в кінці кешування."

#: wp-cache.php:743
#, php-format
msgid "Refresh of cache in %d hours %d minutes and %d seconds."
msgstr "Оновлення кешу через %d ч %d хв і %d сек."

#: wp-cache.php:744
#, php-format
msgid "Full refresh of cache in %d hours %d minutes and %d seconds."
msgstr "Повне кешування проіхойдет через %d ч %d хв і %d сек."

#: wp-cache.php:750
#, php-format
msgid "Currently caching from post %d to %d."
msgstr "В даний моменти кешуються записи з %d з %d."

#: wp-cache.php:753
msgid "Update Settings"
msgstr "Оновити настройки"

#: wp-cache.php:761
msgid "Preloading of cache disabled. Please disable legacy page caching or talk to your host administrator."
msgstr "Загальне кешування відключено. Будь повний режим роботи плагіна або зверніться до адміністратора сервера."

#: wp-cache.php:776
#: wp-cache.php:865
msgid "Caching"
msgstr "Статус кешування"

#: wp-cache.php:780
msgid "Cache hits to this website for quick access."
msgstr "Кешувати хіти сайту для прискорення доступу."

#: wp-cache.php:780
#: wp-cache.php:781
#: wp-cache.php:795
#: wp-cache.php:799
#: wp-cache.php:802
#: wp-cache.php:803
#: wp-cache.php:868
msgid "Recommended"
msgstr "Рекомендовано"

#: wp-cache.php:781
msgid "Use mod_rewrite to serve cache files."
msgstr "Використовувати mod_rewrite для обслуговування кешу."

#: wp-cache.php:782
msgid "Use PHP to serve cache files."
msgstr "Використовувати PHP для обслуговування кешу."

#: wp-cache.php:783
msgid "Legacy page caching."
msgstr "Спрощене кешування."

#: wp-cache.php:784
msgid "Mod_rewrite is fastest, PHP is almost as fast and easier to get working, while legacy caching is slower again, but more flexible and also easy to get working. New users should go with PHP caching."
msgstr "Використання mod_rewrite допоможе зробити роботу блога швидше, ніж PHP (хоча і цей варіант теж не такий повільний). Цей режим простіше і зручніше, але починаючому користувачеві краще використовувати кешування PHP."

#: wp-cache.php:790
msgid "Miscellaneous"
msgstr "Різне"

#: wp-cache.php:795
msgid "Compress pages so they&#8217;re served more quickly to visitors."
msgstr "Стискати файли кешу щоб прискорити роботу."

#: wp-cache.php:796
msgid "Compression is disabled by default because some hosts have problems with compressed files. Switching it on and off clears the cache."
msgstr "Стиснення кешу вимкнено за замовчуванням, так як може некоректно працювати на деяких хостингах. Включення або Відключення даної опції автоматично чистить тимчасовий кеш."

#: wp-cache.php:799
msgid "304 Not Modified browser caching. Indicate when a page has not been modified since last requested."
msgstr "Помилка 304. Дана помилка виникає тоді, коли сторінка не була змінена з часу минулого запиту."

#: wp-cache.php:800
msgid "304 support is disabled by default because in the past GoDaddy had problems with some of the headers used."
msgstr "Підтримка помилки 304 відключена за замовчуванням (через проблеми деяких сервісів)."

#: wp-cache.php:802
msgid "Don&#8217;t cache pages for <acronym title=\"Logged in users and those that comment\">known users</acronym>."
msgstr "Не кешувати сторінки для <acronym title=\"Зареєстрованих пользователей и авторов комментариев\">відомих користувачів</acronym>."

#: wp-cache.php:803
msgid "Cache rebuild. Serve a supercache file to anonymous users while a new file is being generated."
msgstr "Авто перебудова кеша. Гості блогу побачать застарілі версії сторінок кеша поки нові будуть генеруватися."

#: wp-cache.php:804
msgid "Proudly tell the world your server is Digg proof! (places a message in your blog&#8217;s footer)"
msgstr "Розповісти усім, що Ви користуєтеся плагіном WP Super Cache (повідомлення буде розміщено в \"підвалі\" сайту)!"

#: wp-cache.php:810
#: wp-cache.php:957
#: wp-cache.php:1473
msgid "Advanced"
msgstr "Настройки"

#: wp-cache.php:814
msgid "Mobile device support."
msgstr "Підтримка мобільних пристроїв"

#: wp-cache.php:815
msgid "Clear all cache files when a post or page is published."
msgstr "Очищати кеш коли новий запис або сторінка опублікована."

#: wp-cache.php:816
msgid "Only refresh current page when comments made."
msgstr "Оновлювати сторінку при додаванні нового коментаря до неї"

#: wp-cache.php:817
msgid "List the newest cached pages on this page."
msgstr "Створити список сторінок в кеші (виводиться на цій сторінці)"

#: wp-cache.php:819
msgid "Coarse file locking. You probably don&#8217;t need this but it may help if your server is underpowered. Warning! <em>May cause your server to lock up in very rare cases!</em>"
msgstr "Блокування файлів. Вам швидше за все не знадобиться ця опція, але вона може вирішити проблему малопотужних хостингів. Увага! <em>Включення опції, в окремих випадках, може викликати проблеми в роботі блогу!</em>"

#: wp-cache.php:822
msgid "Use object cache to store cached files."
msgstr "Використовувати об'єкти кеша для зберігання кешованих файлів."

#: wp-cache.php:822
msgid "(Experimental)"
msgstr "(Експериментальні установки)"

#: wp-cache.php:829
msgid "Note:"
msgstr "Важливо:"

#: wp-cache.php:831
#, php-format
msgid "If uninstalling this plugin, make sure the directory <em>%s</em> is writeable by the webserver so the files <em>advanced-cache.php</em> and <em>cache-config.php</em> can be deleted automatically. (Making sure those files are writeable too is probably a good idea!)"
msgstr "Деактивувавши плагін, переконайтеся що <em>папка %s</em> доступна для запису в настройках Вашого сервера, щоб файли плагіна <em>advanced-cache.php</em> і <em>cache-config.php</em> були вилучені в автоматичному режимі (також перевірте наявність прав на запис для цих двох файлів)."

#: wp-cache.php:832
#, php-format
msgid "Please see the <a href=\"%1$s/wp-super-cache/readme.txt\">readme.txt</a> for instructions on uninstalling this script. Look for the heading, \"How to uninstall WP Super Cache\"."
msgstr "Для довідки дивіться <a href=\"%1$s/wp-super-cache/readme.txt\">readme.txt</a> (документ на англ.). Там Ви знайдете корисну інформацію щодо використання та видалення плагіна WP Super Cache. "

#: wp-cache.php:833
#, php-format
msgid "Need help? Check the <a href=\"%1$s\">Super Cache readme file</a>. It includes installation documentation, a FAQ and Troubleshooting tips. The <a href=\"%2$s\">support forum</a> is also available. Your question may already have been answered."
msgstr "Все ще потрібна допомога? Дивіться офіційний <a href=\"%1$s\">ЧАВО по плагіну Super Cache</a> (англ.). Також, Ви можете задати своє питання на <a href=\"%2$s\">форумах тех. підтримки</a> (англ.), або ознайомитися з проблемами інших користувачів і запропонованими їм рішеннями. "

#: wp-cache.php:836
#: wp-cache.php:883
msgid "Update Status"
msgstr "Оновити"

#: wp-cache.php:843
msgid "Accepted Filenames &amp; Rejected URIs"
msgstr "Допустимі імена &amp; Заборонені адреси"

#: wp-cache.php:868
msgid "Caching On"
msgstr "Кешування включено"

#: wp-cache.php:869
msgid "Caching Off"
msgstr "Кешування вимкнено"

#: wp-cache.php:870
msgid "Note: enables PHP caching, cache rebuild, and mobile support"
msgstr "Важливо: рекомендується включити PHP кешування, авто перебудову кешу і підтримку для мобільних пристроїв"

#: wp-cache.php:880
msgid "Notice: Supercache mod_rewrite rules detected. Cached files will be served using those rules. Edit the .htaccess file in the root of your install and remove the SuperCache rules."
msgstr "Увага: Серед правил mod_rewrite виявлені правила від Supercache. Для роботи з кешем будуть використані саме ці правила. Щоб видалити їх, Вам потрібно вручну відредагувати файл. Htaccess."

#: wp-cache.php:892
msgid "Make WordPress Faster"
msgstr "Зроби свій блог швидше!"

#: wp-cache.php:893
#, php-format
msgid "%1$s is maintained and developed by %2$s with contributions from many others."
msgstr "Додаток %1$s створений і оновлюється автором %2$s за підтримки багатьох інших."

#: wp-cache.php:894
#, php-format
msgid "He blogs at %1$s and posts photos at %2$s."
msgstr "Відвідайте блог автора плагіна %1$s та його фотоблог на %2$s."

#: wp-cache.php:895
#, php-format
msgid "Please say hi to him on %s too!"
msgstr "Будь ласка, відвідайте %s автора!"

#: wp-cache.php:903
#, php-format
msgid "Cached pages since %1$s : <strong>%2$s</strong>"
msgstr "Сторінок в кеші з %1$s: <strong>%2$s</strong>"

#: wp-cache.php:904
msgid "Newest Cached Pages:"
msgstr "Нове в кеші:"

#: wp-cache.php:908
#, php-format
msgid "Cached %s seconds ago"
msgstr "Написано в кеш %s секунд назад"

#: wp-cache.php:911
msgid "(may not always be accurate on busy sites)"
msgstr "(Список не рекомендований для великих сайтів)"

#: wp-cache.php:930
msgid "Cache plugins are PHP scripts that live in a plugins folder inside the wp-super-cache folder. They are loaded when Supercache loads, much sooner than regular WordPress plugins."
msgstr "Кешування відбувається за рахунок PHP файлів, що зберігаються в папці плагіна. Вони завантажуються при включенні режиму Supercache. Завантаження відбувається набагато частіше, ніж для решти плагінів WordPress."

#: wp-cache.php:931
msgid "This is strictly an advanced feature only and knowledge of both PHP and WordPress actions is required to create them."
msgstr "Для використання даної опції потрібно знати не тільки PHP, але і WordPress (не на початковому рівні)."

#: wp-cache.php:941
msgid "Available Plugins"
msgstr "Доступні плагіни"

#: wp-cache.php:957
msgid "Easy"
msgstr "Кеш"

#: wp-cache.php:957
msgid "Tester & Contents"
msgstr "Перевірка"

#: wp-cache.php:957
msgid "Preload"
msgstr "Загальний кеш"

#: wp-cache.php:957
msgid "Plugins"
msgstr "Додатки"

#: wp-cache.php:957
msgid "Debug"
msgstr "Обслуговування"

#: wp-cache.php:984
#, php-format
msgid "Notice: WP Super Cache mod_rewrite rule checks disabled unless running on <a href=\"%s\">the main site</a> of this network."
msgstr "Увага: Перевірка правил WP Super Cache mod_rewrite відключена для блогів мережі поки не буде запущена <a href=\"%s\">на головному блозі</a>."

#: wp-cache.php:986
#, php-format
msgid "Notice: WP Super Cache mod_rewrite rule checks disabled unless running on <a href=\"%s\">on the main site</a> of this network."
msgstr "Увага: Перевірка правил WP Super Cache mod_rewrite відключена для блогів мережі поки не буде запущена <a href=\"%s\">на головному блозі</a>."

#: wp-cache.php:996
msgid "Mod Rewrite Rules"
msgstr "Модуль Mod Rewrite"

#: wp-cache.php:1002
msgid "WordPress MU Detected"
msgstr "Виявлено WordPress MU!"

#: wp-cache.php:1002
msgid "Unfortunately the rewrite rules cannot be updated automatically when running WordPress MU. Please open your .htaccess and add the following mod_rewrite rules above any other rules in that file."
msgstr "На жаль, mod_rewrite неможливо оновити в автоматичному режимі в WordPress MU. Вам доведеться вручну зробити це: відкрийте файл. Htaccess (розташований в кореневій директорії блога) і додайте наступні правила в нього."

#: wp-cache.php:1004
msgid "Mod Rewrite rules cannot be updated!"
msgstr "Mod Rewrite не може бути оновлений!"

#: wp-cache.php:1005
#, php-format
msgid "You must have <strong>BEGIN</strong> and <strong>END</strong> markers in %s.htaccess for the auto update to work. They look like this and surround the main WordPress mod_rewrite rules:"
msgstr "Для коректної роботи, у Вашому файлі %s.htaccess повинні бути прописані маркери <strong>BEGIN</strong> і <strong>END</strong>. Між ними знаходяться основні правила WordPress mod_rewrite, приклад:"

#: wp-cache.php:1007
msgid "Refresh this page when you have updated your .htaccess file."
msgstr "Оновлення сторінку коли закінчите редагування файлу.Htaccess."

#: wp-cache.php:1011
msgid "Thank you for upgrading."
msgstr "Спасибі за оновлення."

#: wp-cache.php:1011
#, php-format
msgid "The mod_rewrite rules changed since you last installed this plugin. Unfortunately you must remove the old supercache rules before the new ones are updated. Refresh this page when you have edited your .htaccess file. If you wish to manually upgrade, change the following line: %1$s so it looks like this: %2$s The only changes are \"HTTP_COOKIE\" becomes \"HTTP:Cookie\" and \"wordpressuser\" becomes \"wordpress\". This is a WordPress 2.5 change but it&#8217;s backwards compatible with older versions if you&#8217;re brave enough to use them."
msgstr "З моменту останньої встановленої версії плагіна правила mod_rewrite були змінені. На жаль, Вам доведеться вручну видалити старі правила перш ніж нові зароблять. Оновлення цю сторінку коли закінчите редагувати файл.Htaccess. Якщо Ви хочете повністю оновити все самі, то замініть наступний рядок: %1$s на цю: %2$s. Єдина відмінність - це заміна \"HTTP_COOKIE\" на \"HTTP: Cookie\" і \"wordpressuser\" на \"wordpress\". Ці зміни пов'язані з новою версією WordPress 2.5 . Звичайно Ви можете продовжувати працювати зі старими версіями, якщо вистачить сміливості;) "

#: wp-cache.php:1015
msgid "Trailing slash check required."
msgstr "Необхідна перевірка trailing slash."

#: wp-cache.php:1015
msgid "It looks like your blog has URLs that end with a \"/\". Unfortunately since you installed this plugin a duplicate content bug has been found where URLs not ending in a \"/\" end serve the same content as those with the \"/\" and do not redirect to the proper URL. To fix, you must edit your .htaccess file and add these two rules to the two groups of Super Cache rules:"
msgstr "Здається, всі посилання Вашого блогу закінчуються символом \"/\". На жаль, як тільки Ви встановили даний плагін, спрацювала опція, що виявила копії посилань без символу \"/\" на кінці, і тепер вона не може здійснити перенаправлення на вірний адресу. Щоб це виправити це, Ви повинні відредагувати файл.htaccess і додати два нових правила до групи основних правил плагіна Supercache:"

#: wp-cache.php:1017
msgid "You can see where the rules go and examine the complete rules by clicking the \"View mod_rewrite rules\" link below."
msgstr "Щоб подивитися повний список працюючих правил, натисніть кнопку нижче \"Переглянути правила mod_rewrite\"."

#: wp-cache.php:1031
msgid "Mod Rewrite rules updated!"
msgstr "Правила mod_rewrite оновлені!"

#: wp-cache.php:1032
#, php-format
msgid "%s.htaccess has been updated with the necessary mod_rewrite rules. Please verify they are correct. They should look like this:"
msgstr "Файл %s.htaccess був успішно оновлений, необхідні правила mod_rewrite були додані. Будь ласка, перевірте їх. Вони повинні виглядати так:"

#: wp-cache.php:1034
msgid "Mod Rewrite rules must be updated!"
msgstr "Правила mod_rewrite повинні бути оновлені!"

#: wp-cache.php:1035
#, php-format
msgid "Your %s.htaccess is not writable by the webserver and must be updated with the necessary mod_rewrite rules. The new rules go above the regular WordPress rules as shown in the code below:"
msgstr "Ваш файл %s.htaccess недоступний для запису (в настройках сервера) і не може бути оновлений в автоматичному режимі. Ви повинні вручну додати нові правила до основних правил WordPress так, як зазначено нижче:"

#: wp-cache.php:1040
#, php-format
msgid "WP Super Cache mod rewrite rules were detected in your %s.htaccess file.<br /> Click the following link to see the lines added to that file. If you have upgraded the plugin make sure these rules match."
msgstr "Правила mod_rewrite для плагіна WP Super Cache були виявлені у файлі %s.htaccess. <br /> Натисніть на відповідну кнопку, щоб подивитися строчки. Якщо Ви тільки що оновили плагін, перевірте всі правила. "

#: wp-cache.php:1043
msgid "A difference between the rules in your .htaccess file and the plugin rewrite rules has been found. This could be simple whitespace differences but you should compare the rules in the file with those below as soon as possible. Click the &#8217;Update Mod_Rewrite Rules&#8217; button to update the rules."
msgstr "Між правилами у Вашому файлі. Htaccess і правилами самого плагіна виявлені невідповідності. Це може бути просто зайвий символ у рядку, але Ви повинні особисто порівняти оригінальні правила зі своїми якомога швидше. Натисніть на кнопку &#8217;Оновити правила Mod_Rewrite&#8217;, щоб зробити це в автоматичному режимі."

#: wp-cache.php:1046
msgid "View Mod_Rewrite Rules"
msgstr "Подивитися правила mod_rewrite"

#: wp-cache.php:1050
#: wp-cache.php:2287
#, php-format
msgid "Rules must be added to %s too:"
msgstr "Правила повинні бути також додані до %s:"

#: wp-cache.php:1058
#, php-format
msgid "Gzip encoding rules in %s.htaccess created."
msgstr "Правила для Gzip-стиснення додані в файл %s.htaccess."

#: wp-cache.php:1065
msgid "Fix Configuration"
msgstr "Відновити стандартні настройки"

#: wp-cache.php:1068
msgid "Restore Default Configuration"
msgstr "Відновити налаштування за замовчуванням"

#: wp-cache.php:1076
msgid "Comment moderation is enabled. Your comment may take some time to appear."
msgstr "Перевірка коментарів включена. Перш ніж Ваші коментарі будуть опубліковані пройде якийсь час."

#: wp-cache.php:1101
msgid "Lock Down:"
msgstr "Закритий режим:"

#: wp-cache.php:1101
msgid "Disabled"
msgstr "Вимкнуто"

#: wp-cache.php:1101
msgid "Enabled"
msgstr "Включений"

#: wp-cache.php:1102
msgid "Prepare your server for an expected spike in traffic by enabling the lock down. When this is enabled, new comments on a post will not refresh the cached static files."
msgstr "Підготуйте свій блог до запланованих сплесків відвідуваності, активувавши даний режим. В активному стані розміщення нових коментарів і записів НЕ буде оновлювати статичні файли в кеші."

#: wp-cache.php:1103
msgid "Developers: Make your plugin lock down compatible by checking the \"WPLOCKDOWN\" constant. The following code will make sure your plugin respects the WPLOCKDOWN setting."
msgstr "Розробникам: Зробіть свій власний плагін закритого режиму сумісним з нашим, додавши змінну \"WPLOCKDOWN\". Наведений нижче код допоможе Вам адоптувати свій плагін до налаштувань нашого Режиму."

#: wp-cache.php:1105
msgid "Sorry. My blog is locked down. Updates will appear shortly"
msgstr "Вибачте, мій блог тимчасово недоступний. Зайдіть пізніше"

#: wp-cache.php:1109
msgid "WordPress is locked down. Super Cache static files will not be deleted when new comments are made."
msgstr "Ваш блог використовує Закритий режим. Нові коментарі не оновлюють всі статичні елементи кешу."

#: wp-cache.php:1111
msgid "WordPress is not locked down. New comments will refresh Super Cache static files as normal."
msgstr "Ваш блог не використовує Закритий режим. Останні коментарі оновлюють всі статичні елементи кешу."

#: wp-cache.php:1117
msgid "Lock Down"
msgstr "Закритий режим"

#: wp-cache.php:1125
msgid "Directly Cached Files"
msgstr "Пряме кешування сторінок"

#: wp-cache.php:1183
#, php-format
msgid "%s removed!"
msgstr "%s знищений!"

#: wp-cache.php:1191
#, php-format
msgid "You must make %s writable to enable this feature. As this is a security risk please make it readonly after your page is generated."
msgstr "Ви повинні дозволити запис для %s. Але так як це потенційна уразливість, після оновлення сторінки поверніть права на читання."

#: wp-cache.php:1193
#, php-format
msgid "%s is writable. Please make it readonly after your page is generated as this is a security risk."
msgstr "Діректрія %s доступна для запису. Вам слід змінити її права на \"Тільки читання\"."

#: wp-cache.php:1207
msgid "Existing direct page"
msgstr "Сторінка існує"

#: wp-cache.php:1207
msgid "Delete cached file"
msgstr "Видалити файли з кешу"

#: wp-cache.php:1212
msgid "Add direct page:"
msgstr "Додати сторінку в кеш:"

#: wp-cache.php:1214
#, php-format
msgid "Directly cached files are files created directly off %s where your blog lives. This feature is only useful if you are expecting a major Digg or Slashdot level of traffic to one post or page."
msgstr "Даний кеш створюється прямо з папки, де знаходиться Ваш блог (папка %s). Ця опція корисна тільки тоді, коли очікується великий приплив відвідувачів з більш популярного сайту на конкретну сторінку або запис Вашого."

#: wp-cache.php:1216
#, php-format
msgid "For example: to cache <em>%1$sabout/</em>, you would enter %1$sabout/ or /about/. The cached file will be generated the next time an anonymous user visits that page."
msgstr "Наприклад: щоб додати в кеш сторінку<em>%1$sabout/</em>, потрібно прописати %1$sabout/ або /about/. Кеш для сторінки буде створено відразу ж після першого до неї звернення гостем."

#: wp-cache.php:1217
msgid "Make the textbox blank to remove it from the list of direct pages and delete the cached file."
msgstr "Щоб видалити сторінку з такого кеша, очистіть текстове поле і видаліть файли кешу."

#: wp-cache.php:1222
msgid "Update Direct Pages"
msgstr "Оновити сторінки"

#: wp-cache.php:1260
msgid "Expiry Time &amp; Garbage Collection"
msgstr "Прострочені сторінки &amp; Очищення сміття"

#: wp-cache.php:1262
msgid "Expire time:"
msgstr "Актуальність кешу:"

#: wp-cache.php:1263
msgid "seconds"
msgstr "Секунд"

#: wp-cache.php:1264
msgid "Garbage Collection"
msgstr "Очищення сміття"

#: wp-cache.php:1264
msgid "If the expiry time is more than 1800 seconds (half an hour), garbage collection will be done every 10 minutes, otherwise it will happen 10 seconds after the expiry time above."
msgstr "Якщо час актуальності кешу більше 1800 секунд (півгодини), то очищення сміття буде запускатися кожні 10 хвилин."

#: wp-cache.php:1265
msgid "Checking for and deleting expired files is expensive, but it&#8217;s expensive leaving them there too. On a very busy site you should set the expiry time to <em>300 seconds</em>. Experiment with different values and visit this page to see how many expired files remain at different times during the day. If you are using legacy caching aim to have less than 500 cached files if possible. You can have many times more cached files when using mod_rewrite or PHP caching."
msgstr "Повірка та видалення прострочених сторінок кеша займає час, але й залишати такі файли в системі не варто. На великих блогах потрібно виставляти \"час життя\" кожної копії <em>300 секунд</em>. Ви можете поекспериментувати з налаштуванням і подивитися, скільки прострочених копій сторінок залишається в кеші протягом дня. Оптимальне число таких сторінок не більше 500. Використовуючи кешування з mod_rewrite або PHP, Ви зможете збільшити кількість кешируємой сторінок."

#: wp-cache.php:1266
msgid "Set the expiry time to 0 seconds to disable garbage collection."
msgstr "Встановіть значення на 0, щоб відключити Очищення сміття."

#: wp-cache.php:1267
msgid "Change Expiration"
msgstr "Змінити час життя копії"

#: wp-cache.php:1311
msgid "Rejected User Agents"
msgstr "Пошукові та інші боти"

#: wp-cache.php:1312
msgid "Strings in the HTTP &#8217;User Agent&#8217; header that prevent WP-Cache from caching bot, spiders, and crawlers&#8217; requests. Note that super cached files are still sent to these agents if they already exists."
msgstr "Щоб заборонити плагіну кешувати запити від пошукових ботів і інших мережевих роботів, введіть їх назви в поле нижче (по одному в рядку). Якщо копія сторінки вже існує в кеші Super Cache, то вона все одно буде відправлена боту."

#: wp-cache.php:1319
msgid "Save UA Strings"
msgstr "Зберегти налаштування"

#: wp-cache.php:1342
msgid "Do not cache the following page types. See the <a href=\"http://codex.wordpress.org/Conditional_Tags\">Conditional Tags</a> documentation for a complete discussion on each type."
msgstr "Не кешувати сторінки наступних типів. Перевірте <a href=\"http://codex.wordpress.org/Conditional_Tags\">Статтею Кодексу WP</a> (англ.), щоб дізнатися докладніше про типи сторінок."

#: wp-cache.php:1345
msgid "Single Posts"
msgstr "Записи"

#: wp-cache.php:1346
msgid "Pages"
msgstr "Сторінки"

#: wp-cache.php:1347
msgid "Front Page"
msgstr "Основна сторінка"

#: wp-cache.php:1348
msgid "Home"
msgstr "Домашня сторінка"

#: wp-cache.php:1349
msgid "Archives"
msgstr "Архіви"

#: wp-cache.php:1350
msgid "Tags"
msgstr "Мітки"

#: wp-cache.php:1351
msgid "Category"
msgstr "Рубрики"

#: wp-cache.php:1352
msgid "Feeds"
msgstr "Фіди"

#: wp-cache.php:1353
msgid "Search Pages"
msgstr "Сторінки пошуку"

#: wp-cache.php:1355
#: wp-cache.php:1483
msgid "Save"
msgstr "Зберегти"

#: wp-cache.php:1372
msgid "Add here strings (not a filename) that forces a page not to be cached. For example, if your URLs include year and you dont want to cache last year posts, it&#8217;s enough to specify the year, i.e. &#8217;/2004/&#8217;. WP-Cache will search if that string is part of the URI and if so, it will not cache that page."
msgstr "Поле нижче Ви також можете заповнити фрагментами адрес сторінок (або груп сторінок, наприклад, архів за рік/місяць і т.д.), які не будуть кешуватися. Плагін буде сканувати адреси сторінок на предмет наявності в них фрагментів, зазначених нижче, перед кешуванням. "

#: wp-cache.php:1378
msgid "Save Strings"
msgstr "Зберегти список заборонених фрагментів"

#: wp-cache.php:1394
msgid "Add here those filenames that can be cached, even if they match one of the rejected substring specified above."
msgstr "Поле нижче Ви можете заповнити назвами файлів які будуть кешованими, навіть якщо у їх назві є заборонених до кешуванню фрагменти."

#: wp-cache.php:1400
msgid "Save Files"
msgstr "Зберегти список файлів"

#: wp-cache.php:1445
msgid "Currently logging to: "
msgstr "Режим записаний на:"

#: wp-cache.php:1455
msgid "Fix problems with the plugin by debugging it here. It can send you debug emails or log them to a file in your cache directory."
msgstr "Тут можна вирішити деякі проблеми, що виникають при роботі з плагіном, за допомогою налагодження. Ви можете записувати службову інформацію (логи) у файли директорії кешу."

#: wp-cache.php:1456
msgid "Logging to a file is easier but faces the problem that clearing the cache will clear the log file."
msgstr "Отримати доступ до файлу лога просто, але при очищенні кешу лог також буде видалено."

#: wp-cache.php:1460
msgid "Debugging"
msgstr "Режим відлагодження"

#: wp-cache.php:1461
msgid "Logging Type"
msgstr "Тип входу"

#: wp-cache.php:1461
msgid "Email"
msgstr "Email"

#: wp-cache.php:1462
msgid "file"
msgstr "Файл"

#: wp-cache.php:1463
msgid "IP Address"
msgstr "Адреса IP"

#: wp-cache.php:1463
#, php-format
msgid "(only log requests from this IP address. Your IP is %s)"
msgstr "(Записує звернення тільки з цього IP. Ваш IP - %s)"

#: wp-cache.php:1464
msgid "Log level"
msgstr "Рівень логу"

#: wp-cache.php:1470
msgid "(1 = less, 5 = more, may cause severe server load.)"
msgstr "(1 = найменший, 5 = найбільший, може викликати болше навантаження на сервер)"

#: wp-cache.php:1473
msgid "In very rare cases two problems may arise on some blogs:<ol><li> The front page may start downloading as a zip file.</li><li> The wrong page is occasionally cached as the front page if your blog uses a static front page and the permalink structure is <em>/%category%/%postname%/</em>.</li></ol>"
msgstr "В особливо рідкісних випадках на блозі можуть статися дві проблеми: <ol><li> Замість головної сторінки користувачі будуть бачити пропозицію завантажити. Zip архів з нею </li><li> Замість головної сторінки може бути додана в кеш зовсім інша (тільки у випадку, якщо Ваш бог використовує наступну структуру посилань <em>/%category%/%postname%/</em>).</li></ol>"

#: wp-cache.php:1474
#, php-format
msgid "I&#8217;m 99% certain that they aren&#8217;t bugs in WP Super Cache and they only happen in very rare cases but you can run a simple check once every 5 minutes to verify that your site is ok if you&#8217;re worried. You will be emailed if there is a problem."
msgstr "II&#8217;m 99% certain! Помилки, які можуть виникнути при роботі з WP Super Cache, не пов'язані з самим плагіном. Вони виникають в окремих випадках, але Ви можете підстрахуватися, запустивши автоматичну перевірку блогу кожні 5 хвилин. У разі помилки, Вам буде надіслано повідомлення. "

#: wp-cache.php:1476
msgid "Check front page every 5 minutes."
msgstr "Перевіряти головну сторінку кожні 5 хвилин."

#: wp-cache.php:1477
msgid "Front page text"
msgstr "Ключовий текст"

#: wp-cache.php:1477
msgid "Text to search for on your front page. If this text is missing the cache will be cleared. Leave blank to disable."
msgstr "Введіть текст який плагін буде шукати на головній сторінці. Якщо текст не буде виявлений, то кеш автоматично очиститься. Залиште поле порожнім щоб відключити опцію."

#: wp-cache.php:1478
msgid "Clear cache on error."
msgstr "Очищати кеш при помильці."

#: wp-cache.php:1479
msgid "Email the blog admin when checks are made. (useful for testing)"
msgstr "Повідомити адміністратора блогу по завершенню перевірки (корисно при тестуванні на помилки)."

#: wp-cache.php:1493
msgid "Error: GZIP compression is enabled, disable it if you want to enable wp-cache."
msgstr "Помилка: стиснення GZIP увімкнено, вимкніть його якщо хочете запустити wp-cache."

#: wp-cache.php:1540
#: wp-cache.php:1702
msgid "Warning"
msgstr "Увага"

#: wp-cache.php:1540
msgid "GZIP compression is enabled in WordPress, wp-cache will be bypassed until you disable gzip compression."
msgstr "Стиснення GZIP включено в настройках WordPress, плагін не буде працювати до тих пір, поки Ви не відключите gzip."

#: wp-cache.php:1598
#: wp-cache.php:1603
#: wp-cache.php:1635
#: wp-cache.php:1640
#: wp-cache.php:1646
msgid "Error"
msgstr "Помилка"

#: wp-cache.php:1598
#, php-format
msgid "Your cache directory (<strong>$cache_path</strong>) did not exist and couldn&#8217;t be created by the web server. Check %s permissions."
msgstr "Папка <strong>($ cache_path)</strong> не існує і не може бути створена в автоматичному режимі. Перевірте права доступу до %s."

#: wp-cache.php:1603
#, php-format
msgid "Your cache directory (<strong>%1$s</strong>) or <strong>%2$s</strong> need to be writable for this plugin to work. Double-check it."
msgstr "Папка (<strong>%1$s</strong>) або <strong>%2$s</strong> повинна бути доступна для запису. Перевірте права доступу."

#: wp-cache.php:1635
#, php-format
msgid "Your WP-Cache config file (<strong>%s</strong>) is out of date and not writable by the Web server.Please delete it and refresh this page."
msgstr "Конфігураційний файл WP-Cache (<strong>%s</strong>) може бути недійсний і недоступний для запису. Будь ласка, видаліть його вручну і обновіть сторінку."

#: wp-cache.php:1640
#, php-format
msgid "Configuration file missing and %1$s  directory (<strong>%2$s</strong>) is not writable by the Web server.Check its permissions."
msgstr "Конфігураційний файл відсутній, а папка %1$s (<strong>%2$s</strong>) недоступна для запису. Перевірте права доступу."

#: wp-cache.php:1646
#, php-format
msgid "Sample WP-Cache config file (<strong>%s</strong>) does not exist.Verify you installation."
msgstr "Файл WP-Cache (<strong>%s</strong>) не був знайдений. Перевірте, завершили Ви установку плагіна."

#: wp-cache.php:1702
#, php-format
msgid "%s/advanced-cache.php</em> does not exist or cannot be updated."
msgstr "%s/advanced-cache.php</em> не існує або не може бути оновлений в автоматичному режимі."

#: wp-cache.php:1703
msgid "1. If it already exists please delete the file first."
msgstr "1. Якщо він вже існує в папках Вашого блога, видаліть його."

#: wp-cache.php:1704
#, php-format
msgid "2. Make %1$s writable using the chmod command through your ftp or server software. (<em>chmod 777 %1$s</em>) and refresh this page. This is only a temporary measure and you&#8217;ll have to make it read only afterwards again. (Change 777 to 755 in the previous command)"
msgstr "2. Змініть права доступу до %1$s, щоб можна було записати дані в автоматичному режимі (права повинні бути: <em>chmod 777 %1$s</em>) перевірте правильність заданих прав і обновіть сторінку. Це тимчасове умова, після оновлення сторінки права доступу потрібно повернути на колишні (з 777 на 755). "

#: wp-cache.php:1705
#, php-format
msgid "3. Refresh this page to update <em>%s/advanced-cache.php</em>"
msgstr "3. Оновлення сторінку, файл <em>%s/advanced-cache.php</em> оновиться сам"

#: wp-cache.php:1706
#, php-format
msgid "If that doesn&#8217;t work, make sure the file <em>%s/advanced-cache.php</em> doesn&#8217;t exist:"
msgstr "Якщо це не спрацювало, переконайтеся що <em>%s/advanced-cache.php</em> точно не існує:"

#: wp-cache.php:1707
#, php-format
msgid "<li>1. Open <em>%1$s$wp_cache_file</em> in a text editor.</li><li>2. Change the text <em>CACHEHOME</em> to <em>%2$s</em></li><li>3. Save the file and copy it to <em>%3$s</em> and refresh this page.</li>"
msgstr "<li>1. Відкрийте файл <em>%1$s$wp_cache_file</em> в текстовому редакторі. </li><li> 2. Змініть рядок <em>CACHEHOME</em> на <em>%2$s</em></li><li> 3. Збережіть файл і скопіюйте в <em>%3$s</em>, потім оновіть сторінку.</li>"

#: wp-cache.php:1730
msgid "<h3>WP_CACHE constant set to false</h3><p>The WP_CACHE constant is used by WordPress to load the code that serves cached pages. Unfortunately it is set to false. Please edit your wp-config.php and add or edit the following line above the final require_once command:<br /><br /><code>define('WP_CACHE', true);</code></p>"
msgstr " <h3>Параметр WP_CACHE не активований</h3><p>Даний параметр повинен бути активований для того, щоб WordPress міг коректно працювати з файлами кеша. В даний момент він відключений. Щоб включити його, відкрийте файл wp-config.php і додайте в нього наступний рядок:<br /><br /><code>define('WP_CACHE', true);</code></p>"

#: wp-cache.php:1732
msgid "<strong>Error: WP_CACHE is not enabled</strong> in your <code>wp-config.php</code> file and I couldn&#8217;t modify it."
msgstr "<strong>Помилка: Мінлива WP_CACHE не активована</strong> у файлі загальних конфігурацій <code>wp-config.php</code>."

#: wp-cache.php:1733
#, php-format
msgid "Edit <code>%s</code> and add the following line:<br /> <code>define('WP_CACHE', true);</code><br />Otherwise, <strong>WP-Cache will not be executed</strong> by WordPress core. "
msgstr "Відредагуйте код <code>%s</code> і додайте наступну строчку:<br /> <code>define('WP_CACHE', true);</code><br />Інакше, <strong>WP-Cache не зможе запуститися.</strong>."

#: wp-cache.php:1737
msgid "<h3>WP_CACHE constant added to wp-config.php</h3><p>If you continue to see this warning message please see point 5 of the <a href=\"http://wordpress.org/extend/plugins/wp-super-cache/faq/\">FAQ</a>. The WP_CACHE line must be moved up."
msgstr "<h3>Мінлива WP_CACHE добавлена в файл конфігурацій wp-config.php</h3><p>Якщо Ви досі бачите це повідомлення, то звіртеся з пунктом 5 <a href=\"http://wordpress.org/extend/plugins/wp-super-cache/faq/\">ЧАВО</a> (на англ.). Рядок із WP_CACHE повинна бути розміщена вище."

#: wp-cache.php:1759
msgid "Cache Contents"
msgstr "Стан кешу"

#: wp-cache.php:1762
msgid "Object cache in use. No cache listing available."
msgstr "Список кешування недоступний."

#: wp-cache.php:1786
#, php-format
msgid "Deleting supercache file: <strong>%s</strong><br />"
msgstr "Видалення файлів Super <strong>Cache:%s</strong> <br /> "

#: wp-cache.php:1803
#, php-format
msgid "Deleting wp-cache file: <strong>%s</strong><br />"
msgstr "Видалення файлів WP-Cache: <strong>%s</strong><br />"

#: wp-cache.php:1868
msgid "Cache stats are not automatically generated. You must click the link below to regenerate the stats on this page."
msgstr "Статистика об'єктів в кеші НЕ створюється автоматично. Щоб оновити її вручну натисніть на посилання нижче."

#: wp-cache.php:1869
msgid "Regenerate cache stats"
msgstr "Оновити статистику кеша"

#: wp-cache.php:1871
#, php-format
msgid "Cache stats last generated: %s minutes ago."
msgstr "Статистика об'єктів в кеші останній раз створено: %s мин назад."

#: wp-cache.php:1877
msgid "WP-Cache"
msgstr "Стан WP-Cache"

#: wp-cache.php:1878
#: wp-cache.php:1894
#, php-format
msgid "%s Cached Pages"
msgstr "%s сторінок в кеші"

#: wp-cache.php:1879
#: wp-cache.php:1899
#, php-format
msgid "%s Expired Pages"
msgstr "%s сторінок прострочено"

#: wp-cache.php:1893
msgid "WP-Super-Cache"
msgstr "Стан WP-Super-Cache"

#: wp-cache.php:1904
msgid "Fresh WP-Cached Files"
msgstr "Нові файли WP-Cache"

#: wp-cache.php:1905
#: wp-cache.php:1921
#: wp-cache.php:1937
#: wp-cache.php:1953
msgid "URI"
msgstr "АДРЕСА"

#: wp-cache.php:1905
#: wp-cache.php:1921
msgid "Key"
msgstr "Ключ"

#: wp-cache.php:1905
#: wp-cache.php:1921
#: wp-cache.php:1937
#: wp-cache.php:1953
msgid "Age"
msgstr "Вік"

#: wp-cache.php:1905
#: wp-cache.php:1921
#: wp-cache.php:1937
#: wp-cache.php:1953
msgid "Delete"
msgstr "Видалити"

#: wp-cache.php:1920
msgid "Stale WP-Cached Files"
msgstr "Старі файли WP-Cache"

#: wp-cache.php:1936
msgid "Fresh Super Cached Files"
msgstr "Нові файли Super Cache"

#: wp-cache.php:1952
msgid "Stale Super Cached Files"
msgstr "Старі файли Super Cache"

#: wp-cache.php:1968
msgid "Hide file list"
msgstr "Прибрати список"

#: wp-cache.php:1970
msgid "Too many cached files, no listing possible."
msgstr "Надто багато об'єктів в кеші, створення списку неможливо."

#: wp-cache.php:1972
msgid "List all cached files"
msgstr "Список кешованих файлів"

#: wp-cache.php:1978
#, php-format
msgid "<strong>Garbage Collection</strong><br />Last GC was <strong>%s</strong> minutes ago<br />"
msgstr "<strong>Очищення сміття</strong><br />Попереднє очищення було<strong>%s</strong> хвилин тому<br />"

#: wp-cache.php:1979
#, php-format
msgid "Next GC in <strong>%s</strong> minutes"
msgstr "Наступне очищення буде через<strong>%s</strong> хвилин"

#: wp-cache.php:1982
#, php-format
msgid "Expired files are files older than %s seconds. They are still used by the plugin and are deleted periodically."
msgstr "Прострочений кеш - це сторінки, створені більш ніж %s секунд тому. Не дивлячись на свій статус, вони використовуються в загальному кеші і періодично видаляються."

#: wp-cache.php:1993
msgid "Delete Expired"
msgstr "Видалити прострочений кеш"

#: wp-cache.php:1999
#: wp-cache.php:2011
#: wp-cache.php:2174
msgid "Delete Cache"
msgstr "Видалити весь кеш"

#: wp-cache.php:2011
msgid "Delete Super Cache cached files (opens in new window)"
msgstr "Видалити файли Super Cache (у новому вікні)"

#: wp-cache.php:2150
#, php-format
msgid "%1$s is Digg proof thanks to caching by %2$s"
msgstr "%1$s вдячний плагіну %2$s за проделаннную роботу"

#: wp-cache.php:2183
#, php-format
msgid "WP Super Cache must be configured. Go to <a href=\"%s\">the admin page</a> to enable and configure the plugin."
msgstr "Додаток WP Super Cache повинен бути налаштований! Щоб активувати його і налаштувати, перейдіть на <a href=\"%s\">сторінку управління</a> плагіном."

#: wp-cache.php:2189
msgid "Settings"
msgstr "Настройки"

#: wp-cache.php:2199
#, php-format
msgid "WP Super Cache is disabled. Please go to the <a href=\"%s\">plugin admin page</a> to enable caching."
msgstr "Додаток WP Super Cache не активовано. Щоб активувати його, перейдіть на <a href=\"%s\">сторінку управління</a> плагіном."

#: wp-cache.php:2218
#, php-format
msgid "[%s] Front page is gzipped! Please clear cache!"
msgstr "[%s] Головна сторінка стиснута за допомогою gzipped! Видаліть кеш!"

#: wp-cache.php:2218
#, php-format
msgid "Please visit %s to clear the cache as the front page of your site is now downloading!"
msgstr "Сталася помилка! Відвідайте %s щоб видалити кеш!"

#: wp-cache.php:2221
#, php-format
msgid "[%s] Front page is gzipped! Cache Cleared!"
msgstr "[%s] Головна сторінка стиснута за допомогою gzipped! Кеш знищений!"

#: wp-cache.php:2221
#, php-format
msgid "The cache on your blog has been cleared because the front page of your site is now downloading. Please visit %s to verify the cache has been cleared."
msgstr "Весь кеш для Вашого блогу був очищений, так як плагін помітив помилку на головній сторінці (вона Викачуємо архів). Для перевірки кеш, відвідайте цю стрніцу %s."

#: wp-cache.php:2228
#, php-format
msgid "[%s] Front page is not correct! Please clear cache!"
msgstr "[%s] Кеш головної сторінки містить помилку! Видаліть кеш!"

#: wp-cache.php:2228
#, php-format
msgid "Please visit %1$s to clear the cache as the front page of your site is not correct and missing the text, \"%2$s\"!"
msgstr "Сталася помилка, на головній сторінці Вашого блогу не був виявлений ключовою текст \"%2$s\"! Щоб виправити помилку, відвідайте цю сторінку %1$s і уручну очистити кеш."

#: wp-cache.php:2231
#, php-format
msgid "[%s] Front page is not correct! Cache Cleared!"
msgstr "[%s] Головна сторінка відображається з помилкою! Кеш знищений!"

#: wp-cache.php:2231
#, php-format
msgid "The cache on your blog has been cleared because the front page of your site is missing the text \"%2$s\". Please visit %1$s to verify the cache has been cleared."
msgstr "Сталася помилка, на головній сторінці Вашого блогу не був виявлений ключовою текст \"%2$s\"! Відвідайте цю сторінку %1$s щоб перевірити кеш."

#: wp-cache.php:2236
#, php-format
msgid "[%s] Front page check!"
msgstr "[%s] Головна сторінка перевірена!"

#: wp-cache.php:2236
#, php-format
msgid "WP Super Cache has checked the front page of your blog. Please visit %s if you would like to disable this."
msgstr "Додаток WP Super Cache щойно перевірив головну сторінку Вашого блогу на предмет помилок. Щоб відключити повідомлення та перевірку відвідайте цю сторінку %s."

#: wp-cache.php:2279
msgid "Cannot update .htaccess"
msgstr "Не можу оновити файл.Htaccess"

#: wp-cache.php:2279
#, php-format
msgid "The file <code>%s.htaccess</code> cannot be modified by the web server. Please correct this using the chmod command or your ftp client."
msgstr "Файл <code>%s.htaccess</code> не може бути оновлений в автоматичному режимі. Щоб виправити це задайте необхідні для запису права доступу до нього. "

#: wp-cache.php:2279
msgid "Refresh this page when the file permissions have been modified."
msgstr "Оновлення сторінку коли необхідні права доступу будуть задані."

#: wp-cache.php:2279
#, php-format
msgid "Alternatively, you can edit your <code>%s.htaccess</code> file manually and add the following code (before any WordPress rules):"
msgstr "Ви також можете редагувати файл <code>%s.htaccess</code> вручну і особисто додати необхідні правила (до наявних там правил WordPress): "

#: wp-cache.php:2283
#, php-format
msgid "To serve static html files your server must have the correct mod_rewrite rules added to a file called <code>%s.htaccess</code>"
msgstr "Щоб плагін міг правильно працювати з статичними html файлами, Ви повинні додати в файл <code>%s.htaccess</code> правила модуля mod_rewrite "

#: wp-cache.php:2284
msgid "You can edit the file yourself add the following rules."
msgstr "Ви можете вручну відредагувати файл і додати необхідні правила."

#: wp-cache.php:2285
msgid " Make sure they appear before any existing WordPress rules. "
msgstr "Перевірте, що вони йдуть перед всіма вже існуючими в документі правилами WordPress."

#: wp-cache.php:2293
msgid "Update Mod_Rewrite Rules"
msgstr "Оновити правила mod_rewrite"

#: wp-cache.php:2389
#, php-format
msgid "[%1$s] Cache Preload Started"
msgstr "[%1$s] Загальне кешування почалося"

#: wp-cache.php:2391
#, php-format
msgid "[%1$s] Refreshing posts from %2$d to %3$d"
msgstr "[%1$s] Оновлення кеша записів з %2$d до %3$d"

#: wp-cache.php:2402
#, php-format
msgid "[%1$s] %2$d posts refreshed"
msgstr "[%1$s] %2$d кеш записів оновлений"

#: wp-cache.php:2402
msgid "Refreshed the following posts:"
msgstr "Наступні записи оновлені:"

#: wp-cache.php:2411
#, php-format
msgid "Scheduling next preload refresh in %d minutes."
msgstr "Наступне заплановане оновлення загального кешу через %d хвилин."

#: wp-cache.php:2421
#, php-format
msgid "[%s] Cache Preload Completed"
msgstr "[%s] Загальне кешування завершено"

#: wp-cache.php:2421
msgid "Cleaning up old supercache files."
msgstr "Видаляти старі файли з кешу Super Cache."

#: wp-cache.php:2462
#, php-format
msgid "[%s] Preload may have stalled."
msgstr "[%s] Загальнe кешування могло бути перервано."

#: wp-cache.php:2462
#, php-format
msgid ""
"Preload has been restarted.\n"
"%s"
msgstr ""
"Загальне кешування було перезапущенe. \n"
"%s"

#~ msgid ""
#~ "Scroll down this page and click the <strong>Update Mod_Rewrite Rules</"
#~ "strong> button."
#~ msgstr ""
#~ "Пролистайте страницу вних и нажмите кнопку <strong>Обновить правила "
#~ "Mod_Rewrite</strong>."

#~ msgid "WP Super Cache Manager"
#~ msgstr "Управление плагином WP Super Cache"

#~ msgid "Why your configuration may not be changed"
#~ msgstr "Почему конфигурации не могут быть изменены"

#~ msgid "Why"
#~ msgstr "Почему"

#~ msgid "ON"
#~ msgstr "ВКЛ"

#~ msgid "WP Cache and Super Cache enabled"
#~ msgstr "Плагины WP Cache и Super Cache включены"

#~ msgid "HALF ON"
#~ msgstr "РАБОТАЕТ НАПОЛОВИНУ"

#~ msgid "Super Cache Disabled, only legacy WP-Cache caching."
#~ msgstr "Плагин Super Cache отключен, работает только кэшировани WP-Cache"

#~ msgid "OFF"
#~ msgstr "ВЫКЛ"

#~ msgid "WP Cache and Super Cache disabled"
#~ msgstr "Плагины WP Cache и Super Cache отключены"

#~ msgid ""
#~ "Cache rebuild. Serve a supercache file to anonymous users while a new "
#~ "file is being generated. Recommended for <em>very</em> busy websites with "
#~ "lots of comments. Makes \"directly cached pages\" and \"Lockdown mode\" "
#~ "obsolete."
#~ msgstr ""
#~ "Восстановление кэша. При включенной опции, гости сайта будут получать "
#~ "старые страницы из плагина Super Cache, пока формируются новые. "
#~ "Рекомендуется использовать данную опцию только для <em>очень</em> "
#~ "посещаемых блогов, где много комментариев. После включения опции в "
#~ "настройках \"прямое кэширование страниц\" и \"закрытый режим\" нет нужды "
#~ "(они устаревают)"

#~ msgid ""
#~ "List the newest cached pages (may be expensive to run on busy sites, use "
#~ "with caution.)"
#~ msgstr ""
#~ "Создавать список новых кэшированных страниц (использовать на свой страх и "
#~ "риск, может затормозить сайты с большим количеством посетителей)"

#~ msgid ""
#~ "%1$s really makes your blog go faster. Make it go faster<sup>*</sup> by "
#~ "buying me an <a href=\"%2$s\">Amazon gift card</a>! Make it out to \"%3$s"
#~ "\" for whatever amount you want. Every penny helps!"
#~ msgstr ""
#~ "%1$s действительно ускоряет Ваш блог. Вы можете сделать его еще "
#~ "быстрее<sup>*,</sup> купив мне <a href=\"%2$s\">подарочную карту сервиса "
#~ "Amazon</a>! Можете выбрать \"%3$s\" любое значение, какое хотите. Каждая "
#~ "копейка поможет мне!"

#~ msgid ""
#~ "If Amazon isn&#8217;t your thing, there&#8217;s also PayPal. Click the "
#~ "\"Donate\" button below or take a quick peek at my <a href=\"%s"
#~ "\">wishlist</a>."
#~ msgstr ""
#~ "Если Вы не хотите иметь дело с Amazon , то можно сделать пожертвование "
#~ "через PayPal. Нажмите кнопку \"Пожертвовать\" или выберите подарок для "
#~ "меня из моего <a href=\"%s\">списка желаний</a>."

#~ msgid "Thanks in advance!"
#~ msgstr "Заранее благодарю!"

#~ msgid ""
#~ "Ok, it won&#8217;t go any faster but you&#8217;ll make this plugin author "
#~ "very happy!"
#~ msgstr ""
#~ "Ладно, он не будет быстрее, но Вы все равно сделаете автора плагина очень "
#~ "счастливым!"

#~ msgid "Don&#8217;t show me this again."
#~ msgstr "Не показывать больше это сообщение."

#~ msgid "Hide"
#~ msgstr "Спрятать"

#~ msgid ""
#~ "He blogs at %1$s and posts photos at %2$s. He would really appreciate a "
#~ "<a href=\"%3$s\">donation</a> to encourage development of this plugin."
#~ "<br />Even a penny will help."
#~ msgstr ""
#~ "Блог автора - %1$s, фотоблог - %2$s. Автор плагина будет благодарен любой "
#~ "<a href=\"%3$s\">поддержке</a> от Вас.<br />Даже копейка может помочь в "
#~ "его работе."

#~ msgid "Super Cache Compression"
#~ msgstr "Сжатие кэша Super Cache"

#~ msgid "Compression is enabled by default when in <em>HALF ON</em> mode."
#~ msgstr ""
#~ "Сжатие кэша отключено по умолчанию в режиме <em>РАБОТАЕТ НАПОЛОВИНУ</em>."

#~ msgid "Super Cache compression is now disabled."
#~ msgstr "Сжатие кэша Super Cache теперь отключено."

#~ msgid "Super Cache compression is now enabled."
#~ msgstr "Сжатие кэша Super Cache теперь включено."

#~ msgid "Update Compression"
#~ msgstr "Обновить настройки сжатия"

#~ msgid "Debug Settings"
#~ msgstr "Параметры отладки"

#~ msgid ""
#~ "<h3>WP_CACHE constant in wp-config.php set to false</h3><p>The WP_CACHE "
#~ "constant is defined in your wp-config.php but must be set to true, not "
#~ "false for this plugin to work correctly.</p>"
#~ msgstr ""
#~ "<h3>Значение для WP_CACHE в конфигурационном файле WordPrees wp-config."
#~ "php установлено на \"false\"</h3><p>Чтобы плагин заработал, значение "
#~ "WP_CACHE в wp-config.php нужно поставить на \"true\".</p>"

#~ msgid "You must edit the file yourself add the following rules."
#~ msgstr ""
#~ "Вы должны вручную отредактировать файл и добавить необходимые правила."
