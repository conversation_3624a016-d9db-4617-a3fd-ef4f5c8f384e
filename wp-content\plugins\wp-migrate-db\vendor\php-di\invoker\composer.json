{"name": "php-di/invoker", "description": "Generic and extensible callable invoker", "keywords": ["invoker", "dependency-injection", "dependency", "injection", "callable", "invoke"], "homepage": "https://github.com/PHP-DI/Invoker", "license": "MIT", "type": "library", "autoload": {"psr-4": {"DeliciousBrains\\WPMDB\\Container\\Invoker\\": "src/"}}, "autoload-dev": {"psr-4": {"DeliciousBrains\\WPMDB\\Container\\Invoker\\Test\\": "tests/"}}, "require": {"container-interop/container-interop": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.5", "athletic/athletic": "~0.1.8"}}