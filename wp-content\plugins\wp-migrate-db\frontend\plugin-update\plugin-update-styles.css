.check-licence-spinner {
  left: 5px;
  position: relative;
  top: 2px;
  margin-bottom: -2px;
  width: 16px;
  height: 16px;
}

.wpmdb-original-update-row {
  display: none;
}

.plugin-update-tr.wpmdbpro-custom-visible .update-message.pre-shiny-updates,
.plugin-update-tr.wpmdbpro-custom .update-message.pre-shiny-updates {
  padding-left: 40px;
}
.plugin-update-tr.wpmdbpro-custom-visible .update-message.pre-shiny-updates::before,
.plugin-update-tr.wpmdbpro-custom .update-message.pre-shiny-updates::before {
  margin-left: -30px;
  float: left;
}
.plugin-update-tr.wpmdbpro-custom-visible .update-message.pre-shiny-updates p,
.plugin-update-tr.wpmdbpro-custom .update-message.pre-shiny-updates p {
  display: inline-block;
  margin: 0;
}
.plugin-update-tr.wpmdbpro-custom-visible .update-message.pre-shiny-updates span,
.plugin-update-tr.wpmdbpro-custom .update-message.pre-shiny-updates span {
  display: block;
}
.plugin-update-tr.wpmdbpro-custom-visible .update-message.post-shiny-updates p::before,
.plugin-update-tr.wpmdbpro-custom .update-message.post-shiny-updates p::before {
  position: absolute;
}
.plugin-update-tr.wpmdbpro-custom.legacy-addon .update-message p::before {
  content: "\f182";
}
.plugin-update-tr.wpmdbpro-custom-visible .update-message.post-shiny-updates p span,
.plugin-update-tr.wpmdbpro-custom .update-message.post-shiny-updates p span {
  margin-left: 30px;
  display: block;
}

.plugins #the-list tr.wpmdbpro-has-message td,
.plugins #the-list tr.wpmdbpro-has-message th {
  box-shadow: none;
  -webkit-box-shadow: none;
}

.plugins .plugin-update-tr .wpmdb-compat-plugin-row-error .notice {
  margin-top: 0;
}

/*# sourceMappingURL=plugin-update-styles.css.map */
